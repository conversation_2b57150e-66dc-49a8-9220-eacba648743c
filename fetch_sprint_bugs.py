#!/usr/bin/env python3
"""
Script to fetch bugs from the current sprint using Jira REST API
"""

import requests
import json
import base64
from datetime import datetime

# Jira configuration
JIRA_BASE_URL = "https://nexdor-tech.atlassian.net"
EMAIL = "<EMAIL>"
API_TOKEN = "ATATT3xFfGF0akRu_z5r5SZ9cW7_KpVrvN1MchICQ4r5EelfVx7kpk_POurmMolNiIFmNVUFOXgOaGT0M3KqoJf0xkGK1YLuQMstIGir1738FPKaylzS3Uaa5heJVAy9Ek6uBFLoBeCxpj15gcsq3GZ8HV8EQu9o4-bvBjVCL2XFzvvVyMz4J50=0DA68A00"

def create_auth_header():
    """Create basic auth header for Jira API"""
    credentials = f"{EMAIL}:{API_TOKEN}"
    encoded_credentials = base64.b64encode(credentials.encode()).decode()
    return f"Basic {encoded_credentials}"

def fetch_sprint_bugs():
    """Fetch all bugs from the current sprint"""
    
    # JQL query to get bugs from current sprint
    jql_query = "sprint in openSprints() AND issuetype = Bug"
    
    # API endpoint
    url = f"{JIRA_BASE_URL}/rest/api/3/search"
    
    # Headers
    headers = {
        "Authorization": create_auth_header(),
        "Accept": "application/json",
        "Content-Type": "application/json"
    }
    
    # Parameters
    params = {
        "jql": jql_query,
        "maxResults": 100,  # Adjust as needed
        "fields": "key,summary,status,priority,assignee,created,updated,description,labels,components"
    }
    
    try:
        print(f"Fetching bugs from current sprint...")
        print(f"JQL Query: {jql_query}")
        print("-" * 50)
        
        response = requests.get(url, headers=headers, params=params)
        response.raise_for_status()
        
        data = response.json()
        issues = data.get("issues", [])
        total = data.get("total", 0)
        
        print(f"Found {total} bugs in current sprint:")
        print("=" * 80)
        
        if not issues:
            print("No bugs found in the current sprint.")
            return
        
        for i, issue in enumerate(issues, 1):
            key = issue["key"]
            summary = issue["fields"]["summary"]
            status = issue["fields"]["status"]["name"]
            priority = issue["fields"]["priority"]["name"] if issue["fields"]["priority"] else "None"
            assignee = issue["fields"]["assignee"]["displayName"] if issue["fields"]["assignee"] else "Unassigned"
            created = issue["fields"]["created"]
            updated = issue["fields"]["updated"]
            
            # Format dates
            created_date = datetime.fromisoformat(created.replace('Z', '+00:00')).strftime('%Y-%m-%d %H:%M')
            updated_date = datetime.fromisoformat(updated.replace('Z', '+00:00')).strftime('%Y-%m-%d %H:%M')
            
            print(f"{i}. {key}: {summary}")
            print(f"   Status: {status}")
            print(f"   Priority: {priority}")
            print(f"   Assignee: {assignee}")
            print(f"   Created: {created_date}")
            print(f"   Updated: {updated_date}")
            print(f"   URL: {JIRA_BASE_URL}/browse/{key}")
            print("-" * 80)
        
        # Summary
        print(f"\nSummary:")
        print(f"Total bugs in current sprint: {total}")
        
        # Group by status
        status_counts = {}
        for issue in issues:
            status = issue["fields"]["status"]["name"]
            status_counts[status] = status_counts.get(status, 0) + 1
        
        print(f"\nBy Status:")
        for status, count in status_counts.items():
            print(f"  {status}: {count}")
        
        # Group by priority
        priority_counts = {}
        for issue in issues:
            priority = issue["fields"]["priority"]["name"] if issue["fields"]["priority"] else "None"
            priority_counts[priority] = priority_counts.get(priority, 0) + 1
        
        print(f"\nBy Priority:")
        for priority, count in priority_counts.items():
            print(f"  {priority}: {count}")
        
        # Group by assignee
        assignee_counts = {}
        for issue in issues:
            assignee = issue["fields"]["assignee"]["displayName"] if issue["fields"]["assignee"] else "Unassigned"
            assignee_counts[assignee] = assignee_counts.get(assignee, 0) + 1
        
        print(f"\nBy Assignee:")
        for assignee, count in assignee_counts.items():
            print(f"  {assignee}: {count}")
            
    except requests.exceptions.RequestException as e:
        print(f"Error fetching data from Jira: {e}")
        if hasattr(e, 'response') and e.response is not None:
            print(f"Response status: {e.response.status_code}")
            print(f"Response text: {e.response.text}")
    except Exception as e:
        print(f"Unexpected error: {e}")

if __name__ == "__main__":
    fetch_sprint_bugs()
