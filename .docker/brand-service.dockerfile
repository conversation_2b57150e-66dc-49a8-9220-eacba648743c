FROM golang:1.23.0-alpine AS builder

WORKDIR /app

COPY ./golang-services/go.mod .
COPY ./golang-services/go.sum .
COPY ./golang-services/localize.yml .
RUN go mod download

COPY ./golang-services .

RUN cd brand-service && go build -o brand-service .

FROM alpine:3.12
RUN apk add --no-cache ca-certificates tzdata
ENV TZ=Asia/Ho_Chi_Minh
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && \
   echo $TZ > /etc/timezone
WORKDIR /app
COPY --from=builder /app/brand-service/brand-service .
COPY --from=builder /app/localize.yml .