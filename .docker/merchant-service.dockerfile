FROM golang:1.23.0-alpine AS builder

WORKDIR /app

COPY ./golang-services/go.mod .
COPY ./golang-services/go.sum .
RUN go mod download

COPY ./golang-services .

RUN cd merchant-service && go build -o merchant-service .

FROM alpine:3.12
RUN apk add --no-cache ca-certificates tzdata
ENV TZ=Asia/Ho_Chi_Minh
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && \
   echo $TZ > /etc/timezone
WORKDIR /app
COPY --from=builder /app/merchant-service/merchant-service .