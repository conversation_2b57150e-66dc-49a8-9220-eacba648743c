# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Architecture Overview

NexPOS is a sophisticated Point-of-Sale and restaurant management platform with a hybrid microservices architecture designed for multi-brand, multi-site restaurant operations with extensive third-party integrations.

### Service Architecture

**Golang Services (Core Business Logic)**
- `brand-service`: Brand management, menu templates, site management, core products, billing
- `order-service`: Order processing, cart management, payments, shipments, vouchers  
- `user-service`: Authentication, user management, subscriptions, notifications
- `merchant-service`: Third-party merchant integrations and cron jobs
- `notification-service`: Real-time notifications via WebSockets, text-to-speech
- `email-service`: Email notifications and templates
- `stock-service`: Inventory management
- `nexdorpay-service`: Payment processing with banking integration
- `printer-service`: POS printer management (ESC/POS protocol)
- `migration-service`: Database migration management
- `cron-service`: Scheduled tasks and background jobs
- `zalo-mini-app-service`: Zalo platform integration

**Node.js Services (Legacy & Specialized)**
- `nexpos-service`: Main legacy service with extensive business logic
- `gateway-service`: MongoDB to PostgreSQL data migration
- `subscribe-service`: Brand subscription management for delivery platforms
- `shopee-service`, `shopee-otp-service`: Shopee platform integration
- `browser-service`: Browser automation for merchant platforms
- `nexpos-ai-service`: AI chatbot integration (Facebook, Zalo)
- `odoo-service`: ERP integration with custom Odoo modules
- `zalo-chat-service`: Zalo messaging integration

### Technology Stack

- **Go 1.23**: Microservices with Gin framework, GORM ORM, Google Wire DI
- **Node.js**: Express.js services with MongoDB/Mongoose
- **Databases**: PostgreSQL (primary), MongoDB (legacy), Redis (caching)
- **Message Queue**: RabbitMQ for async communication
- **Infrastructure**: Docker, Kubernetes, Google Cloud Platform

### Key Business Domains

- **Multi-Platform Restaurant Management**: Brands, sites, hubs, staff
- **Menu & Product Management**: Core products, menu templates, multi-channel publishing
- **Order Management**: Multi-source orders, payments, stock tracking
- **Third-party Integrations**: Delivery platforms (Grab, Shopee, Be), payment gateways, delivery services

## Development Commands

### Building and Testing

**Go Services:**
```bash
# Vet all Go services
make vet

# Build specific service binary (from service directory)
cd golang-services/brand-service && go build

# Run tests (from service directory)  
cd golang-services/brand-service && go test ./...

# Generate Wire dependency injection (from service directory)
cd golang-services/brand-service && wire
```

**Node.js Services:**
```bash
# Main nexpos service
cd nodejs-services/nexpos-service
npm run local     # Development with nodemon
npm run dev       # Development mode
npm run build     # Webpack build
npm run start     # Production

# ESLint
npm run lint      # (if available)
```

### Docker & Deployment

**Service-specific builds:**
```bash
# Build and push specific service
make build-brand-image
make build-order-image
make build-user-image

# Build and deploy to environments
make deploy-brand-image-dev
make deploy-dev          # Deploy nexpos to dev
make deploy-stag         # Deploy to staging  
make deploy-prod         # Deploy to production
```

**Database Operations:**
```bash
# Dump production to development
make dump_prod_to_dev

# Deploy Odoo modules
make deploy-odoo-dev
make deploy-odoo-prod
```

### Environment Profiles
```bash
# Switch Kubernetes contexts
make prod-profile    # Production cluster
make uat-profile     # UAT cluster
```

## Code Patterns & Conventions

### Go Services Structure

- **Dependency Injection**: All services use Google Wire with `di/wire.go` and auto-generated `wire_gen.go`
- **Router Pattern**: Routes defined in `router/` directory with domain-specific handlers
- **Model Layer**: Shared models in `golang-services/sdk/models/` with BaseModel pattern
- **Middleware**: Common middleware in `golang-services/sdk/middlewares/`

### Database Patterns

- **BaseModel**: All entities extend BaseModel with ObjectID (string), CreatedAt, UpdatedAt
- **Custom ObjectID**: String-based IDs instead of auto-increment integers
- **Multi-tenant**: Brand/site-based data isolation using BrandID, SiteID fields

### Service Communication

- **RabbitMQ**: Async events for order processing, notifications, stock updates
- **REST APIs**: Synchronous service-to-service communication
- **WebSockets**: Real-time notifications and order updates

### Third-party Integration Patterns

- **IMerchant Interface**: Standardized interface in `golang-services/sdk/merchant/` for all delivery platforms
- **Platform SDKs**: Individual implementations for Grab, Shopee, Be, TikTok, etc.
- **Payment Abstraction**: Unified payment flow in `golang-services/sdk/payment/` supporting MoMo, VNPay, PayOS

### Localization

- **i18n Support**: Localization files in `golang-services/localize.yml` (Vietnamese/English)
- **Error Messages**: Centralized error message localization

## Important Development Notes

- **Wire Generation**: After modifying DI in Go services, run `wire` from the service directory
- **Multi-environment**: Code supports dev, staging, and production environments
- **Database Migration**: Use migration-service for schema changes
- **Testing**: Each Go service should have tests; run from individual service directories
- **ESLint**: Node.js services use ESLint with custom configuration
- **Kubernetes**: Deployment configurations in `.k8s/` directory