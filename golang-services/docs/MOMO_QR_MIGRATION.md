# MOMO QR Code Migration from Node.js to Golang

## Overview

This document describes the migration of the MOMO QR code extraction functionality from Node.js to Golang SDK.

## Original Node.js Implementation

The original Node.js function `get_momo_qr_code_buffer` in `nodejs-services/.shared/payment/momo/index.js`:

```javascript
momo.get_momo_qr_code_buffer = async (pay_url) => {
    const resp = await axios.get(pay_url)
    const $ = cheerio.load(resp.data);

    if ($('svg').length === 0) {
        return null;
    }
    const image_b64 = $('.image-qr-code').attr('src').replace('data:image/png;base64,', '')
    const buffer = Buffer.from(image_b64, 'base64')

    const image_hash = crypto.createHash('md5').update(buffer).digest('hex')
    const file = await upload_file({
        bucket: 'nexpos-files',
        key: `momo_qr/${image_hash}.jpg`,
        buff: buffer
    })
    return file;
}
```

## Migrated Golang Implementation

The equivalent Golang function `GetMomoQRCodeBuffer` in `golang-services/sdk/payment/momo.go`:

```go
func (c *MomoClient) GetMomoQRCodeBuffer(payURL string) (string, error) {
    // Make HTTP GET request to the payment URL
    resp, err := c.HTTPClient.R().Get(payURL)
    if err != nil {
        return "", fmt.Errorf("failed to fetch MOMO payment page: %w", err)
    }

    // Parse HTML response using goquery (equivalent to cheerio in Node.js)
    doc, err := goquery.NewDocumentFromReader(strings.NewReader(string(resp.Body())))
    if err != nil {
        return "", fmt.Errorf("failed to parse HTML: %w", err)
    }

    // Check if SVG element exists
    if doc.Find("svg").Length() == 0 {
        return "", fmt.Errorf("no SVG QR code found on the page")
    }

    // Extract base64 image data from .image-qr-code element
    imageSrc, exists := doc.Find(".image-qr-code").Attr("src")
    if !exists {
        return "", fmt.Errorf("QR code image not found")
    }

    // Remove the data URL prefix
    const base64Prefix = "data:image/png;base64,"
    if !strings.HasPrefix(imageSrc, base64Prefix) {
        return "", fmt.Errorf("invalid image format, expected base64 PNG")
    }
    imageB64 := strings.TrimPrefix(imageSrc, base64Prefix)

    // Decode base64 to buffer
    buffer, err := base64.StdEncoding.DecodeString(imageB64)
    if err != nil {
        return "", fmt.Errorf("failed to decode base64 image: %w", err)
    }

    // Generate MD5 hash of the buffer
    hash := md5.Sum(buffer)
    imageHash := hex.EncodeToString(hash[:])

    // Upload file to Google Cloud Storage using SDK utility
    fileURL, err := utils.UploadFile("nexpos-files", fmt.Sprintf("momo_qr/%s.jpg", imageHash), buffer)
    if err != nil {
        return "", fmt.Errorf("failed to upload QR code image: %w", err)
    }

    return fileURL, nil
}
```

## Integration with CreatePaymentLink

The function is automatically called in `CreatePaymentLink` when MOMO doesn't provide a QR code URL:

```go
// If QRCodeURL is not provided by MOMO but PayURL exists, extract QR code
// This implements the equivalent of: if (!payment_link.qrCodeUrl && payment_link?.pay_url)
if response.QRCode == "" && response.PayURL != "" {
    qrCodeURL, err := c.GetMomoQRCodeBuffer(response.PayURL)
    if err != nil {
        // Log the error but don't fail the payment creation
        fmt.Printf("Warning: Failed to extract QR code from MOMO payment URL: %v\n", err)
    } else {
        response.QRCode = qrCodeURL
    }
}
```

## Dependencies Added

- `github.com/PuerkitoBio/goquery v1.10.1` - HTML parsing (equivalent to cheerio)
- Standard library packages: `crypto/md5`, `encoding/base64`

## Key Differences

1. **Error Handling**: Golang version provides detailed error messages instead of returning null
2. **Type Safety**: Golang provides compile-time type checking
3. **Integration**: Automatically integrated into the payment creation flow
4. **Logging**: Uses fmt.Printf for warnings (can be replaced with proper logging)

## Usage Example

```go
// Create MOMO client
momoClient := payment.NewMomoClient()

// Create payment (QR code will be automatically extracted if needed)
response, err := momoClient.CreatePaymentLink(token, request)
if err != nil {
    return err
}

// QR code URL is available in response.QRCode
fmt.Printf("QR Code URL: %s\n", response.QRCode)

// Or manually extract QR code from any MOMO payment URL
qrCodeURL, err := momoClient.GetMomoQRCodeBuffer(paymentURL)
```

## Testing

The implementation has been tested for:
- ✅ Compilation without errors
- ✅ Dependency resolution
- ✅ Integration with existing payment flow

## Migration Status

- ✅ Function migrated from Node.js to Golang
- ✅ Integrated into CreatePaymentLink method
- ✅ Dependencies added and resolved
- ✅ Example code provided
- ✅ Documentation created
