package router

import (
	"fmt"
	"net/http"
	"sort"
	"time"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/token"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/utils"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/middlewares"

	"github.com/gin-gonic/gin"
)

// PosShipmentRequest defines the request structure for getting shipment options
type PosShipmentRequest struct {
	SiteID          string `json:"site_id"`
	CustomerAddress struct {
		Lat  float64 `json:"lat"`
		Long float64 `json:"long"`
		Text string  `json:"text"`
	} `json:"customer_address"`
	OrderItems []struct {
		Code     string  `json:"code"`
		Quantity int     `json:"quantity"`
		Price    float64 `json:"price"`
	} `json:"order_items"`
}

// GetShipment handles the request to get shipment options for POS
func GetShipment(c *gin.Context) {
	// Get the database from middleware
	db := middlewares.GetDB(c)

	// Parse request body
	var req PosShipmentRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Invalid request: " + err.Error(),
		})
		return
	}

	// Validate site_id
	if req.SiteID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "site_id is required",
		})
		return
	}

	// Fetch site information
	var site models.Site
	if err := db.Where("id = ?", req.SiteID).First(&site).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "site_not_found",
		})
		return
	}

	if req.CustomerAddress.Text == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "customer_address is required",
		})
		return
	}

	// Fetch hub information
	var hub models.Hub
	if err := db.Where("id = ?", site.HubID).First(&hub).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "hub_not_found",
		})
		return
	}

	// Initialize result object with different shipment categories
	result := map[string][]map[string]interface{}{
		"instant_ship":  {},
		"same_day_ship": {},
		"two_hour_ship": {},
		"schedule_ship": {},
		"province_ship": {},
	}

	// Create shipment request object
	shipmentReq := &models.GetShipmentsRequest{
		From: models.Location{
			Name:    hub.Name,
			Phone:   hub.Phone,
			Address: hub.Address,
		},
		To: models.Location{
			Address: req.CustomerAddress.Text,
			Name:    "",
			Phone:   "",
		},
	}

	// Get tokens for different providers with proper error handling
	ahamoveToken, ahamoveErr := token.GetTokenBySite(db, site, "ahamove")
	if ahamoveErr != nil {
		fmt.Printf("Error fetching Ahamove token: %v\n", ahamoveErr)
	}

	grabExpressToken, grabErr := token.GetTokenBySite(db, site, "grab_express")
	if grabErr != nil {
		fmt.Printf("Error fetching Grab Express token: %v\n", grabErr)
	}

	// Create a delivery provider factory
	providerFactory := NewDeliveryProviderFactory()

	// Get providers through the factory
	ahamoveProvider := providerFactory.GetProvider("ahamove")
	grabExpressProvider := providerFactory.GetProvider("grab_express")

	// Fetch shipment options from all providers
	var ahamoveShipments, grabExpressShipments []models.ShipmentOption

	// Get shipments from each provider if token is available
	if ahamoveProvider != nil && ahamoveToken != nil {
		ahamoveShipments, _ = ahamoveProvider.GetShipments(&models.ShipToken{AccessToken: ahamoveToken.AccessToken}, shipmentReq)
	}

	if grabExpressProvider != nil && grabExpressToken != nil {
		grabExpressShipments, _ = grabExpressProvider.GetShipments(&models.ShipToken{AccessToken: grabExpressToken.AccessToken}, shipmentReq)
	}

	// Categorize shipment options

	// Add grab express shipments to instant_ship category
	for _, shipment := range grabExpressShipments {
		if shipment.Code == "INSTANT" {
			result["instant_ship"] = append(result["instant_ship"], shipmentToMap(shipment))
		}
	}

	// If no instant_ship options from grab, try ahamove
	if len(result["instant_ship"]) == 0 {
		for _, shipment := range ahamoveShipments {
			if shipment.Code == "SGN-BIKE" || shipment.Code == "SGN-ECO" {
				result["instant_ship"] = append(result["instant_ship"], shipmentToMap(shipment))
			}
		}
	}

	// Add ahamove shipments to two_hour_ship category
	for _, shipment := range ahamoveShipments {
		if shipment.Code == "SGN-BIKE" || shipment.Code == "SGN-ECO" {
			result["two_hour_ship"] = append(result["two_hour_ship"], shipmentToMap(shipment))
		}
	}

	// If no two_hour_ship options from ahamove, try grab express
	if len(result["two_hour_ship"]) == 0 {
		for _, shipment := range grabExpressShipments {
			if shipment.Code == "INSTANT" {
				result["two_hour_ship"] = append(result["two_hour_ship"], shipmentToMap(shipment))
			}
		}
	}

	// Filter and sort shipment options

	// Sort instant_ship by price and filter those with price > 0
	result["instant_ship"] = filterAndSortShipments(result["instant_ship"], 1)

	// Sort two_hour_ship by price and filter those with price > 0
	twoHourShipments := filterAndSortShipments(result["two_hour_ship"], 1)

	// If two_hour_ship has options, generate schedule shipments
	if len(twoHourShipments) > 0 {
		choiceSlot := twoHourShipments[0]
		slots := generateShipmentSlots()

		for _, slot := range slots {
			vendor, _ := choiceSlot["vendor"].(string)
			code, _ := choiceSlot["code"].(string)
			price, _ := choiceSlot["price"].(float64)
			options, _ := choiceSlot["options"].([]interface{})
			raw, _ := choiceSlot["raw"].(map[string]interface{})

			// Format date for display
			// fromDateTime, _ := slot.FromDateTime.MarshalJSON()
			// fromDateTimeStr := string(fromDateTime)
			formattedDate := time.Time(slot.FromDateTime).Format("02/01") // DD/MM format

			// Create schedule shipment option
			scheduleShipment := map[string]interface{}{
				"vendor":      vendor,
				"code":        code,
				"price":       price,
				"name":        fmt.Sprintf("Khung giờ %s - %s , ngày %s", slot.FromTime, slot.ToTime, formattedDate),
				"description": "Shop sẽ liên hệ và giao hàng theo lịch hẹn của bạn",
				"options":     options,
				"raw":         map[string]interface{}{},
			}

			// Copy raw data
			if raw != nil {
				for k, v := range raw {
					scheduleShipment["raw"].(map[string]interface{})[k] = v
				}
			}

			// Add schedule information
			scheduleShipment["raw"].(map[string]interface{})["schedule"] = slot

			result["schedule_ship"] = append(result["schedule_ship"], scheduleShipment)
		}
	}

	// Remove the two_hour_ship category as per JS implementation
	delete(result, "two_hour_ship")

	// Check if current time is outside operating hours (before 8am or after 9pm)
	// Use the safe time utilities to prevent panic from nil location
	currentTime := utils.CurrentTimeIn("Asia/Ho_Chi_Minh")

	// Use LoadLocationSafe for timezone to avoid nil pointer panic
	loc := utils.LoadLocationSafe("Asia/Ho_Chi_Minh")

	beforeTime := time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day(), 8, 0, 0, 0, loc)
	afterTime := time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day(), 21, 0, 0, 0, loc)

	if currentTime.Before(beforeTime) || currentTime.After(afterTime) {
		result["instant_ship"] = []map[string]interface{}{} // Clear instant_ship options
	}

	// Ensure all shipment categories are initialized as empty arrays if they're nil
	for key := range result {
		if result[key] == nil {
			result[key] = []map[string]interface{}{}
		}
	}

	// Return the result
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    result,
	})
}

// shipmentToMap converts a ShipmentOption to a map for response
func shipmentToMap(shipment models.ShipmentOption) map[string]interface{} {
	return map[string]interface{}{
		"vendor":      shipment.Vendor,
		"code":        shipment.Code,
		"name":        shipment.Name,
		"description": shipment.Description,
		"price":       shipment.Price,
		"options":     shipment.ExtraServices,
		"raw":         shipment.Raw,
	}
}

// filterAndSortShipments sorts shipments by price and limits the number of results
func filterAndSortShipments(shipments []map[string]interface{}, limit int) []map[string]interface{} {
	// Filter out shipments with price <= 0
	var filtered []map[string]interface{}
	for _, s := range shipments {
		if price, ok := s["price"].(float64); ok && price > 0 {
			filtered = append(filtered, s)
		}
	}

	// Sort shipments by price (ascending)
	sort.Slice(filtered, func(i, j int) bool {
		price1, _ := filtered[i]["price"].(float64)
		price2, _ := filtered[j]["price"].(float64)
		return price1 < price2
	})

	// Limit the number of results
	if len(filtered) > limit {
		filtered = filtered[:limit]
	}

	return filtered
}

// PosPickupSlotsRequest defines the request structure for getting pickup time slots
type PosPickupSlotsRequest struct {
	CustomerPhone string `json:"customer_phone"`
}

// ShipmentService defines the structure for shipment service details.
// This should ideally be defined in a shared models or constants package.
type ShipmentService struct {
	Vendor  string        `json:"vendor"`
	Code    string        `json:"code"`
	Price   float64       `json:"price"`
	Options []interface{} `json:"options"` // Using interface{} for flexibility, define specific struct if possible
	Raw     interface{}   `json:"raw"`     // Using interface{} for flexibility, define specific struct if possible
}

// SELF_PICKUP defines the constant for self-pickup service.
// Replace with actual configuration values, potentially loaded from config or a constants file.
var SELF_PICKUP = ShipmentService{
	Vendor:  "self_pickup", // Example value
	Code:    "SELF_PICKUP", // Example value
	Price:   0,             // Example value
	Options: []interface{}{},
	Raw:     map[string]interface{}{}, // Example value
}

// TimeSlot defines the structure for a raw time slot interval.
type TimeSlot struct {
	Date         string    `json:"date"`
	FromTime     string    `json:"from_time"`
	FromDateTime time.Time `json:"from_date_time"`
	ToTime       string    `json:"to_time"`
	ToDateTime   time.Time `json:"to_date_time"`
	IsActive     bool      `json:"is_active"`
}

// PickupSlot defines the structure for the final pickup slot output format.
type PickupSlot struct {
	Vendor      string        `json:"vendor"`
	Code        string        `json:"code"`
	Price       float64       `json:"price"`
	Name        string        `json:"name"`
	Description string        `json:"description"`
	Options     []interface{} `json:"options"`
	Raw         interface{}   `json:"raw"` // Includes the original TimeSlot under "schedule" key
}

// generateShipmentSlots generates available shipment time slots for the next few days.
// This logic might belong in a dedicated service or utility package.
func generateShipmentSlots() []TimeSlot {
	// Define the number of days ahead to generate slots for
	maxDays := 3
	// Initialize an empty slice to hold the generated slots
	slots := []TimeSlot{}
	// Define the location for time zone consistency (e.g., Asia/Ho_Chi_Minh)
	// TODO: Make timezone configurable or determine dynamically
	loc, err := time.LoadLocation("Asia/Ho_Chi_Minh")
	if err != nil {
		// Log the error and default to UTC if the specified location cannot be loaded
		fmt.Println("Error loading location, defaulting to UTC:", err) // Consider using a proper logger
		loc = time.UTC
	}

	// Calculate the earliest start time for slots (current time + 1 hour)
	currentTime := time.Now().In(loc).Add(1 * time.Hour)

	// Iterate through the specified number of days
	for i := 0; i < maxDays; i++ {
		// Get the date for the current iteration day
		currentDate := time.Now().In(loc).AddDate(0, 0, i)
		year, month, day := currentDate.Date()

		// Define the start hour (e.g., 9 AM) for the operational window
		startHour := 9
		// Define the end hour (e.g., 9 PM) for the operational window
		endHour := 21

		// Calculate the absolute start time for slots on this day
		startTime := time.Date(year, month, day, startHour, 0, 0, 0, loc)
		// Calculate the absolute end time for slots on this day
		endTime := time.Date(year, month, day, endHour, 0, 0, 0, loc)

		// Adjust the start time for the first day if it's before the calculated earliest start time
		if startTime.Before(currentTime) {
			// Round the current time up to the start of the next hour
			startTime = currentTime.Truncate(time.Hour)
			if currentTime.Minute() > 0 || currentTime.Second() > 0 || currentTime.Nanosecond() > 0 {
				startTime = startTime.Add(1 * time.Hour)
			}
		}

		// Generate slots in one-hour increments within the operational window
		for slotStart := startTime; slotStart.Before(endTime); slotStart = slotStart.Add(1 * time.Hour) {
			// Calculate the end time for this specific slot
			slotEnd := slotStart.Add(1 * time.Hour)
			// Ensure the generated slot doesn't extend beyond the overall end time for the day
			if slotEnd.After(endTime) {
				break // Stop generating slots for this day if the next slot would exceed the end time
			}

			// Create the TimeSlot object
			slot := TimeSlot{
				Date:         slotStart.Format("2006-01-02"), // Format date as YYYY-MM-DD
				FromTime:     slotStart.Format("15:04"),      // Format time as HH:MM (24-hour)
				FromDateTime: slotStart,                      // Store the actual time.Time object
				ToTime:       slotEnd.Format("15:04"),        // Format time as HH:MM (24-hour)
				ToDateTime:   slotEnd,                        // Store the actual time.Time object
				IsActive:     true,                           // Mark the slot as active by default
			}
			// Append the newly created slot to the list
			slots = append(slots, slot)
		}
	}
	// Return the list of generated slots
	return slots
}

// generatePickupSlots generates pickup slots based on the available shipment slots.
// This function transforms the raw TimeSlot data into the desired PickupSlot format.
func generatePickupSlots() []PickupSlot {
	// Get the base shipment time slots
	shipmentSlots := generateShipmentSlots()
	// Pre-allocate slice capacity for efficiency
	result := make([]PickupSlot, 0, len(shipmentSlots))

	// Iterate through the generated shipment slots
	for _, v := range shipmentSlots {
		// Create the raw data map to be included in the PickupSlot
		// It includes the original TimeSlot data under the "schedule" key
		rawMap := map[string]interface{}{
			"schedule": v, // Embed the original TimeSlot struct
		}
		// Merge original Raw data from SELF_PICKUP if it's a map[string]interface{}
		if originalRaw, ok := SELF_PICKUP.Raw.(map[string]interface{}); ok {
			for key, val := range originalRaw {
				// Avoid overwriting the "schedule" key we just added
				if key != "schedule" {
					rawMap[key] = val
				}
			}
		}

		// Create the PickupSlot object using data from the TimeSlot and SELF_PICKUP constant
		pickupSlot := PickupSlot{
			Vendor: SELF_PICKUP.Vendor,
			Code:   SELF_PICKUP.Code,
			Price:  SELF_PICKUP.Price,
			// Format the name string including time range and date (DD/MM format)
			Name:        fmt.Sprintf("Khung giờ %s - %s , ngày %s", v.FromTime, v.ToTime, v.FromDateTime.Format("02/01")),
			Description: "Vui lòng đến cửa hàng để nhận hàng vào khung giờ này", // Static description for pickup
			Options:     SELF_PICKUP.Options,                                    // Use options defined in SELF_PICKUP
			Raw:         rawMap,                                                 // Assign the combined raw data map
		}
		// Append the formatted pickup slot to the result list
		result = append(result, pickupSlot)
	}

	// Return the list of formatted pickup slots
	return result
}

// GetPickupSlots handles the HTTP request to get available pickup time slots.
func GetPickupSlots(c *gin.Context) {
	// Get site ID from URL parameter
	// siteID := c.Param("site_id")
	// Attempt to convert site ID to integer, handle potential error
	// _, err := strconv.Atoi(siteID) // Use blank identifier as siteIDInt is not used after validation
	// if err != nil {
	// 	// Return bad request error if site ID is not a valid integer
	// 	c.JSON(http.StatusBadRequest, gin.H{
	// 		"success": false,
	// 		"error":   "Invalid site ID format",
	// 	})
	// 	return
	// }

	// Note: The request body binding `PosPickupSlotsRequest` is removed as it's not used
	// based on the original JS code which didn't seem to use `customer_phone`.
	// If `customer_phone` or other request data is needed for slot generation logic
	// (e.g., filtering based on user), re-add the binding and pass data accordingly.
	/*
		var req PosPickupSlotsRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   err.Error(),
			})
			return
		}
	*/

	// Call the internal function to generate the pickup slots
	pickupSlots := generatePickupSlots() // Changed from mock data to call the generation function

	// Return the generated pickup slots in the response
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		// "site_id": siteIDInt, // Removed as siteIDInt is not defined if conversion isn't assigned
		"data": pickupSlots, // Changed key from "pickup_slots" to "data" to match JS response
	})
}
