# Order Payment Status API

## Endpoint
`GET /v1/order-service/sites/:site_id/user/orders/:order_id/payment_status`

## Description
Returns the latest payment status for a specific order. This endpoint retrieves the most recent OrderPayment record associated with the given order ID.

## Authentication
Requires authentication token in the Authorization header.

## Parameters

### Path Parameters
- `site_id` (string): The site identifier
- `order_id` (string): The order identifier

## Response Format

### Success Response (200 OK)

#### When payment record exists:
```json
{
  "success": true,
  "data": {
    "order_id": "ORDER_123456",
    "transaction_id": "TXN_789012",
    "partner_transaction_id": "PARTNER_TXN_345678",
    "vendor": "vnpay",
    "amount": 150000,
    "currency": "VND",
    "status": "COMPLETED",
    "description": "Payment completed successfully",
    "created_at": "2025-05-29T10:30:00Z",
    "updated_at": "2025-05-29T10:35:00Z"
  }
}
```

#### When no payment record exists:
```json
{
  "success": true,
  "data": {
    "order_id": "ORDER_123456",
    "status": "NO_PAYMENT",
    "message": "No payment record found for this order"
  }
}
```

### Error Responses

#### Order Not Found (404 Not Found)
```json
{
  "success": false,
  "error_code": "order_not_found",
  "error_message": "Order not found"
}
```

#### Database Error (400 Bad Request)
```json
{
  "success": false,
  "error_code": "database_error",
  "error_message": "Database connection error"
}
```

## Payment Status Values
- `PENDING`: Payment is being processed
- `COMPLETED`: Payment was successful
- `CANCELLED`: Payment was cancelled
- `NO_PAYMENT`: No payment record found for this order

## Supported Payment Vendors
- `momo`: MoMo payment gateway
- `vnpay`: VNPay payment gateway
- `nexdorpay`: NexDorPay payment gateway

## Usage Examples

### cURL Example
```bash
curl -X GET "https://api.example.com/v1/order-service/sites/SITE_123/user/orders/ORDER_456/payment_status" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json"
```

### JavaScript Example
```javascript
const response = await fetch('/v1/order-service/sites/SITE_123/user/orders/ORDER_456/payment_status', {
  method: 'GET',
  headers: {
    'Authorization': 'Bearer YOUR_ACCESS_TOKEN',
    'Content-Type': 'application/json'
  }
});

const data = await response.json();
if (data.success) {
  console.log('Payment status:', data.data.status);
} else {
  console.error('Error:', data.error_message);
}
```

## Notes
- The endpoint returns the **latest** payment record based on the `created_at` timestamp
- If multiple payment attempts exist for an order, only the most recent one is returned
- The endpoint requires proper authentication and user permissions
- All timestamps are returned in ISO 8601 format
