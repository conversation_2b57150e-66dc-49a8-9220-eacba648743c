package main

import (
	"encoding/json"
	"fmt"
	"log"
	"os"
	"time"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/mapping"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// Example demonstrating how to use the MapOrder function
func main() {
	fmt.Println("=== MapOrder Function Example ===")
	fmt.Println()

	// Example 1: Test MapOrder with sample data
	fmt.Println("1. Testing MapOrder with sample Shopee order data:")
	testMapOrderWithSampleData()
	fmt.Println()

	// Example 2: Test MapOrder with different sources
	fmt.Println("2. Testing MapOrder with different order sources:")
	testMapOrderWithDifferentSources()
	fmt.Println()

	// Example 3: Query and re-map orders from database (if available)
	fmt.Println("3. Testing MapOrder with real database orders:")
	testMapOrderWithDatabaseOrders()
	fmt.Println()

	fmt.Println("=== Example completed ===")
}

// testMapOrderWithSampleData demonstrates MapOrder with sample Shopee data
func testMapOrderWithSampleData() {
	// Sample Shopee order data
	shopeeOrderData := map[string]interface{}{
		"order_time":         1640995200, // Unix timestamp for 2022-01-01 10:00:00
		"actual_pick_time":   1640998800, // Unix timestamp for 2022-01-01 11:00:00
		"delivery_time_unix": 1641002400, // Unix timestamp for 2022-01-01 12:00:00
		"order_value_amount": 250000.0,   // 250,000 VND
		"commission": map[string]interface{}{
			"amount": 25000.0, // 25,000 VND commission
		},
		"total_value_amount": 225000.0, // 225,000 VND after commission
		"assignee": map[string]interface{}{
			"name":  "Nguyen Van A",
			"phone": "+84901234567",
		},
		"order_user": map[string]interface{}{
			"name":  "Tran Thi B",
			"phone": "+84987654321",
		},
		"deliver_address": map[string]interface{}{
			"contact_name": "Tran Thi B",
			"address":      "123 Nguyen Hue Street, District 1, Ho Chi Minh City",
		},
		"notes": map[string]interface{}{
			"order_note": "Please deliver to the 5th floor",
		},
		"is_remove_plastic": true,
		"dishes": []interface{}{
			map[string]interface{}{
				"name":     "Pho Bo",
				"quantity": 2,
				"price":    80000.0,
			},
			map[string]interface{}{
				"name":     "Banh Mi",
				"quantity": 1,
				"price":    25000.0,
			},
		},
	}

	// Create MerchantOrder
	merchantOrder := &models.MerchantOrder{
		LongOrderID:  "SHOPEE_20240101_123456789",
		ShortOrderID: "SP123456",
		Source:       "shopee_food",
		DataInDetail: shopeeOrderData,
		Status:       "PENDING",
	}

	// Map the order
	mappedOrder := mapping.MapOrder(merchantOrder)

	// Display results
	fmt.Printf("  Original Order ID: %s\n", merchantOrder.LongOrderID)
	fmt.Printf("  Mapped Order ID: %s\n", mappedOrder.OrderID)
	fmt.Printf("  Source: %s\n", mappedOrder.Source)

	// Display DataMapping details
	dataMapping := mappedOrder.DataMapping.Data
	fmt.Printf("  Customer: %s\n", dataMapping.CustomerName)
	fmt.Printf("  Driver: %s\n", dataMapping.DriverName)
	fmt.Printf("  Total: %.0f VND\n", dataMapping.Total)
	fmt.Printf("  Commission: %.0f VND\n", dataMapping.Commission)
	fmt.Printf("  Order Time: %s\n", dataMapping.OrderTime)
	fmt.Printf("  Address: %s\n", dataMapping.CustomerAddress)
	fmt.Printf("  Note: %s\n", dataMapping.Note)
}

// testMapOrderWithDifferentSources demonstrates MapOrder with various sources
func testMapOrderWithDifferentSources() {
	sources := []struct {
		name   string
		source string
		data   map[string]interface{}
	}{
		{
			name:   "Grab Order",
			source: "grab",
			data: map[string]interface{}{
				"orderID":    "GR987654321",
				"orderTime":  "2024-01-01T14:00:00Z",
				"pickupTime": "2024-01-01T14:30:00Z",
				"total":      180000.0,
				"commission": 18000.0,
				"customer": map[string]interface{}{
					"name":    "Le Van C",
					"phone":   "+84912345678",
					"address": "456 Le Loi Street, District 3, Ho Chi Minh City",
				},
			},
		},
		{
			name:   "BE Order",
			source: "be",
			data: map[string]interface{}{
				"order_id":   "BE123456789",
				"order_time": "2024-01-01T15:00:00Z",
				"total":      200000.0,
				"customer": map[string]interface{}{
					"name":    "Pham Thi D",
					"phone":   "+84934567890",
					"address": "789 Dong Khoi Street, District 1, Ho Chi Minh City",
				},
			},
		},
		{
			name:   "Local Order",
			source: "local",
			data: map[string]interface{}{
				"order_id":        "LOCAL_001",
				"order_time":      "2024-01-01T16:00:00Z",
				"order_time_sort": time.Now().Unix(),
				"total":           150000.0,
				"customer": map[string]interface{}{
					"name":    "Hoang Van E",
					"phone":   "+84945678901",
					"address": "321 Hai Ba Trung Street, District 1, Ho Chi Minh City",
				},
			},
		},
	}

	for _, test := range sources {
		fmt.Printf("  %s:\n", test.name)

		merchantOrder := &models.MerchantOrder{
			LongOrderID:  fmt.Sprintf("%s_%d", test.source, time.Now().Unix()),
			ShortOrderID: fmt.Sprintf("SHORT_%s", test.source),
			Source:       test.source,
			DataInDetail: test.data,
			Status:       "PENDING",
		}

		mappedOrder := mapping.MapOrder(merchantOrder)
		dataMapping := mappedOrder.DataMapping.Data

		fmt.Printf("    - Order ID: %s\n", mappedOrder.OrderID)
		fmt.Printf("    - Source: %s\n", mappedOrder.Source)
		fmt.Printf("    - Customer: %s\n", dataMapping.CustomerName)
		fmt.Printf("    - Total: %.0f VND\n", dataMapping.Total)
		fmt.Printf("    - Commission: %.0f VND\n", dataMapping.Commission)
		fmt.Println()
	}
}

// testMapOrderWithDatabaseOrders demonstrates MapOrder with real database data
func testMapOrderWithDatabaseOrders() {
	// Try to connect to database
	dsn := os.Getenv("POSTGRESQL_URI")
	if dsn == "" {
		fmt.Println("  POSTGRESQL_URI not set, skipping database test")
		fmt.Println("  To test with real data, set POSTGRESQL_URI environment variable")
		return
	}

	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Silent),
	})
	if err != nil {
		fmt.Printf("  Failed to connect to database: %v\n", err)
		return
	}

	// Query recent orders
	var orders []models.Order
	result := db.Limit(3).Order("created_at DESC").Find(&orders)
	if result.Error != nil {
		fmt.Printf("  Error querying orders: %v\n", result.Error)
		return
	}

	if len(orders) == 0 {
		fmt.Println("  No orders found in database")
		return
	}

	fmt.Printf("  Found %d orders in database, testing MapOrder:\n", len(orders))

	for i, order := range orders {
		fmt.Printf("    Order %d:\n", i+1)
		fmt.Printf("      - Original ID: %s\n", order.OrderID)
		fmt.Printf("      - Source: %s\n", order.Source)
		fmt.Printf("      - Status: %s\n", order.Status)

		// Check if order has raw data
		if order.Data.Data == nil {
			fmt.Println("      - No raw data available")
			continue
		}

		rawData, exists := order.Data.Data["raw"]
		if !exists || rawData == nil {
			fmt.Println("      - No raw data in Data field")
			continue
		}

		// Create MerchantOrder from database data
		merchantOrder := &models.MerchantOrder{
			LongOrderID:  order.OrderID,
			ShortOrderID: order.ShortOrderID,
			Source:       order.Source,
			DataInDetail: rawData,
			Status:       order.Status,
		}

		// Re-map the order
		remappedOrder := mapping.MapOrder(merchantOrder)
		dataMapping := remappedOrder.DataMapping.Data

		fmt.Printf("      - Re-mapped successfully\n")
		fmt.Printf("      - Customer: %s\n", dataMapping.CustomerName)
		fmt.Printf("      - Total: %.0f VND\n", dataMapping.Total)
		fmt.Printf("      - Commission: %.0f VND\n", dataMapping.Commission)

		// Pretty print the DataMapping for debugging
		if i == 0 { // Only show detailed mapping for first order
			fmt.Println("      - Detailed DataMapping:")
			mappingJSON, _ := json.MarshalIndent(dataMapping, "        ", "  ")
			fmt.Printf("        %s\n", string(mappingJSON))
		}
		fmt.Println()
	}
}

// Helper function to demonstrate error handling
func handleMapOrderError(merchantOrder *models.MerchantOrder) {
	defer func() {
		if r := recover(); r != nil {
			log.Printf("MapOrder panicked for order %s: %v", merchantOrder.LongOrderID, r)
		}
	}()

	mappedOrder := mapping.MapOrder(merchantOrder)
	if mappedOrder == nil {
		log.Printf("MapOrder returned nil for order %s", merchantOrder.LongOrderID)
		return
	}

	fmt.Printf("Successfully mapped order %s\n", mappedOrder.OrderID)
}
