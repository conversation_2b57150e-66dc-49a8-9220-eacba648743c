package handlers

import (
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/merchant"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/rabbitmq"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/redis"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/utils"
	"github.com/tidwall/gjson"

	"github.com/gin-gonic/gin"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/middlewares"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/token"
	"github.com/thoas/go-funk"
	"gorm.io/gorm"
)

// CronSiteFeedbacks handles the API endpoint for processing site order feedbacks
func CronSiteFeedbacks(c *gin.Context) {
	db := middlewares.GetDB(c)
	rb := middlewares.GetRabbitMQ(c)
	queueKey := "cron_job:site_order_feedbacks"

	// Get cached sites from queue
	cachedSites, err := redis.Queue.GetQueue(queueKey)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// If queue is empty, populate it with sites that have relevant tokens
	if len(cachedSites) == 0 {
		var sites []models.Site
		if err := db.Where("tokens @> ?", `[{"source": "shopee", "token_code": {"$ne": ""}}]`).
			Or("tokens @> ?", `[{"source": "shopee_fresh", "token_code": {"$ne": ""}}]`).
			Or("tokens @> ?", `[{"source": "grab", "token_code": {"$ne": ""}}]`).
			Or("tokens @> ?", `[{"source": "grab_mart", "token_code": {"$ne": ""}}]`).
			Select("id, name").
			Find(&sites).Error; err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   err.Error(),
			})
			return
		}

		// Convert sites to map for queue
		sitesForQueue := funk.Map(sites, func(site models.Site) map[string]any {
			return map[string]any{
				"_id":  site.ID,
				"name": site.Name,
			}
		}).([]map[string]any)

		if err := redis.Queue.SetQueue(queueKey, sitesForQueue); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   err.Error(),
			})
			return
		}

		cachedSites = sitesForQueue
	}

	// Pick sites from queue for processing
	selectedSites, err := redis.Queue.PickQueue(queueKey, map[string]any{
		"size":         1,
		"min_duration": 3600, // 1 hour
	})
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Process selected sites
	tasks := []*utils.Task{}

	for _, site := range selectedSites {
		siteID, ok := site["_id"].(string)
		if !ok || siteID == "" {
			continue
		}

		tasks = append(tasks, utils.NewTask(func() error {
			// Create a message with site ID and limit
			message := map[string]any{
				"site_id": siteID,
				"limit":   100,
			}

			messageJSON, err := json.Marshal(message)
			if err != nil {
				return err
			}

			return rb.Publish("cron_site_order_feedbacks", messageJSON)
		}))
	}

	pool := utils.NewPool(tasks, 1)
	pool.Run()

	// Extract site names for response
	siteNames := funk.Map(selectedSites, func(site map[string]any) string {
		if name, ok := site["name"].(string); ok {
			return name
		}
		return ""
	}).([]string)

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    siteNames,
	})
}

// CronSiteIncidents handles the API endpoint for processing site order incidents
func CronSiteIncidents(c *gin.Context) {
	db := middlewares.GetDB(c)
	rb := middlewares.GetRabbitMQ(c)
	queueKey := "cron_job:site_order_incident_list"

	// Get cached sites from queue
	cachedSites, err := redis.Queue.GetQueue(queueKey)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// If queue is empty, populate it with sites that have relevant tokens
	if len(cachedSites) == 0 {
		var sites []models.Site
		if err := db.Where("tokens @> ?", `[{"source": "grab", "token_code": {"$ne": ""}}]`).
			Or("tokens @> ?", `[{"source": "grab_mart", "token_code": {"$ne": ""}}]`).
			Select("id, name").
			Find(&sites).Error; err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   err.Error(),
			})
			return
		}

		// Convert sites to map for queue
		sitesForQueue := funk.Map(sites, func(site models.Site) map[string]any {
			return map[string]any{
				"_id":  site.ID,
				"name": site.Name,
			}
		}).([]map[string]any)

		if err := redis.Queue.SetQueue(queueKey, sitesForQueue); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   err.Error(),
			})
			return
		}

		cachedSites = sitesForQueue
	}

	// Pick sites from queue for processing
	selectedSites, err := redis.Queue.PickQueue(queueKey, map[string]any{
		"size":         10,
		"min_duration": 600, // 10 minutes
	})
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Process selected sites
	tasks := []*utils.Task{}

	for _, site := range selectedSites {
		siteID, ok := site["_id"].(string)
		if !ok || siteID == "" {
			continue
		}

		tasks = append(tasks, utils.NewTask(func() error {
			// Create a message with site ID
			message := map[string]any{
				"site_id": siteID,
			}

			messageJSON, err := json.Marshal(message)
			if err != nil {
				return err
			}

			return rb.Publish("cron_site_order_incident_list", messageJSON)
		}))
	}

	pool := utils.NewPool(tasks, 10)
	pool.Run()

	// Extract site names for response
	siteNames := funk.Map(selectedSites, func(site map[string]any) string {
		if name, ok := site["name"].(string); ok {
			return name
		}
		return ""
	}).([]string)

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    siteNames,
	})
}

// ProcessFeedbacks is a background handler for processing site order feedbacks
func ProcessFeedbacks(rb *rabbitmq.RabbitClient, db *gorm.DB, message []byte) error {
	// Parse the message
	var msg struct {
		SiteID string `json:"site_id"`
		Limit  int    `json:"limit"`
	}

	if err := json.Unmarshal(message, &msg); err != nil {
		return err
	}

	// Find the site
	var site models.Site
	if err := db.Where("id = ?", msg.SiteID).First(&site).Error; err != nil {
		return err
	}

	if err := getFeedbacksBySource(db, &site, site.MainSource, msg.Limit); err != nil {
		log.Printf("Error getting feedbacks for site %s, source %s: %v", site.ID, site.MainSource, err)
	}

	return nil
}

// ProcessIncidents is a background handler for processing site order incidents
func ProcessIncidents(rb *rabbitmq.RabbitClient, db *gorm.DB, message []byte) error {
	// Parse the message
	var msg struct {
		SiteID string `json:"site_id"`
	}

	if err := json.Unmarshal(message, &msg); err != nil {
		return err
	}

	// Find the site
	var site models.Site
	if err := db.Where("id = ?", msg.SiteID).First(&site).Error; err != nil {
		return err
	}

	// Process each source
	sources := []string{"grab", "grab_mart"}
	for _, source := range sources {
		if err := getIncidentsBySource(db, &site, source); err != nil {
			log.Printf("Error getting incidents for site %s, source %s: %v", site.ID, source, err)
		}
	}

	return nil
}

// getFeedbacksBySource retrieves order feedbacks for a specific site and source
func getFeedbacksBySource(db *gorm.DB, site *models.Site, source string, limit int) error {
	// Get token for the site and source
	tokenAccount, err := token.GetTokenBySite(db, *site, source)
	if err != nil || tokenAccount == nil || tokenAccount.AccessToken == "" {
		return fmt.Errorf("token not found or invalid for site %s, source %s", site.ID, source)
	}

	// Skip if token has too many failures
	if tokenAccount.FailCount > 100 {
		return fmt.Errorf("token has too many failures for site %s, source %s", site.ID, source)
	}

	// Get merchant client for the source
	iMerchant := merchant.NewMerchant(source)
	if iMerchant == nil {
		return fmt.Errorf("merchant client not found for source %s", source)
	}

	// Convert TokenAccount to Token for merchant interface
	merchantToken := &models.Token{
		AccessToken: tokenAccount.AccessToken,
		SiteID:      tokenAccount.SiteID,
		SiteName:    tokenAccount.SiteName,
		Username:    tokenAccount.Username,
		Password:    tokenAccount.Password,
	}

	// Get order feedbacks
	feedbackList, err := iMerchant.GetOrderFeedbacks(merchantToken, limit)
	if err != nil {
		log.Printf("Error getting feedbacks for site %s, source %s: %v", site.ID, source, err)
		return nil // Don't return error, just skip this source
	}

	if len(feedbackList) == 0 {
		log.Printf("No feedbacks found for site %s, source %s", site.ID, source)
		return nil
	}

	// Map and save feedbacks
	for _, feedbackData := range feedbackList {
		mappedFeedback, err := mapOrderFeedback(source, feedbackData)
		if err != nil {
			log.Printf("Error mapping feedback for site %s, source %s: %v", site.ID, source, err)
			continue
		}

		if mappedFeedback == nil {
			continue
		}

		// Set site ID
		mappedFeedback.SiteID = site.ID

		// Check if feedback already exists
		var existingFeedback models.OrderFeedback
		if err := db.Where("ref_id = ? AND source = ?", mappedFeedback.RefID, source).First(&existingFeedback).Error; err == nil {
			// Feedback already exists, skip
			continue
		}

		// For grab/grab_mart, try to find order_id from orders table
		if source == "grab" || source == "grab_mart" {
			var order models.Order
			if err := db.Where("data->>'bookingCode' = ?", mappedFeedback.RefID).First(&order).Error; err == nil {
				mappedFeedback.OrderID = order.OrderID
			}
		}

		// Save new feedback
		if err := db.Create(mappedFeedback).Error; err != nil {
			log.Printf("Error saving feedback for site %s, source %s: %v", site.ID, source, err)
			continue
		}

		log.Printf("Saved feedback %s for site %s, source %s", mappedFeedback.RefID, site.ID, source)
	}

	log.Printf("Processed %d feedbacks for site %s, source %s", len(feedbackList), site.ID, source)

	return nil
}

// getIncidentsBySource retrieves order incidents for a specific site and source
func getIncidentsBySource(db *gorm.DB, site *models.Site, source string) error {
	// Get token for the site and source
	tokenAccount, err := token.GetTokenBySite(db, *site, source)
	if err != nil || tokenAccount == nil || tokenAccount.AccessToken == "" {
		return fmt.Errorf("token not found or invalid for site %s, source %s", site.ID, source)
	}

	// Skip if token has too many failures
	if tokenAccount.FailCount > 100 {
		return fmt.Errorf("token has too many failures for site %s, source %s", site.ID, source)
	}

	// Get merchant client for the source
	iMerchant := merchant.NewMerchant(source)
	if iMerchant == nil {
		return fmt.Errorf("merchant client not found for source %s", source)
	}

	// Get order incidents
	var incidentList []any

	// This part would need to be implemented in the merchant interface
	// For now, we'll just log that we would fetch incidents
	log.Printf("Would fetch incidents for site %s, source %s", site.ID, source)

	// Process incidents
	if len(incidentList) > 0 {
		// Map incident to OrderIncident model
		// Save to database if not exists
		log.Printf("Would process %d incidents for site %s, source %s", len(incidentList), site.ID, source)
	}

	return nil
}

// mapOrderFeedback maps feedback data from different merchant sources to OrderFeedback model
func mapOrderFeedback(source string, data any) (*models.OrderFeedback, error) {
	dataBytes, err := json.Marshal(data)
	if err != nil {
		return nil, err
	}

	switch source {
	case "shopee", "shopee_fresh", "shopee_food":
		return mapShopeeFeedback(dataBytes)
	case "grab", "grab_mart":
		return mapGrabFeedback(dataBytes)
	case "be", "be_mart":
		return mapBEFeedback(dataBytes)
	case "gojek":
		return mapGojekFeedback(dataBytes)
	default:
		return nil, nil
	}
}

// mapShopeeFeedback maps Shopee feedback data
func mapShopeeFeedback(dataBytes []byte) (*models.OrderFeedback, error) {
	parsed := gjson.ParseBytes(dataBytes)

	// Extract comments and reasons
	var comments []string
	if parsed.Get("comments").Exists() {
		for _, comment := range parsed.Get("comments").Array() {
			comments = append(comments, comment.String())
		}
	}
	if parsed.Get("reasons").Exists() {
		for _, reason := range parsed.Get("reasons").Array() {
			comments = append(comments, reason.String())
		}
	}

	feedback := &models.OrderFeedback{
		RefID:         parsed.Get("id").String(),
		OrderID:       parsed.Get("order_code").String(),
		Rating:        int(parsed.Get("rating_star").Int() - 100), // Shopee uses 101-105 for 1-5 stars
		Comment:       strings.Join(comments, ", "),
		CustomerName:  parsed.Get("user_info.name").String(),
		CreatedAtUnix: parsed.Get("create_time").Int(),
		Data:          json.RawMessage(dataBytes),
	}

	return feedback, nil
}

// mapGrabFeedback maps Grab feedback data
func mapGrabFeedback(dataBytes []byte) (*models.OrderFeedback, error) {
	parsed := gjson.ParseBytes(dataBytes)

	// Parse created date
	var createdAtUnix int64
	if createdAt := parsed.Get("createdAt").String(); createdAt != "" {
		if t, err := time.Parse(time.RFC3339, createdAt); err == nil {
			createdAtUnix = t.Unix()
		}
	}

	rating := int(parsed.Get("rating").Int())
	if rating == 0 {
		rating = 5 // Default rating if not provided
	}

	feedback := &models.OrderFeedback{
		RefID:         parsed.Get("bookingCode").String(),
		OrderID:       parsed.Get("orderID").String(),
		Rating:        rating,
		Comment:       parsed.Get("description").String(),
		CustomerName:  parsed.Get("eaterName").String(),
		CreatedAtUnix: createdAtUnix,
		Data:          json.RawMessage(dataBytes),
	}

	return feedback, nil
}

// mapBEFeedback maps BE feedback data
func mapBEFeedback(dataBytes []byte) (*models.OrderFeedback, error) {
	parsed := gjson.ParseBytes(dataBytes)

	// Parse rated_at date (format: "HH:mm DD/MM/YYYY")
	var createdAtUnix int64
	if ratedAt := parsed.Get("rated_at").String(); ratedAt != "" {
		if t, err := time.Parse("15:04 02/01/2006", ratedAt); err == nil {
			createdAtUnix = t.Unix()
		}
	}

	rating := int(parsed.Get("rating").Int())
	if rating == 0 {
		rating = 5 // Default rating if not provided
	}

	feedback := &models.OrderFeedback{
		RefID:         strconv.FormatInt(parsed.Get("rating_id").Int(), 10),
		OrderID:       strconv.FormatInt(parsed.Get("order_id").Int(), 10),
		Rating:        rating,
		Comment:       parsed.Get("feedback").String(),
		CustomerName:  parsed.Get("user_name").String(),
		CreatedAtUnix: createdAtUnix,
		Data:          json.RawMessage(dataBytes),
	}

	return feedback, nil
}

// mapGojekFeedback maps Gojek feedback data
func mapGojekFeedback(dataBytes []byte) (*models.OrderFeedback, error) {
	parsed := gjson.ParseBytes(dataBytes)

	// Parse created date
	var createdAtUnix int64
	if createdAt := parsed.Get("created_at").String(); createdAt != "" {
		if t, err := time.Parse(time.RFC3339, createdAt); err == nil {
			createdAtUnix = t.Unix()
		}
	}

	rating := int(parsed.Get("rating").Int())
	if rating == 0 {
		rating = 5 // Default rating if not provided
	}

	orderID := parsed.Get("order.id").String()

	feedback := &models.OrderFeedback{
		RefID:         orderID,
		OrderID:       orderID,
		Rating:        rating,
		Comment:       parsed.Get("text").String(),
		CustomerName:  "", // Gojek doesn't provide customer name in feedback
		CreatedAtUnix: createdAtUnix,
		Data:          json.RawMessage(dataBytes),
	}

	return feedback, nil
}
