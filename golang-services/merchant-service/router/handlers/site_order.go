package handlers

import (
	"crypto/md5"
	"encoding/json"
	"errors"
	"fmt"
	"log"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/merchant"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/rabbitmq"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/mapping"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/redis"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/token"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/utils"
	"gorm.io/gorm"
)

// CronSiteOrders processes orders for sites with active tokens
func CronSiteOrders(rb *rabbitmq.RabbitClient, db *gorm.DB, siteID string) error {
	// Get site from database
	var site models.Site
	if err := db.Where("id = ?", siteID).First(&site).Error; err != nil {
		return err
	}

	ordersMap, err := getSiteOrders(db, siteID)
	if err != nil {
		return err
	}

	oldMD5Str := redis.GetByKey(fmt.Sprintf("site_orders_md5:%s", siteID))

	oldOrders := map[string]models.MerchantOrder{}

	if oldMD5Str != "" {
		json.Unmarshal([]byte(oldMD5Str), &oldOrders)
	}

	diffOrders := map[string]models.MerchantOrder{}
	for orderStatus, orders := range ordersMap {
		for _, order := range orders {
			// if oldOrder, ok := oldOrders[order.LongOrderID]; ok && oldOrder.MD5 == order.MD5 {
			// 	continue
			// }

			iMerchant := merchant.NewMerchant(order.Source)

			token, err := token.GetTokenBySite(db, site, order.Source)
			if err != nil {
				log.Printf("Error getting token: %v", err)
				continue
			}
			orderDetail, err := iMerchant.GetOrderDetail(&models.Token{
				SiteID:      token.SiteID,
				AccessToken: token.AccessToken,
			}, order.LongOrderID)

			if err != nil {
				log.Printf("Error getting order detail: %v", err)
				continue
			}

			order.DataInDetail = orderDetail
			order.Status = orderStatus
			diffOrders[order.LongOrderID] = order
		}
	}

	if len(diffOrders) > 0 {
		redis.SetKeyValue(fmt.Sprintf("site_orders_md5:%s", siteID), utils.StructToJSON(ordersMap).Raw)
		// Save to Database
		for _, order := range diffOrders {
			mapOrder := mapping.MapOrder(&order)
			mapOrder.SiteID = site.ID
			mapOrder.HubID = site.HubID
			mapOrder.Status = order.Status
			mapOrder.UserID = ""
			var dbOrder models.Order
			if err := db.Where("order_id = ?", order.LongOrderID).First(&dbOrder).Error; err != nil {
				if !errors.Is(err, gorm.ErrRecordNotFound) {
					return err
				} else {
					if err := db.Create(mapOrder).Error; err != nil {
						return err
					}
				}
			} else {
				if mapOrder != nil {
					if err := db.Model(&dbOrder).Updates(mapOrder).Error; err != nil {
						return err
					}
				}
			}
		}
	}

	return nil
}

// getSiteOrders retrieves orders for a specific site
func getSiteOrders(db *gorm.DB, siteID string) (map[string][]models.MerchantOrder, error) {
	// Get site from database
	var site models.Site
	if err := db.Where("id = ?", siteID).First(&site).Error; err != nil {
		return nil, err
	}

	result := map[string][]models.MerchantOrder{}
	token, err := token.GetTokenBySite(db, site, site.MainSource)
	if err != nil {
		return nil, err
	}
	if token == nil {
		return nil, nil
	}
	if token.SiteID == "" || token.AccessToken == "" {
		return nil, nil
	}

	iMerchant := merchant.NewMerchant(site.MainSource)
	mapStatusOrders, err := iMerchant.GetOrderListV2(&models.Token{
		SiteID:      token.SiteID,
		AccessToken: token.AccessToken,
	})

	if err != nil {
		return nil, err
	}
	for status, orders := range mapStatusOrders {
		for i := range orders {
			orders[i].Source = site.MainSource
			orders[i].MD5 = makeOrderMD5(orders[i])
			orders[i].Status = status
			result[status] = append(result[status], orders[i])
		}
	}
	return result, nil
}

func makeOrderMD5(order models.MerchantOrder) string {
	raw := utils.StructToJSON(order.DataInList).Raw
	excludeFields := []string{}
	if order.Source == "be" {
		excludeFields = []string{"question", "question_type"}
	}

	var data map[string]any
	json.Unmarshal([]byte(raw), &data)

	for _, field := range excludeFields {
		delete(data, field)
	}

	cleanData, _ := json.Marshal(data)
	raw = string(cleanData)

	return fmt.Sprintf("%x", md5.Sum([]byte(raw)))
}
