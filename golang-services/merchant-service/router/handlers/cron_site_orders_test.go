package handlers

import (
	"encoding/json"
	"fmt"
	"log"
	"os"
	"testing"
	"time"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/mapping"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"
	"github.com/stretchr/testify/assert"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// setupTestDB creates a test database connection
func setupTestDB() (*gorm.DB, error) {
	dsn := os.Getenv("POSTGRESQL_URI")
	if dsn == "" {
		// Use a default test database connection if not set
		dsn = "host=localhost user=test password=test dbname=test_db port=5432 sslmode=disable"
	}

	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Silent), // Silent mode for tests
	})
	if err != nil {
		return nil, fmt.Errorf("failed to connect to test database: %v", err)
	}

	return db, nil
}

// TestMapOrderFunctionality tests the MapOrder function with real database data
func TestMapOrderFunctionality(t *testing.T) {
	// Test cases for different order sources
	testCases := []struct {
		name         string
		source       string
		sampleData   map[string]interface{}
		expectedType string
	}{
		{
			name:   "Shopee Food Order",
			source: "shopee_food",
			sampleData: map[string]interface{}{
				"order_time":         1640995200, // Unix timestamp
				"actual_pick_time":   1640998800,
				"delivery_time_unix": 1641002400,
				"order_value_amount": 150000.0,
				"commission": map[string]interface{}{
					"amount": 15000.0,
				},
				"total_value_amount": 135000.0,
				"assignee": map[string]interface{}{
					"name":  "John Driver",
					"phone": "+84901234567",
				},
				"order_user": map[string]interface{}{
					"name":  "Jane Customer",
					"phone": "+84987654321",
				},
				"deliver_address": map[string]interface{}{
					"contact_name": "Jane Customer",
					"address":      "123 Test Street, District 1, Ho Chi Minh City",
				},
				"notes": map[string]interface{}{
					"order_note": "Extra spicy please",
				},
				"is_remove_plastic": false,
			},
			expectedType: "shopee_food",
		},
		{
			name:   "Grab Order",
			source: "grab",
			sampleData: map[string]interface{}{
				"orderID":    "GR123456789",
				"orderTime":  "2024-01-01T10:00:00Z",
				"pickupTime": "2024-01-01T10:30:00Z",
				"total":      200000.0,
				"commission": 20000.0,
				"customer": map[string]interface{}{
					"name":    "Bob Customer",
					"phone":   "+84912345678",
					"address": "456 Grab Street, District 2, Ho Chi Minh City",
				},
				"driver": map[string]interface{}{
					"name":  "Mike Driver",
					"phone": "+84923456789",
				},
			},
			expectedType: "grab",
		},
		{
			name:   "BE Order",
			source: "be",
			sampleData: map[string]interface{}{
				"order_id":   "BE987654321",
				"order_time": "2024-01-01T11:00:00Z",
				"total":      180000.0,
				"customer": map[string]interface{}{
					"name":    "Alice Customer",
					"phone":   "+84934567890",
					"address": "789 BE Avenue, District 3, Ho Chi Minh City",
				},
				"delivery_details": map[string]interface{}{
					"pickup_time": "2024-01-01T11:30:00Z",
				},
			},
			expectedType: "be",
		},
		{
			name:   "Local Order",
			source: "local",
			sampleData: map[string]interface{}{
				"order_id":        "LOCAL123",
				"order_time":      "2024-01-01T12:00:00Z",
				"order_time_sort": 1704110400,
				"pick_time":       "2024-01-01T12:30:00Z",
				"delivery_time":   "2024-01-01T13:00:00Z",
				"total":           120000.0,
				"customer": map[string]interface{}{
					"name":    "Local Customer",
					"phone":   "+84945678901",
					"address": "321 Local Road, District 4, Ho Chi Minh City",
				},
			},
			expectedType: "local",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Create a sample MerchantOrder
			merchantOrder := &models.MerchantOrder{
				LongOrderID:  fmt.Sprintf("TEST_%s_%d", tc.source, time.Now().Unix()),
				ShortOrderID: fmt.Sprintf("SHORT_%s", tc.source),
				Source:       tc.source,
				DataInDetail: tc.sampleData,
				Status:       "PENDING",
			}

			// Test the MapOrder function
			mappedOrder := mapping.MapOrder(merchantOrder)

			// Assertions
			assert.NotNil(t, mappedOrder, "MapOrder should return a non-nil order")
			assert.Equal(t, merchantOrder.LongOrderID, mappedOrder.OrderID, "Order ID should match")
			assert.Equal(t, merchantOrder.ShortOrderID, mappedOrder.ShortOrderID, "Short Order ID should match")
			assert.Equal(t, tc.source, mappedOrder.Source, "Source should match")

			// Check that Data field contains the raw data
			assert.NotNil(t, mappedOrder.Data.Data, "Data field should not be nil")
			rawData, exists := mappedOrder.Data.Data["raw"]
			assert.True(t, exists, "Raw data should exist in Data field")
			assert.Equal(t, tc.sampleData, rawData, "Raw data should match original data")

			// Check DataMapping field
			assert.NotNil(t, mappedOrder.DataMapping.Data, "DataMapping should not be nil")
			dataMapping := mappedOrder.DataMapping.Data
			assert.Equal(t, merchantOrder.LongOrderID, dataMapping.ID, "DataMapping ID should match order ID")
			assert.Equal(t, merchantOrder.ShortOrderID, dataMapping.OrderID, "DataMapping OrderID should match short order ID")
			assert.Equal(t, tc.source, dataMapping.Source, "DataMapping Source should match")

			// Log the mapped order for debugging
			mappedJSON, _ := json.MarshalIndent(mappedOrder, "", "  ")
			log.Printf("Mapped Order for %s:\n%s", tc.name, string(mappedJSON))
		})
	}
}

// TestQueryOrdersFromDatabase tests querying actual orders from the database
func TestQueryOrdersFromDatabase(t *testing.T) {
	// Skip test if no database connection available
	db, err := setupTestDB()
	if err != nil {
		t.Skipf("Skipping test due to database connection error: %v", err)
		return
	}

	// Query recent orders from the database
	var orders []models.Order
	result := db.Limit(5).Order("created_at DESC").Find(&orders)

	if result.Error != nil {
		t.Logf("Error querying orders: %v", result.Error)
		return
	}

	t.Logf("Found %d orders in database", len(orders))

	for i, order := range orders {
		t.Logf("Order %d: ID=%s, Source=%s, Status=%s", i+1, order.OrderID, order.Source, order.Status)

		// Test re-mapping existing order data
		if order.Data.Data != nil {
			if rawData, exists := order.Data.Data["raw"]; exists && rawData != nil {
				// Create a MerchantOrder from the existing data
				merchantOrder := &models.MerchantOrder{
					LongOrderID:  order.OrderID,
					ShortOrderID: order.ShortOrderID,
					Source:       order.Source,
					DataInDetail: rawData,
					Status:       order.Status,
				}

				// Re-map the order
				remappedOrder := mapping.MapOrder(merchantOrder)

				// Verify the re-mapping
				assert.NotNil(t, remappedOrder, "Re-mapped order should not be nil")
				assert.Equal(t, order.OrderID, remappedOrder.OrderID, "Order ID should match after re-mapping")
				assert.Equal(t, order.Source, remappedOrder.Source, "Source should match after re-mapping")

				t.Logf("Successfully re-mapped order %s from source %s", order.OrderID, order.Source)
			}
		}
	}
}

// TestMapOrderWithDifferentSources tests MapOrder with various source types
func TestMapOrderWithDifferentSources(t *testing.T) {
	sources := []string{"shopee_food", "shopee_fresh", "grab", "grab_mart", "be", "local", "unknown_source"}

	for _, source := range sources {
		t.Run(fmt.Sprintf("Source_%s", source), func(t *testing.T) {
			merchantOrder := &models.MerchantOrder{
				LongOrderID:  fmt.Sprintf("TEST_%s_%d", source, time.Now().UnixNano()),
				ShortOrderID: fmt.Sprintf("SHORT_%s", source),
				Source:       source,
				DataInDetail: map[string]interface{}{
					"test_field": "test_value",
					"order_time": time.Now().Unix(),
				},
				Status: "PENDING",
			}

			mappedOrder := mapping.MapOrder(merchantOrder)

			assert.NotNil(t, mappedOrder, "MapOrder should handle all source types")
			assert.Equal(t, source, mappedOrder.Source, "Source should be preserved")
			assert.NotNil(t, mappedOrder.DataMapping.Data, "DataMapping should be created for all sources")

			// Check that unknown sources get default mapping
			if source == "unknown_source" {
				dataMapping := mappedOrder.DataMapping.Data
				assert.Equal(t, merchantOrder.LongOrderID, dataMapping.ID)
				assert.Equal(t, merchantOrder.ShortOrderID, dataMapping.OrderID)
				assert.Equal(t, source, dataMapping.Source)
			}
		})
	}
}

// TestMapOrderWithRealDatabaseData tests MapOrder with actual database orders
func TestMapOrderWithRealDatabaseData(t *testing.T) {
	// Skip test if no database connection available
	db, err := setupTestDB()
	if err != nil {
		t.Skipf("Skipping test due to database connection error: %v", err)
		return
	}

	// Query orders from different sources
	sources := []string{"shopee_food", "grab", "be", "local"}

	for _, source := range sources {
		t.Run(fmt.Sprintf("RealData_%s", source), func(t *testing.T) {
			var orders []models.Order
			result := db.Where("source = ?", source).Limit(3).Find(&orders)

			if result.Error != nil {
				t.Logf("Error querying %s orders: %v", source, result.Error)
				return
			}

			if len(orders) == 0 {
				t.Logf("No %s orders found in database", source)
				return
			}

			t.Logf("Testing MapOrder with %d real %s orders", len(orders), source)

			for _, order := range orders {
				if order.Data.Data == nil {
					continue
				}

				rawData, exists := order.Data.Data["raw"]
				if !exists || rawData == nil {
					continue
				}

				// Create MerchantOrder from database data
				merchantOrder := &models.MerchantOrder{
					LongOrderID:  order.OrderID,
					ShortOrderID: order.ShortOrderID,
					Source:       order.Source,
					DataInDetail: rawData,
					Status:       order.Status,
				}

				// Test MapOrder function
				mappedOrder := mapping.MapOrder(merchantOrder)

				// Verify mapping results
				assert.NotNil(t, mappedOrder, "MapOrder should not return nil")
				assert.Equal(t, order.OrderID, mappedOrder.OrderID, "Order ID should match")
				assert.Equal(t, order.Source, mappedOrder.Source, "Source should match")
				assert.NotNil(t, mappedOrder.DataMapping.Data, "DataMapping should not be nil")

				// Log mapping details
				dataMapping := mappedOrder.DataMapping.Data
				t.Logf("Order %s mapped successfully:", order.OrderID)
				t.Logf("  - Source: %s", dataMapping.Source)
				t.Logf("  - Customer: %s", dataMapping.CustomerName)
				t.Logf("  - Total: %.2f", dataMapping.Total)
				t.Logf("  - Commission: %.2f", dataMapping.Commission)

				// Verify specific fields based on source
				switch source {
				case "shopee_food", "shopee_fresh":
					assert.NotEmpty(t, dataMapping.OrderTime, "Shopee orders should have order time")
				case "grab", "grab_mart":
					assert.NotEmpty(t, dataMapping.ID, "Grab orders should have ID")
				case "be":
					assert.NotEmpty(t, dataMapping.ID, "BE orders should have ID")
				case "local":
					assert.NotEmpty(t, dataMapping.ID, "Local orders should have ID")
				}
			}
		})
	}
}

// TestMapOrderDataConsistency tests that MapOrder produces consistent results
func TestMapOrderDataConsistency(t *testing.T) {
	// Test data for consistency check
	testData := map[string]interface{}{
		"order_time":         1640995200,
		"actual_pick_time":   1640998800,
		"delivery_time_unix": 1641002400,
		"order_value_amount": 150000.0,
		"commission": map[string]interface{}{
			"amount": 15000.0,
		},
		"total_value_amount": 135000.0,
		"assignee": map[string]interface{}{
			"name":  "Test Driver",
			"phone": "+84901234567",
		},
		"order_user": map[string]interface{}{
			"name":  "Test Customer",
			"phone": "+84987654321",
		},
	}

	merchantOrder := &models.MerchantOrder{
		LongOrderID:  "CONSISTENCY_TEST",
		ShortOrderID: "CONS_TEST",
		Source:       "shopee_food",
		DataInDetail: testData,
		Status:       "PENDING",
	}

	// Map the same order multiple times
	results := make([]*models.Order, 5)
	for i := 0; i < 5; i++ {
		results[i] = mapping.MapOrder(merchantOrder)
	}

	// Verify all results are identical
	for i := 1; i < len(results); i++ {
		assert.Equal(t, results[0].OrderID, results[i].OrderID, "Order ID should be consistent")
		assert.Equal(t, results[0].Source, results[i].Source, "Source should be consistent")

		// Compare DataMapping
		dm0 := results[0].DataMapping.Data
		dmi := results[i].DataMapping.Data
		assert.Equal(t, dm0.Total, dmi.Total, "Total should be consistent")
		assert.Equal(t, dm0.Commission, dmi.Commission, "Commission should be consistent")
		assert.Equal(t, dm0.CustomerName, dmi.CustomerName, "Customer name should be consistent")
	}

	t.Log("MapOrder produces consistent results across multiple calls")
}

// BenchmarkMapOrder benchmarks the MapOrder function performance
func BenchmarkMapOrder(b *testing.B) {
	merchantOrder := &models.MerchantOrder{
		LongOrderID:  "BENCH_TEST_ORDER",
		ShortOrderID: "BENCH_SHORT",
		Source:       "shopee_food",
		DataInDetail: map[string]interface{}{
			"order_time":         1640995200,
			"actual_pick_time":   1640998800,
			"delivery_time_unix": 1641002400,
			"order_value_amount": 150000.0,
			"commission": map[string]interface{}{
				"amount": 15000.0,
			},
			"total_value_amount": 135000.0,
		},
		Status: "PENDING",
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = mapping.MapOrder(merchantOrder)
	}
}
