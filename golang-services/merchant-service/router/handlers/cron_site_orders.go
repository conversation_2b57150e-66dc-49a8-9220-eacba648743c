package handlers

import (
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"net/http"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/mapping"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/merchant"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/rabbitmq"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/redis"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/token"
	"github.com/spf13/cast"

	"time"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/utils"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/middlewares"

	"github.com/gin-gonic/gin"
	"github.com/thoas/go-funk"
	"gorm.io/gorm"
)

// CronSiteOrdersHandler handles the API endpoint for processing site orders
func CronSiteOrdersHandler(c *gin.Context) {
	db := middlewares.GetDB(c)
	var sites []models.Site
	if err := db.Where("active = ?", true).
		Where("(shopee_token->>'token_code' != '') OR (grab_token->>'token_code' != '') OR (be_token->>'token_code' != '') OR (shopee_ecom_token->>'access_token' != '')").
		Select("id, name").
		Find(&sites).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Process selected sites
	rb := middlewares.GetRabbitMQ(c)
	tasks := []*utils.Task{}

	for _, site := range sites {
		tasks = append(tasks, utils.NewTask(func() error {
			return rb.Publish("cron_site_order", []byte(cast.ToString(site.ID)))
		}))
	}

	pool := utils.NewPool(tasks, 5)
	pool.Run()

	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}

// CronSiteEcomOrdersHandler handles the API endpoint for processing ecommerce site orders
func CronSiteEcomOrdersHandler(c *gin.Context) {
	db := middlewares.GetDB(c)
	queueKey := "cron_job:site_ecom_orders"

	// Get cached sites from queue
	cachedSites, err := redis.Queue.GetQueue(queueKey)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// If queue is empty, populate it with active sites that have ecom tokens
	if len(cachedSites) == 0 {
		var sites []models.Site
		fiveMinutesAgo := time.Now().Add(-5 * time.Minute)

		if err := db.Where("active = ?", true).
			Where("last_cron_ecom_order IS NULL OR last_cron_ecom_order <= ?", fiveMinutesAgo).
			Where("tokens @> ?", `[{"source": "shopee_ecom", "token_code": {"$ne": ""}}]`).
			Or("tokens @> ?", `[{"source": "lazada", "token_code": {"$ne": ""}}]`).
			Or("tokens @> ?", `[{"source": "tiktok", "token_code": {"$ne": ""}}]`).
			Select("id, name").
			Find(&sites).Error; err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   err.Error(),
			})
			return
		}

		// Convert sites to map for queue
		sitesForQueue := funk.Map(sites, func(site models.Site) map[string]any {
			return map[string]any{
				"_id":  site.ID,
				"name": site.Name,
			}
		}).([]map[string]any)

		if err := redis.Queue.SetQueue(queueKey, sitesForQueue); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   err.Error(),
			})
			return
		}

		cachedSites = sitesForQueue
	}

	// Pick sites from queue for processing
	selectedSites, err := redis.Queue.PickQueue(queueKey, map[string]any{
		"size":         5,
		"min_duration": 300,
	})
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Process selected sites
	rb := middlewares.GetRabbitMQ(c)
	tasks := []*utils.Task{}

	for _, site := range selectedSites {
		siteID, ok := site["_id"].(string)
		if !ok || siteID == "" {
			continue
		}

		tasks = append(tasks, utils.NewTask(func() error {
			// Update last_cron_ecom_order timestamp
			if err := db.Model(&models.Site{}).Where("id = ?", siteID).
				Update("last_cron_ecom_order", time.Now()).Error; err != nil {
				log.Printf("Error updating last_cron_ecom_order: %v", err)
			}

			return rb.Publish("cron_site_ecom_order", []byte(siteID))
		}))
	}

	pool := utils.NewPool(tasks, 5)
	pool.Run()

	// Extract site names for response
	siteNames := funk.Map(selectedSites, func(site map[string]any) string {
		if name, ok := site["name"].(string); ok {
			return name
		}
		return ""
	}).([]string)

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    siteNames,
	})
}

// CronSiteOrdersInXDays handles the API endpoint for processing site orders in a date range
func CronSiteOrdersInXDays(c *gin.Context) {
	db := middlewares.GetDB(c)
	queueKey := "cron_job:site_orders_in_x_days"

	// Get cached sites from queue
	cachedSites, err := redis.Queue.GetQueue(queueKey)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// If queue is empty, populate it with active sites
	if len(cachedSites) == 0 {
		var sites []models.Site
		if err := db.Where("active = ?", true).
			Select("id, name").
			Find(&sites).Error; err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   err.Error(),
			})
			return
		}

		// Convert sites to map for queue
		sitesForQueue := funk.Map(sites, func(site models.Site) map[string]any {
			return map[string]any{
				"_id":  site.ID,
				"name": site.Name,
			}
		}).([]map[string]any)

		if err := redis.Queue.SetQueue(queueKey, sitesForQueue); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   err.Error(),
			})
			return
		}

		cachedSites = sitesForQueue
	}

	// Pick sites from queue for processing
	selectedSites, err := redis.Queue.PickQueue(queueKey, map[string]any{
		"size":         2,
		"min_duration": 4 * 60 * 60, // 4 hours
	})
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Process selected sites
	rb := middlewares.GetRabbitMQ(c)
	tasks := []*utils.Task{}

	for _, site := range selectedSites {
		siteID, ok := site["_id"].(string)
		if !ok || siteID == "" {
			continue
		}

		tasks = append(tasks, utils.NewTask(func() error {
			// Create a message with site ID and date range
			message := map[string]any{
				"site_id": siteID,
				"from":    time.Now().AddDate(0, 0, -2).Format(time.RFC3339),
				"to":      time.Now().Format(time.RFC3339),
			}

			messageJSON, err := json.Marshal(message)
			if err != nil {
				return err
			}

			return rb.Publish("cron_site_orders_by_days", messageJSON)
		}))
	}

	pool := utils.NewPool(tasks, 2)
	pool.Run()

	// Extract site names for response
	siteNames := funk.Map(selectedSites, func(site map[string]any) string {
		if name, ok := site["name"].(string); ok {
			return name
		}
		return ""
	}).([]string)

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    siteNames,
	})
}

// ProcessSiteOrdersByDays is a background handler for processing site orders by days
func ProcessSiteOrdersByDays(rb *rabbitmq.RabbitClient, db *gorm.DB, message []byte) error {
	// Parse the message
	var msg struct {
		SiteID string `json:"site_id"`
		From   string `json:"from"`
		To     string `json:"to"`
	}

	if err := json.Unmarshal(message, &msg); err != nil {
		return fmt.Errorf("failed to unmarshal message: %v", err)
	}

	// Parse dates
	startTime, err := time.Parse(time.RFC3339, msg.From)
	if err != nil {
		return fmt.Errorf("failed to parse start time: %v", err)
	}

	endTime, err := time.Parse(time.RFC3339, msg.To)
	if err != nil {
		return fmt.Errorf("failed to parse end time: %v", err)
	}

	// Get site from database
	var site models.Site
	if err := db.Where("id = ?", msg.SiteID).First(&site).Error; err != nil {
		return fmt.Errorf("failed to find site: %v", err)
	}

	log.Printf("Processing orders by days for site %s (%s) from %s to %s", site.ID, site.Name, msg.From, msg.To)

	// Get orders for the specified date range
	ordersMap, err := getSiteOrdersByDuration(db, msg.SiteID, startTime, endTime)
	if err != nil {
		return fmt.Errorf("failed to get site orders by duration: %v", err)
	}

	// Process and save orders
	totalProcessed := 0
	for orderStatus, orders := range ordersMap {
		for _, order := range orders {
			// Get order detail from merchant
			iMerchant := merchant.NewMerchant(order.Source)
			token, err := token.GetTokenBySite(db, site, order.Source)
			if err != nil {
				log.Printf("Error getting token for site %s, source %s: %v", site.ID, order.Source, err)
				continue
			}

			orderDetail, err := iMerchant.GetOrderDetail(&models.Token{
				SiteID:      token.SiteID,
				AccessToken: token.AccessToken,
			}, order.LongOrderID)
			if err != nil {
				log.Printf("Error getting order detail for order %s: %v", order.LongOrderID, err)
				continue
			}

			order.DataInDetail = orderDetail
			order.Status = orderStatus

			// Map and save order
			mapOrder := mapping.MapOrder(&order)
			mapOrder.SiteID = site.ID
			mapOrder.HubID = site.HubID
			mapOrder.Status = order.Status
			mapOrder.UserID = ""

			var dbOrder models.Order
			if err := db.Where("order_id = ?", order.LongOrderID).First(&dbOrder).Error; err != nil {
				if errors.Is(err, gorm.ErrRecordNotFound) {
					// Create new order
					if err := db.Create(mapOrder).Error; err != nil {
						log.Printf("Error creating order %s: %v", order.LongOrderID, err)
						continue
					}
					totalProcessed++
				} else {
					log.Printf("Error checking existing order %s: %v", order.LongOrderID, err)
					continue
				}
			} else {
				// Update existing order
				if mapOrder != nil {
					if err := db.Model(&dbOrder).Updates(mapOrder).Error; err != nil {
						log.Printf("Error updating order %s: %v", order.LongOrderID, err)
						continue
					}
					totalProcessed++
				}
			}
		}
	}

	log.Printf("Completed processing orders by days for site %s. Total processed: %d", site.Name, totalProcessed)
	return nil
}

// getSiteOrdersByDuration retrieves orders for a specific site within a date range
func getSiteOrdersByDuration(db *gorm.DB, siteID string, startTime, endTime time.Time) (map[string][]models.MerchantOrder, error) {
	// Get site from database
	var site models.Site
	if err := db.Where("id = ?", siteID).First(&site).Error; err != nil {
		return nil, err
	}

	result := map[string][]models.MerchantOrder{}
	token, err := token.GetTokenBySite(db, site, site.MainSource)
	if err != nil {
		return nil, err
	}
	if token == nil {
		return result, nil
	}
	if token.SiteID == "" || token.AccessToken == "" {
		return result, nil
	}

	iMerchant := merchant.NewMerchant(site.MainSource)
	mapStatusOrders, err := iMerchant.GetOrderListByDuration(&models.Token{
		SiteID:      token.SiteID,
		AccessToken: token.AccessToken,
		SiteType:    getSiteType(site.MainSource),
	}, startTime, endTime)
	if err != nil {
		return nil, err
	}

	for status, orders := range mapStatusOrders {
		for i := range orders {
			orders[i].Source = site.MainSource
			orders[i].MD5 = makeOrderMD5(orders[i])
			orders[i].Status = status
			result[status] = append(result[status], orders[i])
		}
	}
	return result, nil
}

// getSiteType returns the site type based on the main source
func getSiteType(mainSource string) string {
	switch mainSource {
	case "grab_mart", "be_mart", "shopee_fresh":
		return "MART"
	default:
		return "FOOD"
	}
}
