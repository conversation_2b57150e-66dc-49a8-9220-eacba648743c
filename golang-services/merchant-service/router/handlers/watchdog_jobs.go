package handlers

import (
	"encoding/json"
	"math"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/middlewares"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"
)

// CreateWatchDogJobRequest represents the request structure for creating a watchdog job
type CreateWatchDogJobRequest struct {
	JobName      string          `json:"job_name" binding:"required"`
	JobData      json.RawMessage `json:"job_data"`
	ScheduleAt   time.Time       `json:"schedule_at" binding:"required"`
	CronInterval int             `json:"cron_interval"` // in seconds, 0 for one-time job
}

// UpdateWatchDogJobRequest represents the request structure for updating a watchdog job
type UpdateWatchDogJobRequest struct {
	JobName      *string         `json:"job_name"`
	JobData      json.RawMessage `json:"job_data"`
	ScheduleAt   *time.Time      `json:"schedule_at"`
	CronInterval *int            `json:"cron_interval"`
	Status       *string         `json:"status"`
}

// WatchDogJobQueryParams represents query parameters for listing watchdog jobs
type WatchDogJobQueryParams struct {
	Page     int    `form:"page"`
	PageSize int    `form:"page_size"`
	Status   string `form:"status"`
	JobName  string `form:"job_name"`
}

// CreateWatchDogJob creates a new watchdog job
func CreateWatchDogJob(c *gin.Context) {
	db := middlewares.GetDB(c)

	var req CreateWatchDogJobRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success":       false,
			"error_code":    "INVALID_REQUEST",
			"error_message": err.Error(),
		})
		return
	}

	// Create the watchdog job
	job := models.WatchDogJob{
		JobName:      req.JobName,
		JobData:      req.JobData,
		ScheduleAt:   req.ScheduleAt,
		CronInterval: req.CronInterval,
		Status:       "pending",
		CreatedAt:    time.Now(),
	}

	if err := db.Create(&job).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success":       false,
			"error_code":    "DATABASE_ERROR",
			"error_message": "Failed to create watchdog job",
		})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"data":    job,
	})
}

// GetWatchDogJobs retrieves a list of watchdog jobs with pagination
func GetWatchDogJobs(c *gin.Context) {
	db := middlewares.GetDB(c)

	var params WatchDogJobQueryParams
	if err := c.ShouldBindQuery(&params); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success":       false,
			"error_code":    "INVALID_REQUEST",
			"error_message": err.Error(),
		})
		return
	}

	// Set default pagination values
	if params.Page <= 0 {
		params.Page = 1
	}
	if params.PageSize <= 0 {
		params.PageSize = 20
	}

	// Build query
	query := db.Model(&models.WatchDogJob{})

	if params.Status != "" {
		query = query.Where("status = ?", params.Status)
	}

	if params.JobName != "" {
		query = query.Where("job_name ILIKE ?", "%"+params.JobName+"%")
	}

	// Get total count
	var total int64
	if err := query.Count(&total).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success":       false,
			"error_code":    "DATABASE_ERROR",
			"error_message": "Failed to count watchdog jobs",
		})
		return
	}

	// Get paginated results
	var jobs []models.WatchDogJob
	offset := (params.Page - 1) * params.PageSize

	if err := query.Order("created_at DESC").
		Offset(offset).
		Limit(params.PageSize).
		Find(&jobs).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success":       false,
			"error_code":    "DATABASE_ERROR",
			"error_message": "Failed to retrieve watchdog jobs",
		})
		return
	}

	// Calculate pagination info
	totalPages := int(math.Ceil(float64(total) / float64(params.PageSize)))

	response := models.PaginationResponse{
		Success:    true,
		Data:       jobs,
		Total:      total,
		Page:       params.Page,
		PageSize:   params.PageSize,
		TotalPages: totalPages,
		HasNext:    params.Page < totalPages,
		HasPrev:    params.Page > 1,
	}

	c.JSON(http.StatusOK, response)
}

// GetWatchDogJob retrieves a specific watchdog job by ID
func GetWatchDogJob(c *gin.Context) {
	db := middlewares.GetDB(c)

	jobID := c.Param("id")
	id, err := strconv.ParseInt(jobID, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success":       false,
			"error_code":    "INVALID_ID",
			"error_message": "Invalid job ID",
		})
		return
	}

	var job models.WatchDogJob
	if err := db.First(&job, id).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success":       false,
			"error_code":    "JOB_NOT_FOUND",
			"error_message": "Watchdog job not found",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    job,
	})
}

// UpdateWatchDogJob updates a watchdog job
func UpdateWatchDogJob(c *gin.Context) {
	db := middlewares.GetDB(c)

	jobID := c.Param("id")
	id, err := strconv.ParseInt(jobID, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success":       false,
			"error_code":    "INVALID_ID",
			"error_message": "Invalid job ID",
		})
		return
	}

	var req UpdateWatchDogJobRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success":       false,
			"error_code":    "INVALID_REQUEST",
			"error_message": err.Error(),
		})
		return
	}

	// Find the job
	var job models.WatchDogJob
	if err := db.First(&job, id).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success":       false,
			"error_code":    "JOB_NOT_FOUND",
			"error_message": "Watchdog job not found",
		})
		return
	}

	// Build update map
	updates := make(map[string]interface{})

	if req.JobName != nil {
		updates["job_name"] = *req.JobName
	}
	if req.JobData != nil {
		updates["job_data"] = req.JobData
	}
	if req.ScheduleAt != nil {
		updates["schedule_at"] = *req.ScheduleAt
	}
	if req.CronInterval != nil {
		updates["cron_interval"] = *req.CronInterval
	}
	if req.Status != nil {
		updates["status"] = *req.Status
	}

	// Update the job
	if err := db.Model(&job).Updates(updates).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success":       false,
			"error_code":    "DATABASE_ERROR",
			"error_message": "Failed to update watchdog job",
		})
		return
	}

	// Reload the job to get updated data
	if err := db.First(&job, id).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success":       false,
			"error_code":    "DATABASE_ERROR",
			"error_message": "Failed to reload updated job",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    job,
	})
}

// DeleteWatchDogJob deletes a watchdog job
func DeleteWatchDogJob(c *gin.Context) {
	db := middlewares.GetDB(c)

	jobID := c.Param("id")
	id, err := strconv.ParseInt(jobID, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success":       false,
			"error_code":    "INVALID_ID",
			"error_message": "Invalid job ID",
		})
		return
	}

	// Check if job exists
	var job models.WatchDogJob
	if err := db.First(&job, id).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success":       false,
			"error_code":    "JOB_NOT_FOUND",
			"error_message": "Watchdog job not found",
		})
		return
	}

	// Delete the job
	if err := db.Delete(&job).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success":       false,
			"error_code":    "DATABASE_ERROR",
			"error_message": "Failed to delete watchdog job",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Watchdog job deleted successfully",
	})
}

// WatchDogJobHistoryQueryParams represents query parameters for listing watchdog job history
type WatchDogJobHistoryQueryParams struct {
	Page     int    `form:"page"`
	PageSize int    `form:"page_size"`
	JobID    int64  `form:"job_id"`
	JobName  string `form:"job_name"`
	Status   string `form:"status"`
}

// GetWatchDogJobHistory retrieves the execution history of watchdog jobs
func GetWatchDogJobHistory(c *gin.Context) {
	db := middlewares.GetDB(c)

	var params WatchDogJobHistoryQueryParams
	if err := c.ShouldBindQuery(&params); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success":       false,
			"error_code":    "INVALID_REQUEST",
			"error_message": err.Error(),
		})
		return
	}

	// Set default pagination values
	if params.Page <= 0 {
		params.Page = 1
	}
	if params.PageSize <= 0 {
		params.PageSize = 20
	}

	// Build query
	query := db.Model(&models.WatchDogJobHistory{})

	if params.JobID > 0 {
		query = query.Where("job_id = ?", params.JobID)
	}

	if params.JobName != "" {
		query = query.Where("job_name ILIKE ?", "%"+params.JobName+"%")
	}

	if params.Status != "" {
		query = query.Where("status = ?", params.Status)
	}

	// Get total count
	var total int64
	if err := query.Count(&total).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success":       false,
			"error_code":    "DATABASE_ERROR",
			"error_message": "Failed to count job history",
		})
		return
	}

	// Get paginated results
	var histories []models.WatchDogJobHistory
	offset := (params.Page - 1) * params.PageSize

	if err := query.Order("created_at DESC").
		Offset(offset).
		Limit(params.PageSize).
		Find(&histories).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success":       false,
			"error_code":    "DATABASE_ERROR",
			"error_message": "Failed to retrieve job history",
		})
		return
	}

	// Calculate pagination info
	totalPages := int(math.Ceil(float64(total) / float64(params.PageSize)))

	response := models.PaginationResponse{
		Success:    true,
		Data:       histories,
		Total:      total,
		Page:       params.Page,
		PageSize:   params.PageSize,
		TotalPages: totalPages,
		HasNext:    params.Page < totalPages,
		HasPrev:    params.Page > 1,
	}

	c.JSON(http.StatusOK, response)
}
