package handlers

import (
	"encoding/json"
	"log"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/middlewares"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/rabbitmq"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/utils"
	"github.com/spf13/cast"
	"gorm.io/gorm"
)

// CronWatchDogJobsHandler handles the API endpoint for processing watchdog jobs
func CronWatchDogJobsHandler(c *gin.Context) {
	db := middlewares.GetDB(c)
	rb := middlewares.GetRabbitMQ(c)

	// Find jobs that are scheduled to run (schedule_at <= now and status = 'pending')
	var jobs []models.WatchDogJob
	now := time.Now()

	if err := db.Where("schedule_at <= ? AND status = ?", now, "pending").
		Find(&jobs).Error; err != nil {
		log.Printf("Error finding watchdog jobs: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"success":       false,
			"error_code":    "DATABASE_ERROR",
			"error_message": "Failed to retrieve watchdog jobs",
		})
		return
	}

	if len(jobs) == 0 {
		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"message": "No jobs to process",
			"data":    []string{},
		})
		return
	}

	// Process jobs
	processedJobs := []string{}
	tasks := []*utils.Task{}

	for _, job := range jobs {
		jobCopy := job // Create a copy for the closure
		tasks = append(tasks, utils.NewTask(func() error {
			return processWatchDogJob(db, rb, jobCopy)
		}))
		processedJobs = append(processedJobs, jobCopy.JobName)
	}

	// Execute tasks in parallel
	pool := utils.NewPool(tasks, 5)
	pool.Run()

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Watchdog jobs processed",
		"data":    processedJobs,
	})
}

// processWatchDogJob processes a single watchdog job
func processWatchDogJob(db *gorm.DB, rb *rabbitmq.RabbitClient, job models.WatchDogJob) error {
	startTime := time.Now()

	// Create job history record
	history := models.WatchDogJobHistory{
		JobID:     job.ID,
		JobName:   job.JobName,
		JobData:   job.JobData,
		StartedAt: startTime,
		Status:    "running",
	}

	if err := db.Create(&history).Error; err != nil {
		log.Printf("Error creating job history: %v", err)
		return err
	}

	// Update job status to running and set started_at
	updates := map[string]interface{}{
		"status":     "running",
		"started_at": &startTime,
		"message":    nil,
	}

	if err := db.Model(&job).Updates(updates).Error; err != nil {
		log.Printf("Error updating job status to running: %v", err)
		return err
	}

	// Process the job based on job_name
	var jobError error
	var message string

	switch job.JobName {
	case "cron_site_order":
		jobError = processSiteOrderJob(rb, job.JobData)
		message = "Site order job processed"
	case "cron_site_ecom_order":
		jobError = processSiteEcomOrderJob(rb, job.JobData)
		message = "Site ecom order job processed"
	case "cron_site_finances":
		jobError = processSiteFinancesJob(rb, job.JobData)
		message = "Site finances job processed"
	case "cron_site_feedbacks":
		jobError = processSiteFeedbacksJob(rb, job.JobData)
		message = "Site feedbacks job processed"
	case "cron_site_incidents":
		jobError = processSiteIncidentsJob(rb, job.JobData)
		message = "Site incidents job processed"
	case "cron_order_feedbacks":
		jobError = processOrderFeedbacksJob(rb, job.JobData)
		message = "Order feedbacks job processed"
	default:
		jobError = processGenericJob(rb, job.JobName, job.JobData)
		message = "Generic job processed"
	}

	completedAt := time.Now()

	// Determine final status and next schedule
	var finalStatus string
	var nextScheduleAt *time.Time

	if jobError != nil {
		finalStatus = "failed"
		message = jobError.Error()
		log.Printf("Job %s failed: %v", job.JobName, jobError)
	} else {
		if job.CronInterval > 0 {
			// Recurring job - set next schedule and keep status as pending
			finalStatus = "pending"
			nextSchedule := job.ScheduleAt.Add(time.Duration(job.CronInterval) * time.Second)
			nextScheduleAt = &nextSchedule
		} else {
			// One-time job - mark as completed
			finalStatus = "completed"
		}
	}

	// Update job with final status
	jobUpdates := map[string]interface{}{
		"status":       finalStatus,
		"completed_at": &completedAt,
		"message":      &message,
	}

	if nextScheduleAt != nil {
		jobUpdates["schedule_at"] = *nextScheduleAt
		jobUpdates["started_at"] = nil // Reset started_at for next run
	}

	if err := db.Model(&job).Updates(jobUpdates).Error; err != nil {
		log.Printf("Error updating job final status: %v", err)
		return err
	}

	// Update job history
	historyStatus := "success"
	if jobError != nil {
		historyStatus = "failed"
	}

	historyUpdates := map[string]interface{}{
		"completed_at": &completedAt,
		"status":       historyStatus,
		"message":      &message,
	}

	if err := db.Model(&history).Updates(historyUpdates).Error; err != nil {
		log.Printf("Error updating job history: %v", err)
		return err
	}

	return nil
}

// processSiteOrderJob processes site order jobs
func processSiteOrderJob(rb *rabbitmq.RabbitClient, jobData json.RawMessage) error {
	var data map[string]interface{}
	if err := json.Unmarshal(jobData, &data); err != nil {
		return err
	}

	siteID := cast.ToString(data["site_id"])
	if siteID == "" {
		return nil // Skip if no site_id
	}

	return rb.Publish("cron_site_order", []byte(siteID))
}

// processSiteEcomOrderJob processes site ecom order jobs
func processSiteEcomOrderJob(rb *rabbitmq.RabbitClient, jobData json.RawMessage) error {
	var data map[string]interface{}
	if err := json.Unmarshal(jobData, &data); err != nil {
		return err
	}

	siteID := cast.ToString(data["site_id"])
	if siteID == "" {
		return nil // Skip if no site_id
	}

	return rb.Publish("cron_site_ecom_order", []byte(siteID))
}

// processSiteFinancesJob processes site finances jobs
func processSiteFinancesJob(rb *rabbitmq.RabbitClient, jobData json.RawMessage) error {
	return rb.Publish("cron_site_finances", jobData)
}

// processSiteFeedbacksJob processes site feedbacks jobs
func processSiteFeedbacksJob(rb *rabbitmq.RabbitClient, jobData json.RawMessage) error {
	return rb.Publish("cron_site_order_feedbacks", jobData)
}

// processSiteIncidentsJob processes site incidents jobs
func processSiteIncidentsJob(rb *rabbitmq.RabbitClient, jobData json.RawMessage) error {
	return rb.Publish("cron_site_order_incident_list", jobData)
}

// processOrderFeedbacksJob processes order feedbacks jobs
func processOrderFeedbacksJob(rb *rabbitmq.RabbitClient, jobData json.RawMessage) error {
	var data map[string]interface{}
	if err := json.Unmarshal(jobData, &data); err != nil {
		return err
	}

	siteID := cast.ToString(data["site_id"])
	if siteID == "" {
		return nil // Skip if no site_id
	}

	// Create message with site_id and limit
	message := map[string]interface{}{
		"site_id": siteID,
		"limit":   cast.ToInt(data["limit"]),
	}

	messageJSON, err := json.Marshal(message)
	if err != nil {
		return err
	}

	return rb.Publish("cron_site_order_feedbacks", messageJSON)
}

// processGenericJob processes generic jobs by publishing to a queue with the job name
func processGenericJob(rb *rabbitmq.RabbitClient, jobName string, jobData json.RawMessage) error {
	return rb.Publish(jobName, jobData)
}
