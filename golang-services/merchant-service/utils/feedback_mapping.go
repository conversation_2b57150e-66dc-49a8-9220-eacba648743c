package utils

import (
	"encoding/json"
	"strconv"
	"strings"
	"time"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"
	"github.com/tidwall/gjson"
)

// MapOrderFeedback maps feedback data from different merchant sources to OrderFeedback model
func MapOrderFeedback(source string, data any) (*models.OrderFeedback, error) {
	dataBytes, err := json.Marshal(data)
	if err != nil {
		return nil, err
	}

	switch source {
	case "shopee", "shopee_fresh", "shopee_food":
		return mapShopeeFeedback(dataBytes)
	case "grab", "grab_mart":
		return mapGrabFeedback(dataBytes)
	case "be", "be_mart":
		return mapBEFeedback(dataBytes)
	case "gojek":
		return mapGojekFeedback(dataBytes)
	default:
		return nil, nil
	}
}

// mapShopeeFeedback maps Shopee feedback data
func mapShopeeFeedback(dataBytes []byte) (*models.OrderFeedback, error) {
	parsed := gjson.ParseBytes(dataBytes)

	// Extract comments and reasons
	var comments []string
	if parsed.Get("comments").Exists() {
		for _, comment := range parsed.Get("comments").Array() {
			comments = append(comments, comment.String())
		}
	}
	if parsed.Get("reasons").Exists() {
		for _, reason := range parsed.Get("reasons").Array() {
			comments = append(comments, reason.String())
		}
	}

	// Extract images
	var images []string
	if parsed.Get("photos").Exists() {
		for _, photo := range parsed.Get("photos").Array() {
			if photoURL := photo.Get("photo_url").String(); photoURL != "" {
				images = append(images, photoURL)
			}
		}
	}

	feedback := &models.OrderFeedback{
		RefID:         parsed.Get("id").String(),
		OrderID:       parsed.Get("order_code").String(),
		Rating:        int(parsed.Get("rating_star").Int() - 100), // Shopee uses 101-105 for 1-5 stars
		Comment:       strings.Join(comments, ", "),
		CustomerName:  parsed.Get("user_info.name").String(),
		CreatedAtUnix: parsed.Get("create_time").Int(),
		Data:          json.RawMessage(dataBytes),
	}

	return feedback, nil
}

// mapGrabFeedback maps Grab feedback data
func mapGrabFeedback(dataBytes []byte) (*models.OrderFeedback, error) {
	parsed := gjson.ParseBytes(dataBytes)

	// Parse created date
	var createdAtUnix int64
	if createdAt := parsed.Get("createdAt").String(); createdAt != "" {
		if t, err := time.Parse(time.RFC3339, createdAt); err == nil {
			createdAtUnix = t.Unix()
		}
	}

	rating := int(parsed.Get("rating").Int())
	if rating == 0 {
		rating = 5 // Default rating if not provided
	}

	feedback := &models.OrderFeedback{
		RefID:         parsed.Get("bookingCode").String(),
		OrderID:       parsed.Get("orderID").String(),
		Rating:        rating,
		Comment:       parsed.Get("description").String(),
		CustomerName:  parsed.Get("eaterName").String(),
		CreatedAtUnix: createdAtUnix,
		Data:          json.RawMessage(dataBytes),
	}

	return feedback, nil
}

// mapBEFeedback maps BE feedback data
func mapBEFeedback(dataBytes []byte) (*models.OrderFeedback, error) {
	parsed := gjson.ParseBytes(dataBytes)

	// Parse rated_at date (format: "HH:mm DD/MM/YYYY")
	var createdAtUnix int64
	if ratedAt := parsed.Get("rated_at").String(); ratedAt != "" {
		if t, err := time.Parse("15:04 02/01/2006", ratedAt); err == nil {
			createdAtUnix = t.Unix()
		}
	}

	rating := int(parsed.Get("rating").Int())
	if rating == 0 {
		rating = 5 // Default rating if not provided
	}

	// Extract images
	var images []string
	if parsed.Get("images").Exists() {
		for _, image := range parsed.Get("images").Array() {
			images = append(images, image.String())
		}
	}

	feedback := &models.OrderFeedback{
		RefID:         strconv.FormatInt(parsed.Get("rating_id").Int(), 10),
		OrderID:       strconv.FormatInt(parsed.Get("order_id").Int(), 10),
		Rating:        rating,
		Comment:       parsed.Get("feedback").String(),
		CustomerName:  parsed.Get("user_name").String(),
		CreatedAtUnix: createdAtUnix,
		Data:          json.RawMessage(dataBytes),
	}

	return feedback, nil
}

// mapGojekFeedback maps Gojek feedback data
func mapGojekFeedback(dataBytes []byte) (*models.OrderFeedback, error) {
	parsed := gjson.ParseBytes(dataBytes)

	// Parse created date
	var createdAtUnix int64
	if createdAt := parsed.Get("created_at").String(); createdAt != "" {
		if t, err := time.Parse(time.RFC3339, createdAt); err == nil {
			createdAtUnix = t.Unix()
		}
	}

	rating := int(parsed.Get("rating").Int())
	if rating == 0 {
		rating = 5 // Default rating if not provided
	}

	orderID := parsed.Get("order.id").String()

	feedback := &models.OrderFeedback{
		RefID:         orderID,
		OrderID:       orderID,
		Rating:        rating,
		Comment:       parsed.Get("text").String(),
		CustomerName:  "", // Gojek doesn't provide customer name in feedback
		CreatedAtUnix: createdAtUnix,
		Data:          json.RawMessage(dataBytes),
	}

	return feedback, nil
}

// GetTokenBySite retrieves token for a specific site and source
func GetTokenBySite(siteID, source string) (*models.Token, error) {
	// This function should be implemented to get token from database
	// For now, return nil to indicate no token found
	return nil, nil
}
