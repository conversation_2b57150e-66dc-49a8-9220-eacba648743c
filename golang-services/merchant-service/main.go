package main

import (
	"log"
	"os"
	"runtime/debug"
	"time"

	"github.com/ThreeDotsLabs/watermill/message"
	"github.com/gin-gonic/gin"
	"github.com/nexdorvn/nexpos-backend/golang-services/merchant-service/router"
	"github.com/nexdorvn/nexpos-backend/golang-services/merchant-service/router/handlers"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/middlewares"
	"github.com/urfave/cli"
)

func main() {
	app := cli.NewApp()
	app.Name = "merchant-service"
	app.Usage = "Merchant service"
	app.Version = "1.0.0"
	app.Commands = []cli.Command{
		{
			Name:      "Start",
			ShortName: "start",
			Usage:     "Start service",
			Flags: []cli.Flag{
				cli.StringFlag{
					Name:   "port",
					Usage:  "Port the server listens to",
					EnvVar: "PORT",
					Value:  "3000",
				},
			},
			Action: func(c *cli.Context) error {
				port := c.String("port")

				r := gin.New()
				r.Use(middlewares.MyCors())

				r.Use(middlewares.RabbitMQMiddleware())
				r.Use(middlewares.GormMiddleware())
				r.Use(middlewares.GoogleStorageMiddleware())
				r.Use(middlewares.RedisMiddleware())
				r.Use(middlewares.ErrorLocalizeMiddleware())

				router.LoadHandlers(r)

				r.Use(gin.Logger())
				r.Use(gin.Recovery())

				// if os.Getenv("NODE_ENV") == "prod" {
				go listenMessages()
				// }

				return r.Run(":" + port)
			},
		},
	}

	if err := app.Run(os.Args); err != nil {
		log.Fatalf("server failed with error %v", err)
	}
}

func listenMessages() {
	defer func() {
		if r := recover(); r != nil {
			log.Printf("PANIC in listenMessages: %v\nStack trace:\n%s", r, debug.Stack())
			// Restart message listening after a delay
			time.Sleep(5 * time.Second)
			log.Println("Restarting message listener...")
			go listenMessages()
		}
	}()

	client := middlewares.NewRabbitMQ()
	db := middlewares.NewDB()

	log.Println("Starting message listeners...")

	// Subscribe to all required message queues with recovery using SDK functions
	client.SubscribeWithRecovery("cron_site_order", "merchant_service", func(m *message.Message) {
		log.Printf("Received cron_site_order message: %s", string(m.Payload))
		if err := handlers.CronSiteOrders(client, db, string(m.Payload)); err != nil {
			log.Printf("Error processing cron_site_order: %v", err)
		}
	})

	client.SubscribeWithRecovery("cron_site_ecom_order", "merchant_service", func(m *message.Message) {
		log.Printf("Received cron_site_ecom_order message: %s", string(m.Payload))
		// TODO: Implement handler
	})

	client.SubscribeWithRecovery("cron_site_orders_by_days", "merchant_service", func(m *message.Message) {
		log.Printf("Received cron_site_orders_by_days message: %s", string(m.Payload))
		if err := handlers.ProcessSiteOrdersByDays(client, db, m.Payload); err != nil {
			log.Printf("Error processing cron_site_orders_by_days: %v", err)
		}
	})

	client.SubscribeWithRecovery("cron_site_finances", "merchant_service", func(m *message.Message) {
		log.Printf("Received cron_site_finances message: %s", string(m.Payload))
		if err := handlers.ProcessSiteFinances(client, db, m.Payload); err != nil {
			log.Printf("Error processing cron_site_finances: %v", err)
		}
	})

	client.SubscribeWithRecovery("cron_site_order_feedbacks", "merchant_service", func(m *message.Message) {
		log.Printf("Received cron_site_order_feedbacks message: %s", string(m.Payload))
		if err := handlers.ProcessFeedbacks(client, db, m.Payload); err != nil {
			log.Printf("Error processing cron_site_order_feedbacks: %v", err)
		}
	})

	client.SubscribeWithRecovery("cron_site_order_incident_list", "merchant_service", func(m *message.Message) {
		log.Printf("Received cron_site_order_incident_list message: %s", string(m.Payload))
		if err := handlers.ProcessIncidents(client, db, m.Payload); err != nil {
			log.Printf("Error processing cron_site_order_incident_list: %v", err)
		}
	})

	client.SubscribeWithRecovery("cron_watchdog_jobs", "merchant_service", func(m *message.Message) {
		log.Printf("Received cron_watchdog_jobs message: %s", string(m.Payload))
		// This message can be used to trigger watchdog job processing externally
		// The actual processing is done via the HTTP endpoint
	})

	log.Println("All message listeners started successfully")

	// Block forever
	select {}
}
