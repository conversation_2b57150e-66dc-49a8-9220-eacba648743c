# WatchDog Jobs System

The WatchDog Jobs system provides a flexible way to schedule and manage recurring or one-time jobs in the merchant-service.

## Features

- **Scheduled Jobs**: Jobs can be scheduled to run at specific times
- **Recurring Jobs**: Support for cron-like intervals (in seconds)
- **Job History**: Complete execution history with status tracking
- **Status Management**: Track job states (pending, running, completed, failed)
- **Flexible Job Data**: Store arbitrary JSON data for each job
- **REST API**: Full CRUD operations for job management

## Database Models

### WatchDogJob
```go
type WatchDogJob struct {
    ID           int64           `json:"id"`
    JobName      string          `json:"job_name"`
    JobData      json.RawMessage `json:"job_data"`
    ScheduleAt   time.Time       `json:"schedule_at"`
    StartedAt    *time.Time      `json:"started_at"`
    CompletedAt  *time.Time      `json:"completed_at"`
    CronInterval int             `json:"cron_interval"` // in seconds, 0 for one-time job
    Status       string          `json:"status"`        // pending, running, completed, failed
    Message      *string         `json:"message"`
    CreatedAt    time.Time       `json:"created_at"`
    UpdatedAt    *time.Time      `json:"updated_at"`
}
```

### WatchDogJobHistory
```go
type WatchDogJobHistory struct {
    ID          int64           `json:"id"`
    JobID       int64           `json:"job_id"`
    JobName     string          `json:"job_name"`
    JobData     json.RawMessage `json:"job_data"`
    StartedAt   time.Time       `json:"started_at"`
    CompletedAt *time.Time      `json:"completed_at"`
    Status      string          `json:"status"`
    Message     *string         `json:"message"`
    CreatedAt   time.Time       `json:"created_at"`
    UpdatedAt   *time.Time      `json:"updated_at"`
}
```

## API Endpoints

### Job Management
- `POST /v1/merchant-service/watchdog/jobs` - Create a new job
- `GET /v1/merchant-service/watchdog/jobs` - List jobs with pagination
- `GET /v1/merchant-service/watchdog/jobs/:id` - Get specific job
- `PUT /v1/merchant-service/watchdog/jobs/:id` - Update job
- `DELETE /v1/merchant-service/watchdog/jobs/:id` - Delete job
- `GET /v1/merchant-service/watchdog/jobs/history` - Get job execution history

### Cron Processing
- `GET /v1/merchant-service/cron/watchdog` - Process scheduled jobs

## Usage Examples

### Creating a One-time Job
```bash
curl -X POST http://localhost:3000/v1/merchant-service/watchdog/jobs \
  -H "Content-Type: application/json" \
  -d '{
    "job_name": "cron_site_order",
    "job_data": {"site_id": "12345"},
    "schedule_at": "2024-01-15T10:00:00Z",
    "cron_interval": 0
  }'
```

### Creating a Recurring Job (every 30 minutes)
```bash
curl -X POST http://localhost:3000/v1/merchant-service/watchdog/jobs \
  -H "Content-Type: application/json" \
  -d '{
    "job_name": "cron_site_order",
    "job_data": {"site_id": "12345"},
    "schedule_at": "2024-01-15T10:00:00Z",
    "cron_interval": 1800
  }'
```

### Listing Jobs
```bash
curl "http://localhost:3000/v1/merchant-service/watchdog/jobs?page=1&page_size=20&status=pending"
```

### Processing Scheduled Jobs
```bash
curl "http://localhost:3000/v1/merchant-service/cron/watchdog"
```

## Job Processing Logic

1. **Job Selection**: The system finds all jobs where `schedule_at <= now` and `status = 'pending'`
2. **Execution**: Each job is processed based on its `job_name`
3. **Status Update**: 
   - For recurring jobs (`cron_interval > 0`): Status remains 'pending', `schedule_at` is updated
   - For one-time jobs (`cron_interval = 0`): Status changes to 'completed'
   - For failed jobs: Status changes to 'failed'
4. **History**: Each execution creates a record in `WatchDogJobHistory`

## Supported Job Types

The system currently supports these job types:
- `cron_site_order` - Process site orders
- `cron_site_ecom_order` - Process site ecom orders  
- `cron_site_finances` - Process site finances
- `cron_site_feedbacks` - Process site feedbacks
- `cron_site_incidents` - Process site incidents
- Generic jobs (publishes to RabbitMQ queue with job name)

## Job Data Format

Job data should be valid JSON that matches the expected format for each job type:

### Site Order Jobs
```json
{
  "site_id": "12345"
}
```

### Site Finance Jobs
```json
{
  "site_id": "12345",
  "from": "2024-01-01T00:00:00Z",
  "to": "2024-01-31T23:59:59Z",
  "order_ids": ["order1", "order2"]
}
```

## Error Handling

- Jobs that fail will have their status set to 'failed'
- Error messages are stored in the `message` field
- Failed jobs do not automatically retry (manual intervention required)
- Job history preserves all execution attempts

## Monitoring

- Use the job history endpoint to monitor execution patterns
- Check job status to identify failed or stuck jobs
- Monitor execution times via `started_at` and `completed_at` timestamps
