# MapOrder Testing Guide

This guide explains how to test the `mapping.MapOrder` function with database queries and various order sources.

## Overview

The `mapping.MapOrder` function is responsible for converting `MerchantOrder` objects (from external platforms like Shopee, Grab, BE) into standardized `Order` objects with proper `DataMapping` structures.

## Test Files Created

### 1. `router/handlers/cron_site_orders_test.go`
Comprehensive test suite for the MapOrder functionality including:
- **TestMapOrderFunctionality**: Tests MapOrder with sample data from different sources
- **TestQueryOrdersFromDatabase**: Queries real orders from database and tests re-mapping
- **TestMapOrderWithDifferentSources**: Tests MapOrder with various order sources
- **TestMapOrderWithRealDatabaseData**: Tests MapOrder with actual database orders
- **TestMapOrderDataConsistency**: Verifies MapOrder produces consistent results
- **BenchmarkMapOrder**: Performance benchmark for MapOrder function

### 2. `test_maporder.sh`
Shell script to run all MapOrder tests with proper environment setup.

### 3. `examples/maporder_example.go`
Standalone example demonstrating how to use MapOrder function with:
- Sample order data
- Different order sources (<PERSON>ee, <PERSON>rab, BE, Local)
- Real database orders (if database connection available)

## Running the Tests

### Prerequisites

1. **Database Connection**: Set the `POSTGRESQL_URI` environment variable:
   ```bash
   export POSTGRESQL_URI="host=localhost user=your_user password=your_password dbname=your_db port=5432 sslmode=disable"
   ```

2. **Go Dependencies**: Ensure all dependencies are installed:
   ```bash
   cd golang-services/merchant-service
   go mod tidy
   ```

### Running Tests

#### Option 1: Use the Test Script (Recommended)
```bash
cd golang-services/merchant-service
./test_maporder.sh
```

#### Option 2: Run Individual Tests
```bash
cd golang-services/merchant-service

# Test MapOrder with sample data
go test -v ./router/handlers -run TestMapOrderFunctionality

# Test with real database orders
go test -v ./router/handlers -run TestQueryOrdersFromDatabase

# Test with different sources
go test -v ./router/handlers -run TestMapOrderWithDifferentSources

# Test with real database data
go test -v ./router/handlers -run TestMapOrderWithRealDatabaseData

# Test data consistency
go test -v ./router/handlers -run TestMapOrderDataConsistency

# Run performance benchmark
go test -v ./router/handlers -bench BenchmarkMapOrder -run ^$

# Run all MapOrder tests
go test -v ./router/handlers -run ".*MapOrder.*"
```

#### Option 3: Run the Example
```bash
cd golang-services/merchant-service
go run examples/maporder_example.go
```

## Test Scenarios

### 1. Sample Data Testing
Tests MapOrder with predefined sample data for each order source:
- **Shopee Food**: Tests with typical Shopee order structure
- **Grab**: Tests with Grab order format
- **BE**: Tests with BE order format  
- **Local**: Tests with local order format

### 2. Database Integration Testing
- Queries actual orders from the database
- Re-maps existing orders using MapOrder
- Verifies mapping consistency
- Tests with orders from different sources

### 3. Source-Specific Testing
Tests MapOrder behavior with:
- Known order sources (shopee_food, grab, be, local)
- Unknown/unsupported sources
- Edge cases and error handling

### 4. Performance Testing
- Benchmarks MapOrder function performance
- Measures execution time and memory usage
- Helps identify performance bottlenecks

## Understanding Test Output

### Successful Test Output
```
=== RUN   TestMapOrderFunctionality/Shopee_Food_Order
    Order SHOPEE_20240101_123456789 mapped successfully:
      - Source: shopee_food
      - Customer: Jane Customer
      - Total: 150000.00
      - Commission: 15000.00
--- PASS: TestMapOrderFunctionality/Shopee_Food_Order (0.00s)
```

### Database Test Output
```
=== RUN   TestQueryOrdersFromDatabase
    Found 5 orders in database
    Order 1: ID=SHOPEE_123, Source=shopee_food, Status=FINISH
    Successfully re-mapped order SHOPEE_123 from source shopee_food
--- PASS: TestQueryOrdersFromDatabase (0.01s)
```

### Benchmark Output
```
BenchmarkMapOrder-8   	   10000	    120.5 ns/op	      64 B/op	       2 allocs/op
```

## Troubleshooting

### Database Connection Issues
If you see "Skipping test due to database connection error":
1. Verify `POSTGRESQL_URI` is set correctly
2. Check database connectivity
3. Ensure database contains order data

### Missing Dependencies
If you see import errors:
```bash
cd golang-services
go mod tidy
```

### Test Failures
1. Check that the `mapping` package is properly imported
2. Verify database schema matches expected `Order` model
3. Ensure test data format matches expected order structures

## Key Test Functions

### `setupTestDB()`
Creates database connection for testing. Uses `POSTGRESQL_URI` environment variable or falls back to default test connection.

### `TestMapOrderFunctionality()`
Core test that verifies MapOrder works correctly with sample data from different order sources.

### `TestQueryOrdersFromDatabase()`
Queries real orders from database and tests re-mapping functionality.

### `TestMapOrderWithRealDatabaseData()`
Advanced test that works with actual database orders, testing MapOrder with real-world data.

## Expected Results

When tests pass successfully, you should see:
1. ✅ MapOrder correctly processes different order sources
2. ✅ Database orders can be re-mapped successfully  
3. ✅ DataMapping contains expected fields for each source
4. ✅ Performance benchmarks show reasonable execution times
5. ✅ Consistency tests verify reliable mapping results

## Integration with Existing Code

The selected code in your file:
```go
mapOrder := mapping.MapOrder(&order)
```

This is exactly what the tests are validating. The tests ensure that:
- The `mapping.MapOrder()` function works correctly
- Different order sources are handled properly
- The resulting mapped order has correct structure
- Database integration works as expected

Use these tests to verify your MapOrder implementation and catch any issues before deployment.
