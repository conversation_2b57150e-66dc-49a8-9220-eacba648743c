#!/bin/bash

# Test script for MapOrder functionality
# This script helps run the MapOrder tests with proper environment setup

echo "=== MapOrder Test Runner ==="
echo "Testing MapOrder functionality with database queries"
echo ""

# Check if POSTGRESQL_URI is set
if [ -z "$POSTGRESQL_URI" ]; then
    echo "Warning: POSTGRESQL_URI environment variable is not set"
    echo "Tests will use default test database connection"
    echo "To use your actual database, set POSTGRESQL_URI environment variable"
    echo ""
    echo "Example:"
    echo "export POSTGRESQL_URI=\"host=localhost user=your_user password=your_password dbname=your_db port=5432 sslmode=disable\""
    echo ""
fi

# Navigate to the correct directory
cd "$(dirname "$0")"

echo "Current directory: $(pwd)"
echo ""

# Run the specific test functions
echo "=== Running MapOrder Functionality Tests ==="
go test -v ./router/handlers -run TestMapOrderFunctionality

echo ""
echo "=== Running Database Query Tests ==="
go test -v ./router/handlers -run TestQueryOrdersFromDatabase

echo ""
echo "=== Running Different Sources Tests ==="
go test -v ./router/handlers -run TestMapOrderWithDifferentSources

echo ""
echo "=== Running Performance Benchmark ==="
go test -v ./router/handlers -bench BenchmarkMapOrder -run ^$

echo ""
echo "=== Running All MapOrder Tests ==="
go test -v ./router/handlers -run ".*MapOrder.*"

echo ""
echo "=== Test Summary ==="
echo "All MapOrder tests completed!"
echo ""
echo "To run individual tests:"
echo "  go test -v ./router/handlers -run TestMapOrderFunctionality"
echo "  go test -v ./router/handlers -run TestQueryOrdersFromDatabase"
echo "  go test -v ./router/handlers -run TestMapOrderWithDifferentSources"
echo ""
echo "To run with your database connection:"
echo "  export POSTGRESQL_URI=\"your_connection_string\""
echo "  ./test_maporder.sh"
