package router

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/nexdorvn/nexpos-backend/golang-services/notification-service/t2s"
)

func HandleText2Speech(c *gin.Context) {
	text := c.Query("text")
	if text == "" {
		c.<PERSON>SO<PERSON>(http.StatusOK, gin.H{
			"success": false,
			"message": "Missing text query parameter",
		})
		return
	}
	audioBuffer, err := t2s.TextToSpeech(text)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	c.<PERSON>(http.StatusOK, "audio/mpeg", audioBuffer)
}
