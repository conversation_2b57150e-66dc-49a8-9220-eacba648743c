package main

import (
	"net/http"
	"os"
	"strings"

	"github.com/nexdorvn/nexpos-backend/golang-services/saas-notification-service/router"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis/v8"
)

func main() {
	rdb := redis.NewClient(&redis.Options{
		Addr:     strings.ReplaceAll(os.Getenv("REDIS_URI"), "redis://", ""),
		Password: os.Getenv("REDIS_PASSWORD"),
		DB:       3,
	})

	r := gin.Default()

	config := cors.DefaultConfig()
	config.AllowAllOrigins = true
	config.AllowHeaders = []string{"Origin", "Content-Length", "Content-Type", "Authorization"}
	config.AllowMethods = []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"}
	r.Use(cors.New(config))

	r.GET("/api/health", func(c *gin.Context) {
		c.<PERSON>(http.StatusOK, gin.H{
			"success": true,
		})
	})

	r.GET("/ws", router.HandleWebSocket)
	r.GET("/t2s", router.HandleText2Speech)

	go router.Broadcaster()
	go router.RedisListener(rdb)

	r.Run(":3000")
}
