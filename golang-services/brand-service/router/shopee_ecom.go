package router

import (
	"net/http"

	"github.com/gin-gonic/gin"
	shopee_ecom "github.com/nexdorvn/nexpos-backend/golang-services/sdk/merchant/shopee-ecom"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"
)

// TestShopeeEcomOrders tests retrieving orders from Shopee E-commerce
func TestShopeeEcomOrders(c *gin.Context) {
	// Create a new Shopee client
	client := shopee_ecom.NewShopeeEcomClient()

	// Create a test token
	token := &models.Token{
		AccessToken: "44467158484f4e7159454e6374505366",
		SiteID:      "225367070",
	}

	// Get orders from the last 24 hours
	// endTime := time.Now()
	// startTime := endTime.Add(-240 * time.Hour)

	// Call GetOrderListByDuration
	// orders, err := client.GetOrderListByDuration(token, startTime, endTime)
	orders, err := client.GetOrderListV2(token)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// get otder details for each order
	orderDetails, err := client.GetOrderDetail(token, "250615MAWK0XXT")

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    orders,
		"details": orderDetails,
	})
}
