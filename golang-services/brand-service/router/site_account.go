// order-service/router/site.go
package router

import (
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/delivery/ahamove"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/delivery/grab_express"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/gmap"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/merchant"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/middlewares"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"

	"github.com/gin-gonic/gin"
)

type LoginAppAccountResponse struct {
	Username    string `json:"username"`
	Password    string `json:"password"`
	SiteID      string `json:"site_id"`
	AccessToken string `json:"access_token"`
}

func LoginQComAccount(c *gin.Context) {
	appID := c.Param("app_id")

	var req activeAccountRequest
	var err error
	if err = c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "invalid_request",
		})
		return
	}

	// Get the appropriate merchant client using the interface
	client := merchant.NewMerchant(appID)
	if client == nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "merchant_client_not_found",
		})
		return
	}

	// Create token with user credentials
	token := &models.Token{
		Username: req.Username,
		Password: req.Password,
	}

	// Get store list from merchant
	storeList, err := client.GetStoreListByAuth(token)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	if len(storeList) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "no_store_found",
		})
		return
	}

	// Return store list
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    storeList,
	})
}

type activeAccountRequest struct {
	Username     string   `json:"username"`
	Password     string   `json:"password"`
	ServiceTypes []string `json:"service_types"`
}

func ActiveAppAccount(c *gin.Context) {
	db := middlewares.GetDB(c)
	// user := middlewares.GetUser(c)

	siteID := c.Param("site_id")
	appID := c.Param("app_id")
	var site models.Site
	if err := db.Where("id = ?", siteID).First(&site).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "site_not_found",
		})
		return
	}

	var req activeAccountRequest
	var err error
	if err = c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "invalid_request",
		})
		return
	}

	// Handle delivery services (ahamove, grab_express)
	if appID == "ahamove" || appID == "grab_express" {
		// Validate service types
		for _, serviceType := range req.ServiceTypes {
			if serviceType == "INSTANT" || serviceType == "SAME_DAY" {
				continue
			}
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   "invalid service_types only INSTANT and SAME_DAY are allowed",
			})
			return
		}

		switch appID {
		case "ahamove":
			// Get token from Ahamove
			client := ahamove.NewAhamoveClient()
			token, err := client.GetToken(req.Username, "")
			if err != nil {
				c.JSON(http.StatusBadRequest, gin.H{
					"success": false,
					"error":   "invalid_request",
				})
				return
			}
			if token == nil {
				c.JSON(http.StatusBadRequest, gin.H{
					"success": false,
					"error":   "invalid_request",
				})
				return
			}

			// Update token account
			tokenAccount := models.TokenAccount{
				TokenCode:    req.Username + "_ahamove",
				Source:       "ahamove",
				Username:     req.Username,
				AccessToken:  token.AccessToken,
				RefreshToken: token.RefreshToken,
				ExpiredAt:    token.ExpiredAt,
			}

			result := db.Model(&models.TokenAccount{}).
				Where("token_code = ?", req.Username+"_ahamove").
				Where("source = ?", "ahamove").
				Updates(tokenAccount)

			if result.Error != nil {
				c.JSON(http.StatusBadRequest, gin.H{
					"success": false,
					"error":   "invalid_request",
				})
				return
			}

			if result.RowsAffected == 0 {
				if err := db.Create(&tokenAccount).Error; err != nil {
					c.JSON(http.StatusBadRequest, gin.H{
						"success": false,
						"error":   "invalid_request",
					})
					return
				}
			}

			// Update site token
			site.AhamoveToken = &models.JSONField[models.DeliveryToken]{
				Data: models.DeliveryToken{
					Source:       "ahamove",
					TokenCode:    req.Username + "_ahamove",
					ServiceTypes: req.ServiceTypes,
				},
			}

		case "grab_express":
			// Get token from Grab Express
			client := grab_express.NewClient()
			token, err := client.GetToken("", "")
			if err != nil {
				c.JSON(http.StatusBadRequest, gin.H{
					"success": false,
					"error":   "invalid_request",
				})
				return
			}

			// Update token account
			tokenAccount := models.TokenAccount{
				TokenCode:    "SAAS_grab_express",
				Source:       "grab_express",
				Username:     token.Username,
				Password:     "",
				AccessToken:  token.AccessToken,
				RefreshToken: token.RefreshToken,
				ExpiredAt:    token.ExpiredAt,
			}

			result := db.Model(&models.TokenAccount{}).
				Where("token_code = ?", "SAAS_grab_express").
				Where("source = ?", "grab_express").
				Updates(tokenAccount)

			if result.RowsAffected == 0 {
				if err := db.Create(&tokenAccount).Error; err != nil {
					c.JSON(http.StatusBadRequest, gin.H{
						"success": false,
						"error":   "invalid_request",
					})
					return
				}
			}
			err = result.Error
			if err != nil {
				c.JSON(http.StatusBadRequest, gin.H{
					"success": false,
					"error":   "invalid_request",
				})
				return
			}

			// Update site token
			site.GrabExpressToken = &models.JSONField[models.DeliveryToken]{
				Data: models.DeliveryToken{
					Source:       "grab_express",
					TokenCode:    "SAAS_grab_express",
					ServiceTypes: req.ServiceTypes,
				},
			}
		}

		// Save site
		err = db.Save(&site).Error
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   "invalid_request",
			})
			return
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    site,
	})
}

type activeQcomAccountRequest struct {
	HubID       string `json:"hub_id"`
	Username    string `json:"username"`
	Password    string `json:"password"`
	AccessToken string `json:"access_token"`
	StoreID     string `json:"store_id"`
	StoreName   string `json:"store_name"`
}

func CreateSiteByQComAccount(c *gin.Context) {
	db := middlewares.GetDB(c)

	appID := c.Param("app_id")
	brandID := c.Param("brand_id")

	var req activeQcomAccountRequest
	var err error
	if err = c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "invalid_request",
		})
		return
	}

	// Determine merchant source and site type based on appID
	codePrefixMap := map[string]string{
		"shopee_fresh": "SHOPEE_",
		"shopee_food":  "SHOPEE_",
		"grab":         "GRAB_",
		"grab_mart":    "GRAB_",
		"be":           "BE_",
		"be_mart":      "BE_",
	}

	codePrefix, ok := codePrefixMap[appID]
	if !ok {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "unsupported_app_id",
		})
		return
	}

	// Get the appropriate merchant client using the interface
	client := merchant.NewMerchant(appID)
	if client == nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "merchant_client_not_found",
		})
		return
	}

	// Get store details
	storeDetail, err := client.GetStore(&models.Token{
		AccessToken: req.AccessToken,
		SiteID:      req.StoreID,
	})
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Log store details
	fmt.Println("Store detail:", storeDetail.Name)

	// Update token account
	tokenAccount := models.TokenAccount{
		TokenCode:    req.Username + "_" + appID,
		Source:       appID,
		Username:     req.Username,
		Password:     req.Password,
		AccessToken:  req.AccessToken,
		SiteID:       req.StoreID,
		SiteName:     req.StoreName,
		RefreshToken: "",
		ExpiredAt:    time.Now().AddDate(0, 0, 15),
	}

	result := db.Model(&models.TokenAccount{}).
		Where("token_code = ?", req.Username+"_"+appID).
		Where("source = ?", appID).
		Updates(tokenAccount)

	if result.Error != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "invalid_request",
		})
		return
	}

	if result.RowsAffected == 0 {
		if err := db.Create(&tokenAccount).Error; err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   "invalid_request",
			})
			return
		}
	}

	// Create or update site
	site := models.Site{}
	err = db.Where("code = ?", codePrefix+storeDetail.ID).FirstOrInit(&site).Error
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "invalid_request",
		})
		return
	}
	isNewSite := site.ID == ""
	if !isNewSite && site.HubID != req.HubID {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "site_already_linked_to_another_hub",
		})
		return
	}

	// Set common site properties
	site.Type = "mart"
	site.MainSource = appID
	site.BrandID = brandID
	site.HubID = req.HubID
	site.Name = storeDetail.Name
	site.Code = codePrefix + storeDetail.ID
	site.Address = storeDetail.Address
	site.AddressObj = models.JSONField[models.AddressObj]{
		Data: models.AddressObj{},
	}
	site.Description = ""
	site.Active = true
	site.AutoConfirm = true

	// Set appropriate token based on app ID
	siteToken := models.SiteToken{
		Source:    appID,
		TokenCode: req.Username + "_" + appID,
		SiteID:    req.StoreID,
		SiteName:  req.StoreName,
	}

	switch appID {
	case "shopee_food", "shopee_fresh":
		site.ShopeeToken = &models.JSONField[models.SiteToken]{Data: siteToken}
	case "grab":
		site.GrabToken = &models.JSONField[models.SiteToken]{Data: siteToken}
	case "be":
		site.BeToken = &models.JSONField[models.SiteToken]{Data: siteToken}
	}

	// Get address details from Google Maps
	gmapAddresses, _ := gmap.GetSuggestionAddresses(site.Address)
	if len(gmapAddresses) > 0 {
		site.Address = gmapAddresses[0].FormattedAddress
		site.AddressObj = models.JSONField[models.AddressObj]{
			Data: gmapAddresses[0],
		}
	}

	// Save site

	if isNewSite {
		err = db.Create(&site).Error
	} else {
		err = db.Save(&site).Error
	}
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "invalid_request",
		})
		return
	}

	rb := middlewares.GetRabbitMQ(c)
	if rb != nil {
		// Create a message to pull orders from the last 30 days
		message := map[string]any{
			"site_id": site.ID,
			"from":    time.Now().AddDate(0, 0, -2).Format(time.RFC3339),
			"to":      time.Now().Format(time.RFC3339),
		}

		messageJSON, err := json.Marshal(message)
		if err != nil {
			fmt.Printf("Warning: Failed to marshal order pulling message for site %s: %v\n", site.ID, err)
		} else {
			// Publish message to pull old orders
			if err := rb.Publish("cron_site_orders_by_days", messageJSON); err != nil {
				fmt.Printf("Warning: Failed to publish order pulling message for site %s: %v\n", site.ID, err)
			} else {
				fmt.Printf("Successfully triggered order pulling for new site %s (%s)\n", site.ID, site.Name)
			}
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    site,
	})
}

func DeActiveAppAccount(c *gin.Context) {
	db := middlewares.GetDB(c)
	// user := middlewares.GetUser(c)

	siteID := c.Param("site_id")
	appID := c.Param("app_id")
	var site models.Site
	if err := db.Where("id = ?", siteID).First(&site).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "site_not_found",
		})
		return
	}

	// Delete related TokenAccount records before deactivating site tokens
	var tokenCodeToDelete string

	if appID == "ahamove" {
		if site.AhamoveToken != nil {
			tokenCodeToDelete = site.AhamoveToken.Data.TokenCode
		}
		site.AhamoveToken = nil
	}

	if appID == "grab_express" {
		if site.GrabExpressToken != nil {
			tokenCodeToDelete = site.GrabExpressToken.Data.TokenCode
		}
		site.GrabExpressToken = nil
	}

	if appID == "local" {
		site.Active = false
	}

	if site.MainSource == "shopee_food" {
		if site.ShopeeToken != nil {
			tokenCodeToDelete = site.ShopeeToken.Data.TokenCode
		}
		site.ShopeeToken = nil
		site.Active = false
	}

	if site.MainSource == "grab" {
		if site.GrabToken != nil {
			tokenCodeToDelete = site.GrabToken.Data.TokenCode
		}
		site.GrabToken = nil
		site.Active = false
	}

	if site.MainSource == "be" {
		if site.BeToken != nil {
			tokenCodeToDelete = site.BeToken.Data.TokenCode
		}
		site.BeToken = nil
		site.Active = false
	}

	if site.MainSource == "shopee_ecom" {
		site.ShopeeEcomToken = nil
		site.Active = false
	}

	if tokenCodeToDelete != "" {
		if err := db.Where("token_code = ? AND source = ?", tokenCodeToDelete, appID).Delete(&models.TokenAccount{}).Error; err != nil {
			fmt.Printf("Warning: Failed to delete TokenAccount with token_code %s: %v\n", tokenCodeToDelete, err)
		}
	}

	if err := db.Save(&site).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "invalid_request",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}
