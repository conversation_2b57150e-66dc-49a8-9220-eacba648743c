// order-service/router/site.go
package router

import (
	"fmt"
	"math"
	"net/http"
	"os"
	"regexp"
	"strconv"
	"time"

	brandutils "github.com/nexdorvn/nexpos-backend/golang-services/brand-service/utils"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/merchant"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/token"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/utils"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/middlewares"

	"github.com/360EntSecGroup-Skylar/excelize"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// GetSiteList godoc
// @Summary Get list of sites
// @Description Retrieves a paginated list of sites with optional filtering
// @Tags sites
// @Accept json
// @Produce json
// @Param brand_id query string false "Filter sites by brand ID"
// @Param active query string false "Filter sites by active status (true/false)"
// @Param type query []string false "Filter sites by type"
// @Param apply_ph_commission query string false "Filter sites by partner hub commission"
// @Param use_core_product query string false "Filter sites by core product usage"
// @Param name query string false "Search sites by name or code"
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(200)
// @Success 200 {object} PaginationResponse "Returns sites list and pagination info"
// @Failure 500 {object} map[string]any "Internal server error"
// @Router /sites [get]
func GetSiteList(c *gin.Context) {
	db := middlewares.GetDB(c)
	user := middlewares.GetUser(c)

	query := db.Model(&models.Site{})

	// Filter by brand_id
	if val := c.Query("brand_id"); val != "" {
		query = query.Where("brand_id = ?", val)
	}

	if val := c.Query("hub_id"); val != "" {
		query = query.Where("hub_id = ?", val)
	}

	// Filter by active status
	if active := c.Query("active"); active != "" {
		query = query.Where("active = ?", active == "true")
	}

	// Filter by type
	if types := c.QueryArray("type"); len(types) > 0 {
		query = query.Where("type IN ?", types)
	}

	// Filter by partner hub commission
	if applyPHCommission := c.Query("apply_ph_commission"); applyPHCommission == "true" {
		query = query.Where("partner_hub_tier IS NOT NULL")
	}

	// Filter by core product usage
	if useCoreProduct := c.Query("use_core_product"); useCoreProduct != "" {
		query = query.Where("use_core_product = ?", useCoreProduct == "true")
	}

	// Search by name or code
	if name := c.Query("name"); name != "" {
		escapedName := regexp.QuoteMeta(name)
		query = query.Where("name ILIKE ? OR code ILIKE ?",
			fmt.Sprintf("%%%s%%", escapedName),
			fmt.Sprintf("%%%s%%", escapedName))
	}

	// User permission check
	if user != nil {
		// Get all sites the user has access to using the middleware function
		accessibleSiteIDs := middlewares.GetUserAccessibleSites(c)

		// If user has no accessible sites, return empty result
		if len(accessibleSiteIDs) == 0 {
			query = query.Where("id = ?", "no_access") // This will return no results
			return
		}

		// Apply the filter
		query = query.Where("id IN (?)", accessibleSiteIDs)
	}

	// Pagination
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "200"))
	offset := (page - 1) * limit

	var total int64
	if err := query.Count(&total).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Failed to count sites",
		})
		return
	}

	var sites []models.Site
	if err := query.Preload("Brand").
		Preload("Hub").
		Order("name ASC").
		Offset(offset).
		Limit(limit).
		Find(&sites).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Failed to retrieve sites",
		})
		return
	}

	// Load Hubs for sites with hub_ids
	hubIDs := []string{}
	for _, site := range sites {
		hubIDs = append(hubIDs, string(site.HubID))
	}

	hubs := []models.Hub{}
	if len(hubIDs) > 0 {
		if err := db.Where("id IN ?", hubIDs).Find(&hubs).Error; err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   "Failed to retrieve hubs",
			})
			return
		}
	}

	// Map hubs to sites
	hubMap := make(map[string]*models.Hub)
	for i := range hubs {
		hubMap[hubs[i].ID] = &hubs[i]
	}

	totalPages := int(math.Ceil(float64(total) / float64(limit)))

	c.JSON(http.StatusOK, models.PaginationResponse{
		Success:    true,
		Data:       sites,
		Total:      total,
		Page:       page,
		PageSize:   limit,
		TotalPages: totalPages,
		HasNext:    page < totalPages,
		HasPrev:    page > 1,
	})
}

// GetSite godoc
// @Summary Get a single site
// @Description Retrieves a single site by code or ID with special domain handling
// @Tags sites
// @Accept json
// @Produce json
// @Param code query string false "Site code"
// @Param id query string false "Site ID"
// @Success 200 {object} map[string]any "Returns site data with self-pickup settings"
// @Failure 200 {object} map[string]any "Returns error if site not found"
// @Router /sites/get [get]
func GetSite(c *gin.Context) {
	db := middlewares.GetDB(c)

	query := db.Model(&models.Site{})

	// Query by code or id
	if code := c.Query("code"); code != "" {
		query = query.Where("code IN ?", []string{code, fmt.Sprintf("%v", code)})
	}
	if id := c.Query("id"); id != "" {
		query = query.Where("id = ?", id)
	}

	// Custom site handling for specific domains
	// if c.GetHeader("origin") != "" {
	// 	origin := c.GetHeader("origin")
	// 	if regexp.MustCompile("ooo.com.vn$").MatchString(origin) {
	// 		query = query.Where("code = ?", "cskh.seller")
	// 	}
	// 	if regexp.MustCompile("nexpos.io$").MatchString(origin) && c.Query("code") == "" {
	// 		query = query.Where("code = ?", "NNDD")
	// 	}
	// }

	var site models.Site
	if err := query.Preload("Brand").First(&site).Error; err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"error":   "site_id_not_found",
		})
		return
	}

	// Check and prepare self-pickup settings
	selfPickupToken := site.GetToken("self_pickup")
	var response struct {
		models.Site
		SelfPickup any `json:"self_pickup,omitempty"`
		TakeAway   any `json:"take_away,omitempty"`
	}
	response.Site = site

	if selfPickupToken != nil {
		isPickUp, isTakeAway := false, false
		for _, serviceType := range utils.StructToJSON(selfPickupToken).Get("settings.service_types").Array() {
			if serviceType.String() == "pick_up" {
				isPickUp = true
			}
			if serviceType.String() == "take_away" {
				isTakeAway = true
			}
		}
		if isPickUp {
			response.SelfPickup = map[string]any{
				"vendor":      "pick_up",
				"code":        "pick_up",
				"price":       0,
				"name":        "Khách đến cửa hàng lấy hàng (Pick Up)",
				"description": "Khách đến cửa hàng lấy hàng (Pick Up)",
			}
		}
		if isTakeAway {
			response.TakeAway = map[string]any{
				"vendor":      "take_away",
				"code":        "take_away",
				"price":       0,
				"name":        "Khách mua mang về (Take Away)",
				"description": "Khách mua mang về (Take Away)",
			}
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    response,
	})
}

// CreateSite godoc
// @Summary Create a new site
// @Description Creates a new site and associates it with the current user
// @Tags sites
// @Accept json
// @Produce json
// @Success 200 {object} map[string]any "Returns the created site"
// @Failure 400 {object} map[string]any "Bad request or site code already exists"
// @Failure 500 {object} map[string]any "Internal server error"
// @Router /sites [post]
func CreateSite(c *gin.Context) {
	db := middlewares.GetDB(c)

	var site models.Site
	if err := c.ShouldBindJSON(&site); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	site.MainSource = "local"

	// Check if site code already exists
	var existingSite models.Site
	if err := db.Where("code = ?", site.Code).First(&existingSite).Error; err != gorm.ErrRecordNotFound {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "site_code_exist",
		})
		return
	}

	err := db.Transaction(func(tx *gorm.DB) error {
		// Create the site
		if err := tx.Create(&site).Error; err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    site,
	})
}

// UpdateSite godoc
// @Summary Update a site
// @Description Updates an existing site by ID
// @Tags sites
// @Accept json
// @Produce json
// @Param site_id path string true "Site ID"
// @Success 200 {object} map[string]any "Returns the updated site"
// @Failure 400 {object} map[string]any "Bad request"
// @Failure 404 {object} map[string]any "Site not found"
// @Failure 500 {object} map[string]any "Internal server error"
// @Router /sites/{site_id} [put]
func UpdateSite(c *gin.Context) {
	db := middlewares.GetDB(c)
	siteID := c.Param("site_id")

	var site models.Site
	if err := db.First(&site, "id = ?", siteID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "site_id_not_found",
		})
		return
	}

	var updateData map[string]any
	if err := c.ShouldBindJSON(&updateData); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	err := db.Transaction(func(tx *gorm.DB) error {
		// Update the site
		if err := tx.Model(&site).Updates(updateData).Error; err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Reload the site to get updated data
	if err := db.First(&site, "id = ?", siteID).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    site,
	})
}

// DeleteSite godoc
// @Summary Delete a site
// @Description Soft deletes a site by ID (sets deleted_at timestamp)
// @Tags sites
// @Accept json
// @Produce json
// @Param site_id path string true "Site ID"
// @Success 200 {object} map[string]any "Success message"
// @Failure 500 {object} map[string]any "Internal server error"
// @Router /sites/{site_id} [delete]
func DeleteSite(c *gin.Context) {
	db := middlewares.GetDB(c)

	now := time.Now()
	if err := db.Model(&models.Site{}).
		Where("id = ?", c.Param("site_id")).
		Update("deleted_at", &now).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}

// UpdateStoreOpen godoc
// @Summary Update store open status for apps
// @Description Updates the pause status for different apps for a specific site
// @Tags sites
// @Accept json
// @Produce json
// @Param site_id path string true "Site ID"
// @Param request body map[string]any true "Request object with apps status"
// @Success 200 {object} map[string]any "Returns the updated site"
// @Failure 400 {object} map[string]any "Bad request"
// @Failure 404 {object} map[string]any "Site not found"
// @Failure 500 {object} map[string]any "Internal server error"
// @Router /sites/{site_id}/store-open [put]
func UpdateStoreOpen(c *gin.Context) {
	db := middlewares.GetDB(c)
	siteID := c.Param("site_id")

	var request struct {
		Apps     map[string]bool `json:"apps"`
		Duration *int            `json:"duration"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	var site models.Site
	if err := db.First(&site, "id = ?", siteID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "site_id_not_found",
		})
		return
	}

	// Update pause apps
	pauseApps := site.PauseApps.Data
	if pauseApps == nil {
		pauseApps = make(map[string]bool)
	}
	for k, v := range request.Apps {
		pauseApps[k] = v
	}

	if err := db.Model(&site).Update("pause_apps", models.JSONField[map[string]bool]{Data: pauseApps}).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    site,
	})
}

var MerchantInfo = map[string]struct {
	Label string
}{
	"grab":         {Label: "Grab"},
	"shopee":       {Label: "Shopee"},
	"grab_express": {Label: "Grab Express"},
	"ahamove":      {Label: "Ahamove"},
}

// GetSiteErrors godoc
// @Summary Get site errors
// @Description Retrieves a list of site errors related to merchant connections
// @Tags sites
// @Accept json
// @Produce json
// @Success 200 {object} map[string]any "Returns list of error messages"
// @Failure 500 {object} map[string]any "Internal server error"
// @Router /sites/errors [get]
func GetSiteErrors(c *gin.Context) {
	user := middlewares.GetUser(c)
	if user == nil {
		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"data":    []string{},
		})
		return
	}

	db := middlewares.GetDB(c)
	var tokenAccounts []models.TokenAccount

	tenMinutesAgo := time.Now().Add(-10 * time.Minute)

	if err := db.Where("working = ? AND source IN ? AND last_working_at < ?",
		false,
		[]string{"grab", "shopee", "grab_express", "ahamove"},
		tenMinutesAgo,
	).Find(&tokenAccounts).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Map token accounts to error messages
	result := []string{}
	for _, account := range tokenAccounts {
		label := MerchantInfo[account.Source].Label
		if label == "" {
			label = account.Source
		}
		errMsg := fmt.Sprintf("Lỗi kết nối ứng dụng: %s - %s", label, account.TokenCode)
		result = append(result, errMsg)
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    result,
	})
}

// GetSiteExports godoc
// @Summary Export sites to Excel
// @Description Generates an Excel export of all sites with their configurations
// @Tags sites
// @Accept json
// @Produce json
// @Param name query string false "Filter sites by name"
// @Success 200 {object} map[string]any "Returns file URL"
// @Failure 500 {object} map[string]any "Internal server error"
// @Router /sites/exports [get]
func GetSiteExports(c *gin.Context) {
	db := middlewares.GetDB(c)
	user := middlewares.GetUser(c)

	// Build query
	query := db.Model(&models.Site{})

	if name := c.Query("name"); name != "" {
		query = query.Where("name ILIKE ?", fmt.Sprintf("%%%s%%", name))
	}

	if user != nil {
		// Get all sites the user has access to using the middleware function
		accessibleSiteIDs := middlewares.GetUserAccessibleSites(c)

		// If user has no accessible sites, return empty result
		if len(accessibleSiteIDs) == 0 {
			c.JSON(http.StatusOK, gin.H{
				"success": true,
				"data":    "",
			})
			return
		}

		// Apply the filter
		query = query.Where("id IN (?)", accessibleSiteIDs)
	}

	// Get sites
	sites := []models.Site{}
	if err := query.Find(&sites).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Get related data
	brandIDs, hubIDs := []string{}, []string{}
	for _, site := range sites {
		brandIDs = append(brandIDs, site.BrandID)
		hubIDs = append(hubIDs, string(site.HubID))
	}

	brands := []models.Brand{}
	hubs := []models.Hub{}
	if err := db.Where("id IN ?", brandIDs).Find(&brands).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	if err := db.Where("id IN ?", hubIDs).Find(&hubs).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Create maps for easier lookup
	brandMap := make(map[string]models.Brand)
	for _, brand := range brands {
		brandMap[brand.ID] = brand
	}

	hubMap := make(map[string]models.Hub)
	for _, hub := range hubs {
		hubMap[hub.ID] = hub
	}

	// Create Excel file
	f := excelize.NewFile()

	// Create headers
	headers := []string{
		"Tên cửa hàng", "Mã cửa hàng", "Địa chỉ", "Thương hiệu", "Khu vực hub",
		"Loại cửa hàng", "Trạng thái", "Tự động xác nhận", "Tự động in",
		"Áp dụng hoa hồng", "Áp dụng quà tặng",
		"Shopee", "Shopee Fresh", "Gojek", "Grab", "Grab Mart", "BE",
	}

	for i, header := range headers {
		col := string(rune('A' + i))
		f.SetCellValue("Sheet1", fmt.Sprintf("%s1", col), header)
	}

	// Add data
	for i, site := range sites {
		row := i + 2
		brand := brandMap[site.BrandID]
		hub := hubMap[string(site.HubID)]

		data := []any{
			site.Name,
			site.Code,
			site.Address,
			brand.Name,
			hub.Name,
			site.Type,
			utils.ShortIf(site.Active, "Hoạt động", "Ngừng hoạt động"),
			utils.ShortIf(site.AutoConfirm, "Có", "Không"),
			utils.ShortIf(site.AutoPrint, "Có", "Không"),
			utils.ShortIf(site.ApplyCommission, "Có", "Không"),
			utils.ShortIf(site.ApplyGift, "Có", "Không"),
		}

		// Add merchant tokens
		merchantSources := []string{"shopee_food", "shopee_fresh", "gojek", "grab", "grab_mart", "be"}
		for _, source := range merchantSources {
			token := site.GetToken(source)
			tokenStatus := "Không"
			if token != nil {
				tokenStatus = fmt.Sprintf("Có: %s", token.Source)
			}
			data = append(data, tokenStatus)
		}

		// Write row data
		for j, value := range data {
			col := string(rune('A' + j))
			f.SetCellValue("Sheet1", fmt.Sprintf("%s%d", col, row), value)
		}
	}

	// Save to buffer
	buffer, err := f.WriteToBuffer()
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Upload file
	filename := fmt.Sprintf("reports/Báo_cáo_%d.xlsx", time.Now().Unix())
	file, err := utils.UploadFile("nexpos-files", filename, buffer.Bytes())
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    file,
	})
}

// GetSiteOpenStatus godoc
// @Summary Get site open status
// @Description Retrieves the open status for a site across different merchant platforms
// @Tags sites
// @Accept json
// @Produce json
// @Param site_id path string true "Site ID"
// @Success 200 {object} map[string]any "Returns site data with open status for each platform"
// @Failure 404 {object} map[string]any "Site not found"
// @Failure 500 {object} map[string]any "Internal server error"
// @Router /sites/{site_id}/open/status [get]
func GetSiteOpenStatus(c *gin.Context) {
	db := middlewares.GetDB(c)
	siteID := c.Param("site_id")

	// Find the site
	var site models.Site
	if err := db.First(&site, "id = ?", siteID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "site_id_not_found",
		})
		return
	}

	// Check if merchant apps are enabled
	useMerchantApps := true
	if os.Getenv("USE_MERCHANT_APPS") == "false" {
		useMerchantApps = false
	}

	if !useMerchantApps {
		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"data": map[string]any{
				"pause_apps": map[string]bool{
					"shopee":       false,
					"shopee_fresh": false,
					"be":           false,
					"gojek":        false,
					"grab":         false,
					"grab_mart":    false,
					"baemin":       false,
				},
			},
		})
		return
	}

	// Initialize pause apps map
	pauseApps := make(map[string]bool)
	if site.PauseApps.Data != nil {
		pauseApps = site.PauseApps.Data
	}

	// Get working hours
	workingHours := site.WorkingHours.Data

	// Define merchant sources to check
	merchantSources := []string{"shopee_food", "shopee_fresh", "grab", "grab_mart", "be"}

	// Check open status for each merchant
	for _, source := range merchantSources {
		// Get token for the merchant
		tokenAccount, err := token.GetTokenBySite(db, site, source)
		if err != nil || tokenAccount == nil {
			continue
		}

		// Skip if no access token
		if tokenAccount.AccessToken == "" {
			continue
		}

		// Create merchant client
		merchantClient := merchant.NewMerchant(source)
		if merchantClient == nil {
			continue
		}

		// Check open status
		isOpen, err := merchantClient.GetOpenStatus(&models.Token{
			AccessToken: tokenAccount.AccessToken,
			SiteID:      tokenAccount.SiteID,
		})
		if err != nil {
			continue
		}

		// Update pause apps
		pauseApps[source] = !isOpen
	}

	// Update site with new pause apps and working hours
	if err := db.Model(&site).Updates(map[string]any{
		"pause_apps":    models.JSONField[map[string]bool]{Data: pauseApps},
		"working_hours": models.JSONField[map[string]models.WorkingHour]{Data: workingHours},
	}).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Reload site to get updated data
	if err := db.First(&site, "id = ?", siteID).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    site,
	})
}

// UpdateSiteStoreOpen godoc
// @Summary Update store open status
// @Description Updates the open/close status for a site across different merchant platforms
// @Tags sites
// @Accept json
// @Produce json
// @Param site_id path string true "Site ID"
// @Param request body map[string]any true "Request with apps and duration"
// @Success 200 {object} map[string]any "Returns updated site data"
// @Failure 404 {object} map[string]any "Site not found"
// @Failure 500 {object} map[string]any "Internal server error"
// @Router /sites/{site_id}/opens [post]
func UpdateSiteStoreOpen(c *gin.Context) {
	db := middlewares.GetDB(c)
	siteID := c.Param("site_id")

	// Parse request
	var request struct {
		Apps     map[string]bool `json:"apps"`
		Duration *int            `json:"duration"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Find the site
	var site models.Site
	if err := db.First(&site, "id = ?", siteID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "site_id_not_found",
		})
		return
	}

	// Check if merchant apps are enabled
	if os.Getenv("USE_MERCHANT_APPS") != "true" {
		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"data":    site,
		})
		return
	}

	// Define merchant functions
	merchantSources := map[string]string{
		"shopee":       "shopee",
		"shopee_fresh": "shopee",
		"grab":         "grab",
		"grab_mart":    "grab",
		"be":           "be",
	}

	// Update status for each app
	for source, isClose := range request.Apps {
		merchantType, exists := merchantSources[source]
		if !exists {
			continue
		}

		// Get token for the merchant
		tokenAccount, err := token.GetTokenBySite(db, site, source)
		if err != nil || tokenAccount == nil || tokenAccount.AccessToken == "" {
			continue
		}

		// Create merchant client
		merchantClient := merchant.NewMerchant(merchantType)
		if merchantClient == nil {
			continue
		}

		// Determine status (open/close)
		status := "open"
		if isClose {
			status = "close"
		}

		// Get duration
		duration := 0
		if request.Duration != nil {
			duration = *request.Duration
		}

		// Update store status
		_ = merchantClient.UpdateStoreStatus(&models.Token{
			AccessToken: tokenAccount.AccessToken,
			SiteID:      tokenAccount.SiteID,
		}, status, duration)
	}

	// Update site's pause_apps
	pauseApps := site.PauseApps.Data
	if pauseApps == nil {
		pauseApps = make(map[string]bool)
	}

	for source, isClose := range request.Apps {
		pauseApps[source] = isClose
	}

	if err := db.Model(&site).Update("pause_apps", models.JSONField[map[string]bool]{Data: pauseApps}).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Reload site to get updated data
	if err := db.First(&site, "id = ?", siteID).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    site,
	})
}

// GetStoreOpeningHours godoc
// @Summary Get store opening hours
// @Description Retrieves the opening hours for a site from a specific merchant platform
// @Tags sites
// @Accept json
// @Produce json
// @Param site_id path string true "Site ID"
// @Param source query string true "Merchant source (shopee, grab, etc.)"
// @Success 200 {object} map[string]any "Returns opening hours data"
// @Failure 404 {object} map[string]any "Site not found"
// @Failure 500 {object} map[string]any "Internal server error"
// @Router /sites/{site_id}/open/hours [get]
func GetStoreOpeningHours(c *gin.Context) {
	db := middlewares.GetDB(c)
	siteID := c.Param("site_id")

	// Find the site
	var site models.Site
	if err := db.First(&site, "id = ?", siteID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "site_id_not_found",
		})
		return
	}
	source := site.MainSource

	// Define supported merchant functions
	merchantFunctions := map[string]string{
		"shopee":       "shopee",
		"shopee_fresh": "shopee",
		"grab":         "grab",
		"grab_mart":    "grab",
		"be":           "be",
	}

	// Check if source is supported
	merchantType, exists := merchantFunctions[source]
	if !exists {
		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"data":    site.WorkingHours.Data,
		})
		return
	}
	// Get token for the merchant
	tokenAccount, err := token.GetTokenBySite(db, site, source)
	if err != nil || tokenAccount == nil || tokenAccount.AccessToken == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "token_not_found",
		})
		return
	}

	// Create merchant client
	merchantClient := merchant.NewMerchant(merchantType)
	if merchantClient == nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "merchant_client_not_found",
		})
		return
	}

	// Get opening hours using the new GetOpeningHour method
	workingHours, err := merchantClient.GetOpeningHour(&models.Token{
		AccessToken: tokenAccount.AccessToken,
		SiteID:      tokenAccount.SiteID,
	})
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Map working hours to standard format
	newWorkingHour := brandutils.MapWorkingHour(source, workingHours)

	// Update site's working hours
	if len(newWorkingHour) > 0 {
		if err := db.Model(&site).Update("working_hours", models.JSONField[map[string]models.WorkingHour]{Data: newWorkingHour}).Error; err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   err.Error(),
			})
			return
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    newWorkingHour,
	})
}

func UpdateStoreOpeningHours(c *gin.Context) {
	db := middlewares.GetDB(c)
	siteID := c.Param("site_id")

	// Parse request
	var request struct {
		WorkingHours map[string]models.WorkingHour `json:"working_hours"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Find the site
	var site models.Site
	if err := db.First(&site, "id = ?", siteID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "site_id_not_found",
		})
		return
	}

	// Update site's working hours
	if err := db.Model(&site).Update("working_hours", models.JSONField[map[string]models.WorkingHour]{Data: request.WorkingHours}).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}

// SyncStoreOpeningHours godoc
// @Summary Sync store opening hours
// @Description Synchronizes opening hours for a site across different merchant platforms
// @Tags sites
// @Accept json
// @Produce json
// @Param site_id path string true "Site ID"
// @Param request body map[string]any true "Request with working hours and sources"
// @Success 200 {object} map[string]any "Success response"
// @Failure 404 {object} map[string]any "Site not found"
// @Failure 500 {object} map[string]any "Internal server error"
// @Router /sites/{site_id}/opening_hour/sync [post]
func SyncStoreOpeningHours(c *gin.Context) {
	db := middlewares.GetDB(c)
	siteID := c.Param("site_id")

	// Parse request
	var request struct {
		WorkingHours map[string]models.WorkingHour `json:"working_hours"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Find the site
	var site models.Site
	if err := db.First(&site, "id = ?", siteID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "site_id_not_found",
		})
		return
	}

	// Check if merchant apps are enabled
	if os.Getenv("USE_MERCHANT_APPS") != "true" {
		c.JSON(http.StatusOK, gin.H{
			"success": true,
		})
		return
	}

	merchantClient := merchant.NewMerchant(site.MainSource)
	if merchantClient == nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "merchant_client_not_found",
		})
		return
	}

	// Get token for the merchant
	tokenAccount, err := token.GetTokenBySite(db, site, site.MainSource)
	if err != nil || tokenAccount == nil || tokenAccount.AccessToken == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "merchant_token_not_found",
		})
		return
	}

	// Convert working hours to merchant-specific format
	merchantWorkingHours := brandutils.ReverseMapWorkingHour(site.MainSource, request.WorkingHours)
	if merchantWorkingHours == nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "invalid_working_hours_format",
		})
		return
	}

	// Update opening hours in merchant platform
	err = merchantClient.UpdateOpeningHour(&models.Token{
		AccessToken: tokenAccount.AccessToken,
		SiteID:      tokenAccount.SiteID,
	}, merchantWorkingHours)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "failed_to_update_merchant_opening_hours: " + err.Error(),
		})
		return
	}

	// Update site's working hours
	if err := db.Model(&site).Update("working_hours", models.JSONField[map[string]models.WorkingHour]{Data: request.WorkingHours}).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
	})
}
