// order-service/router/site.go
package router

import (
	"fmt"
	"net/http"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/middlewares"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/payment"

	"github.com/gin-gonic/gin"
)

func SiteActiveMoMoAccount(c *gin.Context) {
	db := middlewares.GetDB(c)

	siteID := c.Param("site_id")
	var site models.Site
	if err := db.Where("id = ?", siteID).First(&site).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "site_not_found",
		})
		return
	}

	var req models.MoMoToken
	var err error
	if err = c.ShouldBind<PERSON>(&req); err != nil {
		c.<PERSON>(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "invalid_request",
		})
		return
	}
	if req.PartnerCode == "" || req.AccessKey == "" || req.SecretKey == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "invalid_request",
		})
		return
	}

	// Validate MOMO credentials by testing CreatePaymentLink
	if err := payment.ValidateMoMoCredentials(req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success":       false,
			"error_code":    "momo_validation_failed",
			"error_message": "MOMO credentials validation failed: MOMO payment creation failed: Chữ ký không hợp lệ. Vui lòng kiểm tra dữ liệu gốc trước khi mã hóa",
		})
		return
	}

	// Update site token
	site.MoMoToken = &models.JSONField[models.MoMoToken]{
		Data: req,
	}

	// Save site
	if err = db.Save(&site).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "invalid_request" + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    site,
	})
}

func SiteDeActiveMoMoAccount(c *gin.Context) {
	db := middlewares.GetDB(c)

	siteID := c.Param("site_id")
	var site models.Site
	if err := db.Where("id = ?", siteID).First(&site).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "site_not_found",
		})
		return
	}

	// Update site token
	site.MoMoToken = nil

	// Save site
	if err := db.Save(&site).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "invalid_request: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    site,
	})
}

func SiteActiveVNPayAccount(c *gin.Context) {
	db := middlewares.GetDB(c)

	siteID := c.Param("site_id")
	var site models.Site
	if err := db.Where("id = ?", siteID).First(&site).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "site_not_found",
		})
		return
	}

	var req models.VNPayToken
	var err error
	if err = c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "invalid_request",
		})
		return
	}
	if req.MerchantID == "" || req.TerminalID == "" || req.QRSecretKey == "" || req.TransSecretKey == "" || req.RefundSecretKey == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "invalid_request",
		})
		return
	}

	// Validate VNPay credentials by testing CreatePaymentLink, CheckTransaction, and Refund APIs
	if err := payment.ValidateVNPayCredentials(req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success":       false,
			"error_code":    "vnpay_validation_failed",
			"error_message": fmt.Sprintf("VNPay credentials validation failed: %v", err),
		})
		return
	}

	// Update site token
	site.VNPayToken = &models.JSONField[models.VNPayToken]{
		Data: req,
	}

	// Save site
	if err := db.Save(&site).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "invalid_request: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    site,
	})
}

func SiteDeActiveVNPayAccount(c *gin.Context) {
	db := middlewares.GetDB(c)

	siteID := c.Param("site_id")
	var site models.Site
	if err := db.Where("id = ?", siteID).First(&site).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "site_not_found",
		})
		return
	}

	// Update site token
	site.VNPayToken = nil

	// Save site
	if err := db.Save(&site).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "invalid_request: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    site,
	})
}
