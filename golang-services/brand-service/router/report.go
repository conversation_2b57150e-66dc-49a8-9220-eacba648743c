package router

import (
	"fmt"
	"net/http"
	"time"

	"github.com/nexdorvn/nexpos-backend/golang-services/brand-service/models"
	"github.com/nexdorvn/nexpos-backend/golang-services/brand-service/service"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/middlewares"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/utils/excel"

	"github.com/gin-gonic/gin"
)

// GetReport godoc
// @Summary Get hub report
// @Description Retrieves a report for a specific hub within a time range
// @Tags reports
// @Accept json
// @Produce json
// @Param hub_id query string true "Hub ID"
// @Param start_time query string true "Start time (RFC3339 format)"
// @Param end_time query string true "End time (RFC3339 format)"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /reports [get]
func GetReport(c *gin.Context) {
	db := middlewares.GetDB(c)
	user := middlewares.GetUser(c)

	// Parse query parameters
	var query models.ReportQuery
	if err := c.ShouldBindQuery(&query); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Check if user has access to the hub if a specific hub is requested
	if query.HubID != "" && query.HubID != "all" && !contains(user.Hubs, query.HubID) {
		// c.JSON(http.StatusForbidden, gin.H{
		// 	"success": false,
		// 	"error":   "You don't have permission to access this hub",
		// })
		// return
	}

	// Create report service
	reportService := service.NewReportService(db)

	// Generate report
	report, err := reportService.GetReport(query)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Return report
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    report,
	})
}

// GetReportDetail godoc
// @Summary Get detailed report with pagination
// @Description Retrieves a detailed report with paginated order data
// @Tags reports
// @Accept json
// @Produce json
// @Param brand_ids query []string false "Brand IDs"
// @Param apps query []string false "Apps/Sales channels"
// @Param from query string true "Start time (RFC3339 format)"
// @Param to query string true "End time (RFC3339 format)"
// @Param page query int true "Page number" minimum(1)
// @Param items_per_page query int true "Items per page" minimum(1) maximum(100)
// @Success 200 {object} models.PaginationResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /reports/detail [get]
func GetReportDetail(c *gin.Context) {
	db := middlewares.GetDB(c)
	user := middlewares.GetUser(c)

	// Parse query parameters
	var query models.ReportDetailQuery
	if err := c.ShouldBindQuery(&query); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Set default pagination values if not provided
	if query.Page == 0 {
		query.Page = 1
	}
	if query.ItemsPerPage == 0 {
		query.ItemsPerPage = 20
	}

	// Check if user has access to the brands if specific brands are requested
	if len(query.BrandIDs) > 0 {
		for _, brandID := range query.BrandIDs {
			if !contains(user.Brands, brandID) {
				c.JSON(http.StatusForbidden, gin.H{
					"success": false,
					"error":   "You don't have permission to access one or more of the requested brands",
				})
				return
			}
		}
	}

	// Create report service
	reportService := service.NewReportService(db)

	// Generate detailed report
	report, err := reportService.GetReportDetail(query)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Return report
	c.JSON(http.StatusOK, report)
}

// ExportCompletedOrders godoc
// @Summary Export completed orders to Excel
// @Description Exports completed orders to Excel with detailed financial information
// @Tags reports
// @Accept json
// @Produce json
// @Param brand_ids query []string false "Brand IDs"
// @Param apps query []string false "Apps/Sales channels"
// @Param from query string true "Start time (RFC3339 format)"
// @Param to query string true "End time (RFC3339 format)"
// @Param headers query []string false "Selected headers to export (if not provided, all headers will be exported)"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /reports/export-completed-orders [get]
func ExportCompletedOrders(c *gin.Context) {
	db := middlewares.GetDB(c)
	user := middlewares.GetUser(c)

	// Parse query parameters (same as GetReportDetail)
	var query models.ReportDetailQuery
	if err := c.ShouldBindQuery(&query); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Parse selected headers
	selectedHeaders := c.QueryArray("headers")

	// Check if user has access to the brands if specific brands are requested
	if len(query.BrandIDs) > 0 {
		for _, brandID := range query.BrandIDs {
			if !contains(user.Brands, brandID) {
				c.JSON(http.StatusForbidden, gin.H{
					"success": false,
					"error":   "You don't have permission to access one or more of the requested brands",
				})
				return
			}
		}
	}

	// Create report service
	reportService := service.NewReportService(db)

	// Get completed orders data for Excel
	excelData, err := reportService.GetCompletedOrdersForExcel(query)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Get all available headers
	allHeaders := getCompletedOrdersHeaders()

	// Convert data to interface{} slice for Excel generation
	var allData [][]interface{}
	for _, item := range excelData {
		row := []interface{}{
			item.STT,
			item.ReferenceCode,
			item.OrderCode,
			item.Source,
			item.HubName,
			item.BrandName,
			item.SiteName,
			item.RevenueBeforePromo,
			item.ProductDiscount,
			item.RevenueAfterProductDiscount,
			item.OrderDiscount,
			item.TotalPromotion,
			item.RevenueAfterPromo,
			item.AdditionalIncome,
			item.ShippingFee,
			item.CustomerPaid,
			item.PromoCode,
			item.OrderDate,
			item.OrderTime,
			item.DeliveryDate,
			item.DeliveryTime,
		}
		allData = append(allData, row)
	}

	// Filter headers and data based on selected headers
	headers, data := filterHeadersAndData(allHeaders, allData, selectedHeaders)

	// Generate Excel file
	excelBuffer, err := excel.GenerateExcel("Completed Orders", headers, data)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   fmt.Sprintf("Error generating Excel file: %v", err),
		})
		return
	}

	// Upload to storage
	timestamp := time.Now().Format("20060102_150405")
	fileName := fmt.Sprintf("completed_orders_%s.xlsx", timestamp)
	fileURL, err := excel.UploadExcel("nexpos-files", fmt.Sprintf("reports/%s", fileName), excelBuffer)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   fmt.Sprintf("Error uploading Excel file: %v", err),
		})
		return
	}

	// Return file URL
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"file_url":  fileURL,
			"file_name": fileName,
		},
	})
}

// ExportBrandReport godoc
// @Summary Export brand report to Excel
// @Description Exports completed orders grouped by brand to Excel with financial summary
// @Tags reports
// @Accept json
// @Produce json
// @Param brand_ids query []string false "Brand IDs"
// @Param apps query []string false "Apps/Sales channels"
// @Param from query string true "Start time (RFC3339 format)"
// @Param to query string true "End time (RFC3339 format)"
// @Param headers query []string false "Selected headers to export (if not provided, all headers will be exported)"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /reports/export-brand-report [get]
func ExportBrandReport(c *gin.Context) {
	db := middlewares.GetDB(c)
	user := middlewares.GetUser(c)

	// Parse query parameters (same as GetReportDetail)
	var query models.ReportDetailQuery
	if err := c.ShouldBindQuery(&query); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Parse selected headers
	selectedHeaders := c.QueryArray("headers")

	// Check if user has access to the brands if specific brands are requested
	if len(query.BrandIDs) > 0 {
		for _, brandID := range query.BrandIDs {
			if !contains(user.Brands, brandID) {
				c.JSON(http.StatusForbidden, gin.H{
					"success": false,
					"error":   "You don't have permission to access one or more of the requested brands",
				})
				return
			}
		}
	}

	// Create report service
	reportService := service.NewReportService(db)

	// Get brand report data for Excel
	brandData, err := reportService.GetBrandReportForExcel(query)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Get all available headers
	allHeaders := getBrandReportHeaders()

	// Convert data to interface{} slice for Excel generation
	var allData [][]interface{}
	for _, item := range brandData {
		row := []interface{}{
			item.BrandName,
			item.RevenueBeforePromo,
			item.TotalPromotion,
			item.RevenueAfterPromo,
		}
		allData = append(allData, row)
	}

	// Filter headers and data based on selected headers
	headers, data := filterHeadersAndData(allHeaders, allData, selectedHeaders)

	// Generate Excel file
	excelBuffer, err := excel.GenerateExcel("Brand Report", headers, data)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   fmt.Sprintf("Error generating Excel file: %v", err),
		})
		return
	}

	// Upload to storage
	timestamp := time.Now().Format("20060102_150405")
	fileName := fmt.Sprintf("brand_report_%s.xlsx", timestamp)
	fileURL, err := excel.UploadExcel("nexpos-files", fmt.Sprintf("reports/%s", fileName), excelBuffer)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   fmt.Sprintf("Error uploading Excel file: %v", err),
		})
		return
	}

	// Return file URL
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"file_url":  fileURL,
			"file_name": fileName,
		},
	})
}

// ExportCancelledOrders godoc
// @Summary Export cancelled orders to Excel
// @Description Exports cancelled orders to Excel with detailed information
// @Tags reports
// @Accept json
// @Produce json
// @Param brand_ids query []string false "Brand IDs"
// @Param apps query []string false "Apps/Sales channels"
// @Param from query string true "Start time (RFC3339 format)"
// @Param to query string true "End time (RFC3339 format)"
// @Param headers query []string false "Selected headers to export (if not provided, all headers will be exported)"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /reports/export-cancelled-orders [get]
func ExportCancelledOrders(c *gin.Context) {
	db := middlewares.GetDB(c)
	user := middlewares.GetUser(c)

	// Parse query parameters (same as GetReportDetail)
	var query models.ReportDetailQuery
	if err := c.ShouldBindQuery(&query); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Parse selected headers
	selectedHeaders := c.QueryArray("headers")

	// Check if user has access to the brands if specific brands are requested
	if len(query.BrandIDs) > 0 {
		for _, brandID := range query.BrandIDs {
			if !contains(user.Brands, brandID) {
				c.JSON(http.StatusForbidden, gin.H{
					"success": false,
					"error":   "You don't have permission to access one or more of the requested brands",
				})
				return
			}
		}
	}

	// Create report service
	reportService := service.NewReportService(db)

	// Get cancelled orders data for Excel
	excelData, err := reportService.GetCancelledOrdersForExcel(query)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Get all available headers
	allHeaders := getCancelledOrdersHeaders()

	// Convert data to interface{} slice for Excel generation
	var allData [][]interface{}
	for _, item := range excelData {
		row := []interface{}{
			item.STT,
			item.ReferenceCode,
			item.OrderCode,
			item.Source,
			item.HubName,
			item.BrandName,
			item.SiteName,
			item.ItemCount,
			item.OrderDate,
			item.OrderTime,
			item.CancelledBy,
			item.CancelReason,
		}
		allData = append(allData, row)
	}

	// Filter headers and data based on selected headers
	headers, data := filterHeadersAndData(allHeaders, allData, selectedHeaders)

	// Generate Excel file
	excelBuffer, err := excel.GenerateExcel("Cancelled Orders", headers, data)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   fmt.Sprintf("Error generating Excel file: %v", err),
		})
		return
	}

	// Upload to storage
	timestamp := time.Now().Format("20060102_150405")
	fileName := fmt.Sprintf("cancelled_orders_%s.xlsx", timestamp)
	fileURL, err := excel.UploadExcel("nexpos-files", fmt.Sprintf("reports/%s", fileName), excelBuffer)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   fmt.Sprintf("Error uploading Excel file: %v", err),
		})
		return
	}

	// Return file URL
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"file_url":  fileURL,
			"file_name": fileName,
		},
	})
}

// GetCompletedOrders godoc
// @Summary Get paginated completed orders
// @Description Retrieves paginated completed orders with detailed financial information
// @Tags reports
// @Accept json
// @Produce json
// @Param brand_ids query []string false "Brand IDs"
// @Param apps query []string false "Apps/Sales channels"
// @Param from query string true "Start time (RFC3339 format)"
// @Param to query string true "End time (RFC3339 format)"
// @Param page query int true "Page number" minimum(1)
// @Param items_per_page query int true "Items per page" minimum(1) maximum(100)
// @Success 200 {object} models.PaginationResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /reports/completed-orders [get]
func GetCompletedOrders(c *gin.Context) {
	db := middlewares.GetDB(c)
	user := middlewares.GetUser(c)

	// Parse query parameters
	var query models.ReportDetailQuery
	if err := c.ShouldBindQuery(&query); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Set default pagination values if not provided
	if query.Page == 0 {
		query.Page = 1
	}
	if query.ItemsPerPage == 0 {
		query.ItemsPerPage = 20
	}

	// Validate pagination limits
	if query.ItemsPerPage > 100 {
		query.ItemsPerPage = 100
	}

	// Check if user has access to the brands if specific brands are requested
	if len(query.BrandIDs) > 0 {
		for _, brandID := range query.BrandIDs {
			if !contains(user.Brands, brandID) {
				c.JSON(http.StatusForbidden, gin.H{
					"success": false,
					"error":   "You don't have permission to access one or more of the requested brands",
				})
				return
			}
		}
	}

	// Create report service
	reportService := service.NewReportService(db)

	// Get paginated completed orders
	result, err := reportService.GetCompletedOrdersPaginated(query)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, result)
}

// GetBrandReport godoc
// @Summary Get paginated brand report
// @Description Retrieves paginated brand report with financial summary grouped by brand
// @Tags reports
// @Accept json
// @Produce json
// @Param brand_ids query []string false "Brand IDs"
// @Param apps query []string false "Apps/Sales channels"
// @Param from query string true "Start time (RFC3339 format)"
// @Param to query string true "End time (RFC3339 format)"
// @Param page query int true "Page number" minimum(1)
// @Param items_per_page query int true "Items per page" minimum(1) maximum(100)
// @Success 200 {object} models.PaginationResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /reports/brand-report [get]
func GetBrandReport(c *gin.Context) {
	db := middlewares.GetDB(c)
	user := middlewares.GetUser(c)

	// Parse query parameters
	var query models.ReportDetailQuery
	if err := c.ShouldBindQuery(&query); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Set default pagination values if not provided
	if query.Page == 0 {
		query.Page = 1
	}
	if query.ItemsPerPage == 0 {
		query.ItemsPerPage = 20
	}

	// Validate pagination limits
	if query.ItemsPerPage > 100 {
		query.ItemsPerPage = 100
	}

	// Check if user has access to the brands if specific brands are requested
	if len(query.BrandIDs) > 0 {
		for _, brandID := range query.BrandIDs {
			if !contains(user.Brands, brandID) {
				c.JSON(http.StatusForbidden, gin.H{
					"success": false,
					"error":   "You don't have permission to access one or more of the requested brands",
				})
				return
			}
		}
	}

	// Create report service
	reportService := service.NewReportService(db)

	// Get paginated brand report
	result, err := reportService.GetBrandReportPaginated(query)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, result)
}

// GetCancelledOrders godoc
// @Summary Get paginated cancelled orders
// @Description Retrieves paginated cancelled orders with detailed information
// @Tags reports
// @Accept json
// @Produce json
// @Param brand_ids query []string false "Brand IDs"
// @Param apps query []string false "Apps/Sales channels"
// @Param from query string true "Start time (RFC3339 format)"
// @Param to query string true "End time (RFC3339 format)"
// @Param page query int true "Page number" minimum(1)
// @Param items_per_page query int true "Items per page" minimum(1) maximum(100)
// @Success 200 {object} models.PaginationResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /reports/cancelled-orders [get]
func GetCancelledOrders(c *gin.Context) {
	db := middlewares.GetDB(c)
	user := middlewares.GetUser(c)

	// Parse query parameters
	var query models.ReportDetailQuery
	if err := c.ShouldBindQuery(&query); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Set default pagination values if not provided
	if query.Page == 0 {
		query.Page = 1
	}
	if query.ItemsPerPage == 0 {
		query.ItemsPerPage = 20
	}

	// Validate pagination limits
	if query.ItemsPerPage > 100 {
		query.ItemsPerPage = 100
	}

	// Check if user has access to the brands if specific brands are requested
	if len(query.BrandIDs) > 0 {
		for _, brandID := range query.BrandIDs {
			if !contains(user.Brands, brandID) {
				c.JSON(http.StatusForbidden, gin.H{
					"success": false,
					"error":   "You don't have permission to access one or more of the requested brands",
				})
				return
			}
		}
	}

	// Create report service
	reportService := service.NewReportService(db)

	// Get paginated cancelled orders
	result, err := reportService.GetCancelledOrdersPaginated(query)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, result)
}

// GetOrderFeedbacks godoc
// @Summary Get paginated order feedbacks
// @Description Retrieves paginated order feedbacks with detailed information
// @Tags reports
// @Accept json
// @Produce json
// @Param brand_ids query []string false "Brand IDs"
// @Param apps query []string false "Apps/Sales channels"
// @Param from query string true "Start time (RFC3339 format)"
// @Param to query string true "End time (RFC3339 format)"
// @Param page query int true "Page number" minimum(1)
// @Param items_per_page query int true "Items per page" minimum(1) maximum(100)
// @Success 200 {object} models.PaginationResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /reports/order-feedbacks [get]
func GetOrderFeedbacks(c *gin.Context) {
	db := middlewares.GetDB(c)
	user := middlewares.GetUser(c)

	// Parse query parameters
	var query models.ReportDetailQuery
	if err := c.ShouldBindQuery(&query); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Set default pagination values if not provided
	if query.Page == 0 {
		query.Page = 1
	}
	if query.ItemsPerPage == 0 {
		query.ItemsPerPage = 20
	}

	// Validate pagination limits
	if query.ItemsPerPage > 100 {
		query.ItemsPerPage = 100
	}

	// Check if user has access to the brands if specific brands are requested
	if len(query.BrandIDs) > 0 {
		for _, brandID := range query.BrandIDs {
			if !contains(user.Brands, brandID) {
				c.JSON(http.StatusForbidden, gin.H{
					"success": false,
					"error":   "You don't have permission to access one or more of the requested brands",
				})
				return
			}
		}
	}

	// Create report service
	reportService := service.NewReportService(db)

	// Get paginated order feedbacks
	result, err := reportService.GetOrderFeedbacksPaginated(query)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, result)
}

// ExportOrderFeedbacks godoc
// @Summary Export order feedbacks to Excel
// @Description Exports order feedbacks to Excel with detailed information
// @Tags reports
// @Accept json
// @Produce json
// @Param brand_ids query []string false "Brand IDs"
// @Param apps query []string false "Apps/Sales channels"
// @Param from query string true "Start time (RFC3339 format)"
// @Param to query string true "End time (RFC3339 format)"
// @Param headers query []string false "Selected headers to export (if not provided, all headers will be exported)"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /reports/export-order-feedbacks [get]
func ExportOrderFeedbacks(c *gin.Context) {
	db := middlewares.GetDB(c)
	user := middlewares.GetUser(c)

	// Parse query parameters
	var query models.ReportDetailQuery
	if err := c.ShouldBindQuery(&query); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Parse selected headers
	selectedHeaders := c.QueryArray("headers")

	// Check if user has access to the brands if specific brands are requested
	if len(query.BrandIDs) > 0 {
		for _, brandID := range query.BrandIDs {
			if !contains(user.Brands, brandID) {
				c.JSON(http.StatusForbidden, gin.H{
					"success": false,
					"error":   "You don't have permission to access one or more of the requested brands",
				})
				return
			}
		}
	}

	// Create report service
	reportService := service.NewReportService(db)

	// Get order feedbacks data for Excel
	excelData, err := reportService.GetOrderFeedbacksForExcel(query)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Get all available headers
	allHeaders := getOrderFeedbacksHeaders()

	// Convert data to interface{} slice for Excel generation
	var allData [][]interface{}
	for _, item := range excelData {
		// Format date/time fields
		var ratingDate, ratingTime, orderDate, orderTime, deliveryDate, deliveryTime string

		if item.RatingDateTime != nil {
			ratingDate = item.RatingDateTime.Format("02-01-2006")
			ratingTime = item.RatingDateTime.Format("15:04:05")
		}

		if item.OrderDateTime != nil {
			orderDate = item.OrderDateTime.Format("02-01-2006")
			orderTime = item.OrderDateTime.Format("15:04:05")
		}

		if item.DeliveryDateTime != nil {
			deliveryDate = item.DeliveryDateTime.Format("02-01-2006")
			deliveryTime = item.DeliveryDateTime.Format("15:04:05")
		}

		row := []interface{}{
			item.STT,
			item.ReferenceCode,
			item.OrderCode,
			item.Source,
			item.HubName,
			item.BrandName,
			item.StoreName,
			item.CustomerName,
			item.Rating,
			item.Comment,
			ratingDate,
			ratingTime,
			orderDate,
			orderTime,
			deliveryDate,
			deliveryTime,
		}
		allData = append(allData, row)
	}

	// Filter headers and data based on selected headers
	headers, data := filterHeadersAndData(allHeaders, allData, selectedHeaders)

	// Generate Excel file
	excelBuffer, err := excel.GenerateExcel("Order Feedbacks", headers, data)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   fmt.Sprintf("Error generating Excel file: %v", err),
		})
		return
	}

	// Upload to storage
	timestamp := time.Now().Format("20060102_150405")
	fileName := fmt.Sprintf("order_feedbacks_%s.xlsx", timestamp)
	fileURL, err := excel.UploadExcel("nexpos-files", fmt.Sprintf("reports/%s", fileName), excelBuffer)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   fmt.Sprintf("Error uploading Excel file: %v", err),
		})
		return
	}

	// Return file URL
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"file_url":  fileURL,
			"file_name": fileName,
		},
	})
}

// Helper function to check if a slice contains a string
func contains(slice []string, str string) bool {
	for _, item := range slice {
		if item == str {
			return true
		}
	}
	return false
}

// HeaderItem represents a single header item with key and label
type HeaderItem struct {
	Key   string `json:"key"`
	Label string `json:"label"`
	Type  string `json:"type,omitempty"`
}

// getCompletedOrdersHeaders returns the available headers for completed orders report
func getCompletedOrdersHeaders() []HeaderItem {
	return []HeaderItem{
		{Key: "stt", Label: "STT", Type: "number"},
		{Key: "reference_code", Label: "Mã tham chiếu", Type: "string"},
		{Key: "order_code", Label: "Mã đơn hàng", Type: "string"},
		{Key: "source", Label: "Kênh bán", Type: "string"},
		{Key: "hub_name", Label: "Điểm bán", Type: "string"},
		{Key: "brand_name", Label: "Thương hiệu", Type: "string"},
		{Key: "site_name", Label: "Gian hàng", Type: "string"},
		{Key: "revenue_before_promo", Label: "Doanh thu trước KM (1)", Type: "currency"},
		{Key: "product_discount", Label: "Giảm giá sản phẩm (2)", Type: "currency"},
		{Key: "revenue_after_product_discount", Label: "Thành tiền sau giảm giá SP (3) = (1) - (2)", Type: "currency"},
		{Key: "order_discount", Label: "Giảm giá tổng đơn (4)", Type: "currency"},
		{Key: "total_promotion", Label: "Tổng KM (5) = (2) + (4)", Type: "currency"},
		{Key: "revenue_after_promo", Label: "Doanh thu sau KM (6) = (1) - (5)", Type: "currency"},
		{Key: "additional_income", Label: "Thu khác (7)", Type: "currency"},
		{Key: "shipping_fee", Label: "Tiền ship (8)", Type: "currency"},
		{Key: "customer_paid", Label: "Thực thu khách hàng (9) = (6) + (7) + (8)", Type: "currency"},
		{Key: "promo_code", Label: "Mã KM", Type: "string"},
		{Key: "order_date", Label: "Ngày đặt hàng", Type: "date"},
		{Key: "order_time", Label: "Giờ đặt hàng", Type: "time"},
		{Key: "delivery_date", Label: "Ngày giao hàng", Type: "date"},
		{Key: "delivery_time", Label: "Giờ giao hàng", Type: "time"},
	}
}

// getBrandReportHeaders returns the available headers for brand report
func getBrandReportHeaders() []HeaderItem {
	return []HeaderItem{
		{Key: "brand_name", Label: "Thương hiệu", Type: "string"},
		{Key: "revenue_before_promo", Label: "Doanh thu trước KM (1)", Type: "currency"},
		{Key: "total_promotion", Label: "Tổng KM (2)", Type: "currency"},
		{Key: "revenue_after_promo", Label: "Doanh thu sau KM (3) = (1) - (2)", Type: "currency"},
	}
}

// getCancelledOrdersHeaders returns the available headers for cancelled orders report
func getCancelledOrdersHeaders() []HeaderItem {
	return []HeaderItem{
		{Key: "stt", Label: "STT", Type: "number"},
		{Key: "reference_code", Label: "Mã tham chiếu", Type: "string"},
		{Key: "order_code", Label: "Mã đơn hàng", Type: "string"},
		{Key: "source", Label: "Kênh bán", Type: "string"},
		{Key: "hub_name", Label: "Điểm bán", Type: "string"},
		{Key: "brand_name", Label: "Thương hiệu", Type: "string"},
		{Key: "site_name", Label: "Gian hàng", Type: "string"},
		{Key: "item_quantity", Label: "Số lượng món", Type: "number"},
		{Key: "order_date", Label: "Ngày đặt hàng", Type: "date"},
		{Key: "order_time", Label: "Giờ đặt hàng", Type: "time"},
		{Key: "cancelled_by", Label: "Hủy bởi", Type: "string"},
		{Key: "cancel_reason", Label: "Lý do hủy", Type: "string"},
	}
}

// getOrderFeedbacksHeaders returns the available headers for order feedbacks report
func getOrderFeedbacksHeaders() []HeaderItem {
	return []HeaderItem{
		{Key: "stt", Label: "STT", Type: "number"},
		{Key: "reference_code", Label: "Mã tham chiếu", Type: "string"},
		{Key: "order_code", Label: "Mã đơn hàng", Type: "string"},
		{Key: "source", Label: "Kênh bán", Type: "string"},
		{Key: "hub_name", Label: "Điểm bán", Type: "string"},
		{Key: "brand_name", Label: "Thương hiệu", Type: "string"},
		{Key: "site_name", Label: "Gian hàng", Type: "string"},
		{Key: "customer_name", Label: "Tên KH", Type: "string"},
		{Key: "rating_score", Label: "Điểm đánh giá", Type: "number"},
		{Key: "comment", Label: "Bình luận", Type: "string"},
		{Key: "rating_date", Label: "Ngày đánh giá", Type: "date"},
		{Key: "rating_time", Label: "Giờ đánh giá", Type: "time"},
		{Key: "order_date", Label: "Ngày đặt hàng", Type: "date"},
		{Key: "order_time", Label: "Giờ đặt hàng", Type: "time"},
		{Key: "delivery_date", Label: "Ngày giao hàng", Type: "date"},
		{Key: "delivery_time", Label: "Giờ giao hàng", Type: "time"},
	}
}

// filterHeadersAndData filters headers and data based on selected headers
func filterHeadersAndData(allHeaders []HeaderItem, allData [][]interface{}, selectedHeaders []string) ([]string, [][]interface{}) {
	if len(selectedHeaders) == 0 {
		// Convert HeaderItem to string labels for Excel export
		var headerLabels []string
		for _, header := range allHeaders {
			headerLabels = append(headerLabels, header.Label)
		}
		return headerLabels, allData
	}

	// Find indices of selected headers by matching keys or labels
	var indices []int
	var filteredHeaders []string

	for _, selectedHeader := range selectedHeaders {
		for i, header := range allHeaders {
			if header.Key == selectedHeader || header.Label == selectedHeader {
				indices = append(indices, i)
				filteredHeaders = append(filteredHeaders, header.Label)
				break
			}
		}
	}

	// Filter data based on selected indices
	var filteredData [][]interface{}
	for _, row := range allData {
		var filteredRow []interface{}
		for _, index := range indices {
			if index < len(row) {
				filteredRow = append(filteredRow, row[index])
			}
		}
		filteredData = append(filteredData, filteredRow)
	}

	return filteredHeaders, filteredData
}

// GetCompletedOrdersHeaders godoc
// @Summary Get available headers for completed orders report
// @Description Returns the list of available headers for completed orders report export
// @Tags reports
// @Accept json
// @Produce json
// @Success 200 {object} map[string]interface{}
// @Router /reports/header-completed-orders [get]
func GetCompletedOrdersHeaders(c *gin.Context) {
	headers := getCompletedOrdersHeaders()
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    headers,
	})
}

// GetBrandReportHeaders godoc
// @Summary Get available headers for brand report
// @Description Returns the list of available headers for brand report export
// @Tags reports
// @Accept json
// @Produce json
// @Success 200 {object} map[string]interface{}
// @Router /reports/header-brand-report [get]
func GetBrandReportHeaders(c *gin.Context) {
	headers := getBrandReportHeaders()
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    headers,
	})
}

// GetCancelledOrdersHeaders godoc
// @Summary Get available headers for cancelled orders report
// @Description Returns the list of available headers for cancelled orders report export
// @Tags reports
// @Accept json
// @Produce json
// @Success 200 {object} map[string]interface{}
// @Router /reports/header-cancelled-orders [get]
func GetCancelledOrdersHeaders(c *gin.Context) {
	headers := getCancelledOrdersHeaders()
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    headers,
	})
}

// GetOrderFeedbacksHeaders godoc
// @Summary Get available headers for order feedbacks report
// @Description Returns the list of available headers for order feedbacks report export
// @Tags reports
// @Accept json
// @Produce json
// @Success 200 {object} map[string]interface{}
// @Router /reports/header-order-feedbacks [get]
func GetOrderFeedbacksHeaders(c *gin.Context) {
	headers := getOrderFeedbacksHeaders()
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    headers,
	})
}
