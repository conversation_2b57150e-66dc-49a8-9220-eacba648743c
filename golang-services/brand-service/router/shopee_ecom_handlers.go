package router

import (
	"fmt"
	"net/http"
	"net/url"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	shopee_ecom "github.com/nexdorvn/nexpos-backend/golang-services/sdk/merchant/shopee-ecom"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/middlewares"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"
)

// CustomToken extends models.Token with additional fields needed for our implementation
type CustomToken struct {
	models.Token
	Source string            // Source identifier (e.g., "shopee_ecom")
	Params map[string]string // Additional parameters
}

// ShopeeEcomTokenAdapter adapts between our app's model and the SDK's model
type ShopeeEcomTokenAdapter struct {
	ShopID       int64  `json:"shop_id"`
	AccessToken  string `json:"access_token"`
	RefreshToken string `json:"refresh_token"`
	ExpireIn     int    `json:"expire_in"`
	Source       string `json:"source"`
}

// CustomShopeeEcomToken is a local extension of models.ShopeeEcomToken with additional fields
// This allows us to work around the missing fields in the SDK's struct
type CustomShopeeEcomToken struct {
	models.ShopeeEcomToken     // Embed the original struct
	ExpireIn               int `json:"expire_in"` // Add the missing ExpireIn field
}

// GetShopeeEcomAuthURL generates and returns an authorization URL for Shopee E-commerce integration
func GetShopeeEcomAuthURL(c *gin.Context) {
	// Define request structure with appropriate JSON tags
	type AuthURLRequest struct {
		BrandID string `json:"brand_id" binding:"required"`
		// SiteID      string `json:"site_id" binding:"required"`
		AppType     string `json:"app_type"`
		CallbackURL string `json:"callback_url" binding:"required"`
	}

	// Parse request body
	var req AuthURLRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		// Handle empty request body or JSON parsing errors
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Invalid request format",
			"details": err.Error(),
		})
		return
	}

	// Validate required fields
	if req.BrandID == "" || req.CallbackURL == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Missing required fields",
			"details": "brand_id, site_id, and callback_url are required",
		})
		return
	}

	// Set default app type if not provided
	if req.AppType == "" {
		req.AppType = string(shopee_ecom.DefaultApp)
	}

	// Validate callback URL format
	_, err := url.Parse(req.CallbackURL)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Invalid callback URL format",
		})
		return
	}

	// Create Shopee E-commerce client with the requested app type
	client := shopee_ecom.NewShopeeEcomClientWithType(shopee_ecom.AppType(req.AppType))

	// Generate a state token to prevent CSRF attacks
	stateToken := uuid.New().String()

	// Generate the authorization URL using the provided callback URL
	authURL, err := client.GenerateAuthURL(req.CallbackURL, stateToken)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   fmt.Sprintf("Failed to generate authorization URL: %v", err),
		})
		return
	}

	// Return success response with the generated auth URL
	c.JSON(http.StatusOK, gin.H{
		"success":  true,
		"auth_url": authURL,
	})
}

// HandleShopeeEcomCallback processes the callback from Shopee after user authorization
// It handles the OAuth token exchange, creates a new site, saves the credentials, and returns a structured response
func HandleShopeeEcomCallback(c *gin.Context) {
	// Define response structure for consistent API responses
	type CallbackResponse struct {
		Success  bool   `json:"success"`             // Whether the operation was successful
		Message  string `json:"message,omitempty"`   // Success or error message
		Error    string `json:"error,omitempty"`     // Error details (only set when success is false)
		ShopID   string `json:"shop_id,omitempty"`   // The Shopee shop ID
		ShopName string `json:"shop_name,omitempty"` // The Shopee shop name
		SiteID   string `json:"site_id,omitempty"`   // The site ID in our system
	}

	// Handle POST request with JSON body
	var req struct {
		Code    string `json:"code" binding:"required"`     // Required parameter
		BrandID string `json:"brand_id" binding:"required"` // Required parameter
		ShopID  string `json:"shop_id" binding:"required"`  // Required parameter
		HubID   string `json:"hub_id" binding:"required"`
	}

	// Parse the request body into the request struct
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, CallbackResponse{
			Success: false,
			Error:   "Invalid request format",
			Message: "Required parameters missing or invalid. Ensure shop_id, brand_id, and code are provided.",
		})
		return
	}

	// Extract values from the request
	code := req.Code
	shopIDStr := req.ShopID
	brandID := req.BrandID

	// Use default app type if not provided
	appType := string(shopee_ecom.DefaultApp)

	// Parse the Shopee shop ID to int64 as required by the SDK
	shopID, err := strconv.ParseInt(shopIDStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, CallbackResponse{
			Success: false,
			Error:   "Invalid shop ID format",
			Message: "The shop ID is not in a valid format",
		})
		return
	}

	// Initialize the Shopee E-commerce client with the specified app type
	client := shopee_ecom.NewShopeeEcomClientWithType(shopee_ecom.AppType(appType))

	// Create a token object to store the authentication results
	sdkToken := &models.Token{
		SiteID: shopIDStr,
	}

	// Exchange the authorization code for an access token
	err = client.AuthorizeWithCode(sdkToken, code, shopID)
	if err != nil {
		println("ERROR: Failed to authorize with Shopee:", err.Error())
		c.JSON(http.StatusInternalServerError, CallbackResponse{
			Success: false,
			Error:   "Authorization failed",
			Message: fmt.Sprintf("Failed to authorize with Shopee: %v", err),
		})
		return
	}

	// Create a custom token object with the fields needed by our application
	customToken := &CustomShopeeEcomToken{
		ShopeeEcomToken: models.ShopeeEcomToken{
			ShopID:       shopID,
			AccessToken:  sdkToken.AccessToken,
			RefreshToken: sdkToken.RefreshToken,
			TokenCode:    fmt.Sprintf("%s_shopee_ecom", shopIDStr), // Create a unique token code
		},
		// Capture expire_in if available in the response
		// ExpireIn: sdkToken.ExpireIn,
	}

	// Check if expire_in was provided in the token params
	if expireInStr, ok := sdkToken.Params["expire_in"]; ok {
		if expireIn, err := strconv.Atoi(expireInStr); err == nil {
			customToken.ExpireIn = expireIn
		}
	}

	// Get database connection
	db := middlewares.GetDB(c)

	// Check if brand exists
	var brand models.Brand
	if err := db.Where("id = ?", brandID).First(&brand).Error; err != nil {
		c.JSON(http.StatusBadRequest, CallbackResponse{
			Success: false,
			Error:   "Brand not found",
			Message: fmt.Sprintf("Could not find brand with ID: %s", brandID),
		})
		return
	}

	// Create unique code and shop name for the site
	shopName := fmt.Sprintf("Shopee Shop %s", shopIDStr)
	siteCode := fmt.Sprintf("SHOPEE_ECOM_%s", shopIDStr)

	// First check if site already exists using FirstOrInit pattern
	site := models.Site{}
	if err := db.Where("code = ?", siteCode).FirstOrInit(&site).Error; err != nil {
		c.JSON(http.StatusInternalServerError, CallbackResponse{
			Success: false,
			Error:   "Database error",
			Message: fmt.Sprintf("Failed to initialize site record: %v", err),
		})
		return
	}

	// Set site properties with struct initialization approach
	site = models.Site{
		// Preserve existing ID if site already exists
		ID: site.ID,

		// Core identification
		BrandID:    brandID,
		HubID:      req.HubID,
		Name:       shopName,
		Code:       siteCode,
		Type:       "mart",
		MainSource: "shopee_ecom",

		// Basic information
		Address:     "",
		Description: "",

		// Shopee-specific token
		ShopeeEcomToken: &models.JSONField[models.ShopeeEcomToken]{
			Data: customToken.ShopeeEcomToken,
		},

		// Required fields with default values
		Active:      true,
		AutoConfirm: false,
		AutoPrint:   false,

		// Empty JSON fields
		AddressObj: models.JSONField[models.AddressObj]{},
		Tokens:     models.JSONArray[models.SiteToken]{},
		HubIDs:     models.JSONArray[string]{},

		// Settings
		ApplyCommission:          false,
		ApplyGift:                false,
		UseCoreProduct:           false,
		EnableLocalOrderShipping: false,
		IsHeadSite:               false,
		IsHESite:                 false,
		PreparationTimePerOrder:  0,

		// Initialize PauseApps with empty map
		PauseApps: models.JSONField[map[string]bool]{
			Data: make(map[string]bool),
		},
	}

	// Create token code using shop ID (similar to username + source in other integrations)
	tokenCode := fmt.Sprintf("%s_shopee_ecom", shopIDStr)

	// Calculate token expiration time based on ExpireIn value
	// If ExpireIn is available, use it; otherwise default to 15 days
	var expiredAt time.Time
	if customToken.ExpireIn > 0 {
		// Convert ExpireIn from seconds to a duration
		expiredAt = time.Now().Add(time.Duration(customToken.ExpireIn) * time.Second)
	} else {
		// Default expiration time (15 days) as seen in other implementations
		expiredAt = time.Now().AddDate(0, 0, 15)
	}

	// Create or update TokenAccount record
	// This stores the authentication credentials in a centralized table
	tokenAccount := models.TokenAccount{
		TokenCode:    tokenCode,
		Source:       "shopee_ecom",
		SiteID:       shopIDStr, // Store Shopee shop ID
		SiteName:     shopName,  // Store Shopee shop name
		AccessToken:  sdkToken.AccessToken,
		RefreshToken: sdkToken.RefreshToken,
		ExpiredAt:    expiredAt,
		// Username and Password are empty since this is OAuth-based authentication
	}

	// First try to update existing record, then create if it doesn't exist
	// This is the FirstOrCreate pattern in GORM
	result := db.Model(&models.TokenAccount{}).
		Where("token_code = ?", tokenCode).
		Where("source = ?", "shopee_ecom").
		Updates(tokenAccount)

	if result.Error != nil {
		// Log error but continue with site creation
		fmt.Printf("Warning: Failed to update TokenAccount: %v\n", result.Error)
	} else if result.RowsAffected == 0 {
		// No existing record was updated, so create a new one
		if err := db.Create(&tokenAccount).Error; err != nil {
			// Log error but continue with site creation
			fmt.Printf("Warning: Failed to create TokenAccount: %v\n", err)
		}
	}

	// Save the site to the database with proper error handling
	var saveErr error
	if site.ID == "" {
		// This is a new site (Create)
		saveErr = db.Create(&site).Error
	} else {
		// This is an existing site (Update)
		saveErr = db.Save(&site).Error
	}

	if saveErr != nil {
		// Log the error details for debugging
		fmt.Printf("Database error saving site: %v\n", saveErr)

		// Return a structured error response to the client
		c.JSON(http.StatusInternalServerError, CallbackResponse{
			Success: false,
			Error:   "Failed to save site in database",
			Message: fmt.Sprintf("Database error: %v", saveErr),
		})
		return
	}

	// Return a success response with relevant shop information
	c.JSON(http.StatusOK, CallbackResponse{
		Success:  true,
		Message:  "Successfully connected to Shopee E-commerce",
		ShopID:   shopIDStr,
		ShopName: shopName,
		SiteID:   site.ID,
	})
}

// GetShopeeEcomTokenFromSite constructs a Token object from a site's ShopeeEcomToken
// This function is used to prepare tokens for use with the Shopee E-commerce SDK
func GetShopeeEcomTokenFromSite(site *models.Site) *models.Token {
	// If site has no Shopee E-commerce token, return nil
	if site.ShopeeEcomToken == nil {
		return nil
	}

	// Create a token to return
	token := &models.Token{
		SiteID:       strconv.FormatInt(site.ShopeeEcomToken.Data.ShopID, 10),
		AccessToken:  site.ShopeeEcomToken.Data.AccessToken,
		RefreshToken: site.ShopeeEcomToken.Data.RefreshToken,
		Params:       make(map[string]string),
	}

	// We can't directly access ExpireIn field since it doesn't exist in the SDK model
	// Instead we'll need to use our adapter pattern or another approach
	// In a production implementation, we might store this in a separate storage

	return token
}

// SaveShopeeEcomTokenToSite updates a site's ShopeeEcomToken field with data from a Token object
// This function should be called after a token has been refreshed by the SDK
// func SaveShopeeEcomTokenToSite(site *models.Site, token *models.Token) error {
// 	// If token is nil, return error
// 	if token == nil {
// 		return fmt.Errorf("cannot save nil token")
// 	}

// 	// Convert shopID to int64
// 	shopID, err := strconv.ParseInt(token.SiteID, 10, 64)
// 	if err != nil {
// 		return fmt.Errorf("invalid shop ID format: %s", token.SiteID)
// 	}

// 	// Initialize ShopeeEcomToken if nil
// 	if site.ShopeeEcomToken == nil {
// 		site.ShopeeEcomToken = &models.JSONField[models.ShopeeEcomToken]{
// 			Data: models.ShopeeEcomToken{
// 				ShopID: shopID,
// 			},
// 		}
// 	}

// 	// Update token data
// 	site.ShopeeEcomToken.Data.AccessToken = token.AccessToken
// 	site.ShopeeEcomToken.Data.TokenCode = tokenCode
// 	site.ShopeeEcomToken.Data.RefreshToken = token.RefreshToken

// 	// We can't set ExpireIn directly as it doesn't exist in the SDK model
// 	// In a production implementation, we might store this in a separate storage
// 	// or use a custom JSON serialization to include it in the ShopeeEcomToken

// 	return nil
// }
