# Export APIs Testing Guide

This document provides testing instructions for the new export APIs.

## Prerequisites

1. Ensure the brand-service is running
2. Have valid authentication token
3. Have test data with completed orders in the database

## API Endpoints

### 1. Export Completed Orders

**Endpoint:** `GET /v1/brand-service/reports/export-completed-orders`

**Test Request:**
```bash
curl -X GET "http://localhost:3000/v1/brand-service/reports/export-completed-orders?from=2024-01-01T00:00:00Z&to=2024-12-31T23:59:59Z" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -H "Content-Type: application/json"
```

**With Filters:**
```bash
curl -X GET "http://localhost:3000/v1/brand-service/reports/export-completed-orders?from=2024-01-01T00:00:00Z&to=2024-12-31T23:59:59Z&brand_ids[]=BRAND_ID_1&brand_ids[]=BRAND_ID_2&apps[]=grab&apps[]=shopee" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -H "Content-Type: application/json"
```

**Expected Response:**
```json
{
  "success": true,
  "data": {
    "file_url": "https://storage.googleapis.com/nexpos-files/reports/completed_orders_20240109_143022.xlsx",
    "file_name": "completed_orders_20240109_143022.xlsx"
  }
}
```

### 2. Export Brand Report

**Endpoint:** `GET /v1/brand-service/reports/export-brand-report`

**Test Request:**
```bash
curl -X GET "http://localhost:3000/v1/brand-service/reports/export-brand-report?from=2024-01-01T00:00:00Z&to=2024-12-31T23:59:59Z" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -H "Content-Type: application/json"
```

**Expected Response:**
```json
{
  "success": true,
  "data": {
    "file_url": "https://storage.googleapis.com/nexpos-files/reports/brand_report_20240109_143022.xlsx",
    "file_name": "brand_report_20240109_143022.xlsx"
  }
}
```

## Excel File Verification

### Completed Orders Excel Format
The downloaded Excel file should contain columns:
- STT
- Mã tham chiếu
- Mã đơn hàng
- Kênh bán
- Điểm bán
- Thương hiệu
- Gian hàng
- Doanh thu trước KM (1)
- Giảm giá sản phẩm (2)
- Thành tiền sau giảm giá SP (3) = (1) - (2)
- Giảm giá tổng đơn (4)
- Tổng KM (5) = (2) + (4)
- Doanh thu sau KM (6) = (1) - (5)
- Thu khác (7)
- Tiền ship (8)
- Thực thu khách hàng (9) = (6) + (7) + (8)
- Mã KM
- Ngày đặt hàng
- Giờ đặt hàng
- Ngày giao hàng
- Giờ giao hàng

### Brand Report Excel Format
The downloaded Excel file should contain columns:
- Thương hiệu
- Doanh thu trước KM (1)
- Tổng KM (2)
- Doanh thu sau KM (3) = (1) - (2)

With brands listed alphabetically and a "TỔNG" row at the end.

## Error Cases to Test

1. **Missing required parameters:**
```bash
curl -X GET "http://localhost:3000/v1/brand-service/reports/export-completed-orders" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```
Expected: 400 Bad Request

2. **Invalid date format:**
```bash
curl -X GET "http://localhost:3000/v1/brand-service/reports/export-completed-orders?from=invalid-date&to=2024-12-31T23:59:59Z" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```
Expected: 400 Bad Request

3. **Unauthorized access:**
```bash
curl -X GET "http://localhost:3000/v1/brand-service/reports/export-completed-orders?from=2024-01-01T00:00:00Z&to=2024-12-31T23:59:59Z"
```
Expected: 401 Unauthorized

4. **Access to restricted brands:**
```bash
curl -X GET "http://localhost:3000/v1/brand-service/reports/export-completed-orders?from=2024-01-01T00:00:00Z&to=2024-12-31T23:59:59Z&brand_ids[]=RESTRICTED_BRAND_ID" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```
Expected: 403 Forbidden

## Performance Testing

For large datasets, monitor:
- Response time
- Memory usage
- File size
- Upload time to storage

## Notes

- Both APIs use the same authentication and permission checks as existing report APIs
- Files are uploaded to Google Cloud Storage in the "nexpos-files" bucket under "reports/" folder
- File names include timestamp to avoid conflicts
- Both APIs support the same filtering options as the report detail API
