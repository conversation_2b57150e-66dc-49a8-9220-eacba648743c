# Brand Service

This service handles brand-related operations including brand management, hub management, site management, and utility endpoints.

## API Endpoints

### Map API

#### GET `/v1/brand-service/map/v2/suggest_addresses`

Returns address suggestions based on the input query.

**Query Parameters:**
- `address` (required): The address query string

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "formatted_address": "123 Test Street, District 1, Ho Chi Minh City, Vietnam",
      "location": {
        "lat": 10.7758439,
        "lng": 106.7017555
      },
      "province_name": "Ho Chi Minh City",
      "district_name": "District 1",
      "ward_name": "Ben Nghe Ward",
      "route": "Test Street"
    },
    ...
  ]
}
```

### Reports API

#### GET `/v1/brand-service/reports/export-completed-orders`

Exports completed orders to Excel with detailed financial information. Uses the same query parameters as the report detail API but exports all completed orders (no pagination) to an Excel file.

**Query Parameters:**
- `brand_ids` (optional): Array of brand IDs to filter by
- `apps` (optional): Array of sales channels/apps to filter by (e.g., "grab", "shopee", "be")
- `from` (required): Start time in RFC3339 format (e.g., "2024-01-01T00:00:00Z")
- `to` (required): End time in RFC3339 format (e.g., "2024-01-31T23:59:59Z")

**Response:**
```json
{
  "success": true,
  "data": {
    "file_url": "https://storage.googleapis.com/nexpos-files/reports/completed_orders_20240109_143022.xlsx",
    "file_name": "completed_orders_20240109_143022.xlsx"
  }
}
```

**Excel Format:**
The exported Excel file contains the following columns:
- STT (Sequential number)
- Mã tham chiếu (Reference code)
- Mã đơn hàng (Order code)
- Kênh bán (Sales channel)
- Điểm bán (Point of sale)
- Thương hiệu (Brand)
- Gian hàng (Store)
- Doanh thu trước KM (1) (Revenue before promotion)
- Giảm giá sản phẩm (2) (Product discount)
- Thành tiền sau giảm giá SP (3) = (1) - (2) (Revenue after product discount)
- Giảm giá tổng đơn (4) (Order discount)
- Tổng KM (5) = (2) + (4) (Total promotion)
- Doanh thu sau KM (6) = (1) - (5) (Revenue after promotion)
- Thu khác (7) (Additional income)
- Tiền ship (8) (Shipping fee)
- Thực thu khách hàng (9) = (6) + (7) + (8) (Customer paid)
- Mã KM (Promotion code)
- Ngày đặt hàng (Order date)
- Giờ đặt hàng (Order time)
- Ngày giao hàng (Delivery date)
- Giờ giao hàng (Delivery time)

#### GET `/v1/brand-service/reports/export-brand-report`

Exports completed orders grouped by brand to Excel with financial summary. Uses the same query parameters as the report detail API but groups data by brand and shows totals.

**Query Parameters:**
- `brand_ids` (optional): Array of brand IDs to filter by
- `apps` (optional): Array of sales channels/apps to filter by (e.g., "grab", "shopee", "be")
- `from` (required): Start time in RFC3339 format (e.g., "2024-01-01T00:00:00Z")
- `to` (required): End time in RFC3339 format (e.g., "2024-01-31T23:59:59Z")

**Response:**
```json
{
  "success": true,
  "data": {
    "file_url": "https://storage.googleapis.com/nexpos-files/reports/brand_report_20240109_143022.xlsx",
    "file_name": "brand_report_20240109_143022.xlsx"
  }
}
```

**Excel Format:**
The exported Excel file contains the following columns:
- Thương hiệu (Brand name)
- Doanh thu trước KM (1) (Revenue before promotion)
- Tổng KM (2) (Total promotion)
- Doanh thu sau KM (3) = (1) - (2) (Revenue after promotion)

The report includes:
- One row per brand with aggregated financial data
- Brands sorted alphabetically
- A "TỔNG" (Total) row at the end with grand totals

## Running the Service

```bash
go run main.go start --port 3000
```

## Testing

```bash
go test ./...
```
