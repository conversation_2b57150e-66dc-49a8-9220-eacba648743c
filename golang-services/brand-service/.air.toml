# Air configuration for Go hot-reloading (idiomatic Go style)
root = "."
tmp_dir = "tmp"

[build]
# Command to build the application
cmd = "go build -o ./tmp/main ."
# Binary file path
bin = "./tmp/main start"  # Add the "start" command here
# File extensions to watch for changes
include_ext = ["go", "yml", "yaml", "json"]
# Directories to exclude from watching
exclude_dir = ["tmp", "vendor", ".git"]
# Files to exclude from watching
exclude_file = []
# Delay between each check (in milliseconds)
delay = 1000
# Stop running old binary when build fails
stop_on_error = true
# Send interrupt signal before killing process
send_interrupt = false

[log]
# Show log time
time = true

[color]
# Customize colors
main = "magenta"
watcher = "cyan"
build = "yellow"
runner = "green"

[misc]
# Clean temporary directory on exit
clean_on_exit = true
