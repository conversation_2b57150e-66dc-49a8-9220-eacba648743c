// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package di

import (
	"github.com/gin-gonic/gin"
	"github.com/google/wire"
	"github.com/nexdorvn/nexpos-backend/golang-services/brand-service/router"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/di"
)

// Injectors from wire.go:

// InitializeApp sets up the application with all dependencies
func InitializeApp() (*gin.Engine, error) {
	db, err := di.ProvideDB()
	if err != nil {
		return nil, err
	}
	client, err := di.ProvideStorageClient()
	if err != nil {
		return nil, err
	}
	localizer, err := di.ProvideLocalizer()
	if err != nil {
		return nil, err
	}
	redisClient, err := di.ProvideRedisClient()
	if err != nil {
		return nil, err
	}
	rabbitClient, err := di.ProvideRabbitMQClient()
	if err != nil {
		return nil, err
	}
	middlewareProvider := di.NewMiddlewareProvider(db, client, localizer, redisClient, rabbitClient)
	engine := ProvideRouter(middlewareProvider)
	return engine, nil
}

// wire.go:

// ServiceSet provides the service-specific providers
var ServiceSet = wire.NewSet(
	ProvideRouter,
)

// ProvideRouter creates a new Gin router with all middleware and routes
func ProvideRouter(middlewareProvider *di.MiddlewareProvider) *gin.Engine {
	r := gin.New()

	r.Use(middlewareProvider.CorsMiddleware())
	r.Use(middlewareProvider.GormMiddleware())
	r.Use(middlewareProvider.GoogleStorageMiddleware())
	r.Use(middlewareProvider.ErrorLocalizeMiddleware())
	r.Use(middlewareProvider.RedisMiddleware())
	r.Use(middlewareProvider.RabbitMQMiddleware())

	router.LoadHandlers(r)

	r.Use(gin.Logger())
	r.Use(gin.Recovery())

	return r
}
