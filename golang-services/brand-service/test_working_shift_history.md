# Working Shift History API Test

## API Endpoint
```
GET /v1/brand-service/hubs/:hub_id/working-shifts/history
```

## Query Parameters
- `page` (int, optional): Page number (default: 1)
- `limit` (int, optional): Items per page (default: 10, max: 100)
- `start_date` (string, optional): Start date in DD/MM/YYYY format
- `end_date` (string, optional): End date in DD/MM/YYYY format
- `employee_id` (string, optional): Filter by employee ID
- `employee_name` (string, optional): Filter by employee name (partial match)
- `status` (string, optional): Filter by shift status (open/closed)

## Example Request
```bash
curl -X GET "http://localhost:3000/v1/brand-service/hubs/hub_123/working-shifts/history?page=1&limit=10&start_date=01/01/2025&end_date=20/01/2025" \
  -H "Authorization: Bearer <access_token>" \
  -H "Content-Type: application/json"
```

## Expected Response Format
```json
{
  "success": true,
  "data": [
    {
      "start_date": "01/01/2025",
      "end_date": "20/01/2025",
      "start_date_time": "06:00",
      "end_date_time": "11:00",
      "employee_id": "user_123",
      "employee_name": "Nguyen Huu Dai",
      "initial_cash": 2500000,
      "total_cash": 3282000,
      "total_shift": 720000,
      "group_by_order": [
        {
          "order_id": "ORD20250505-621",
          "quantity": 6,
          "channel": "SPF - Gà nướng OOO",
          "amount": 135000
        }
      ],
      "group_by_payment_method": [
        {
          "payment_method": "CASH",
          "total_amount": 100000,
          "net_amount": 80000
        }
      ]
    }
  ],
  "totalDocs": 45,
  "page": 1,
  "limit": 10,
  "totalPages": 5,
  "hasNextPage": true,
  "hasPrevPage": false
}
```

## Error Response Format
```json
{
  "success": false,
  "error_code": "FETCH_HISTORY_FAILED",
  "error_message": "Error description"
}
```

## Features Implemented
1. ✅ Paginated response with standard pagination fields
2. ✅ Date range filtering (DD/MM/YYYY format)
3. ✅ Employee filtering by ID and name
4. ✅ Status filtering (open/closed)
5. ✅ Order grouping by order_id with quantity, channel, and amount
6. ✅ Payment method grouping with total and net amounts
7. ✅ Proper error handling with error codes
8. ✅ Authentication and permission checks
9. ✅ Time formatting (HH:MM format)
10. ✅ Channel mapping from order source

## Notes
- The API returns data in the exact format specified in the user's JSON example
- Date filtering uses DD/MM/YYYY format as shown in the example
- Channel names are mapped from order sources (grab -> Grab, pos -> POS, etc.)
- Net amount currently equals total amount (can be enhanced later for commission calculations)
- The API integrates with existing working shift and order data
