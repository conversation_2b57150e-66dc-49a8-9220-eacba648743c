package service

import (
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/go-redis/redis/v8"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/utils"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/utils/bill"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/utils/excel"
	"gorm.io/gorm"
)

// WorkingShiftService handles business logic for working shifts
type WorkingShiftService struct {
	DB          *gorm.DB
	RedisClient *redis.Client
}

// NewWorkingShiftService creates a new working shift service
func NewWorkingShiftService(db *gorm.DB, redisClient *redis.Client) *WorkingShiftService {
	return &WorkingShiftService{
		DB:          db,
		RedisClient: redisClient,
	}
}

// GetAllHubsWorkingShift gets the latest working shift for all hubs the user has access to
func (s *WorkingShiftService) GetAllHubsWorkingShift(userID string) ([]map[string]interface{}, error) {
	// Get hubs the user has access to
	var hubStaffs []models.HubStaff
	if err := s.DB.Where("user_id = ?", userID).Find(&hubStaffs).Error; err != nil {
		return nil, err
	}

	hubIDs := make([]string, len(hubStaffs))
	for i, staff := range hubStaffs {
		hubIDs[i] = staff.HubID
	}

	// Get hub details
	var hubs []models.Hub
	if err := s.DB.Where("id IN ?", hubIDs).Find(&hubs).Error; err != nil {
		return nil, err
	}

	result := make([]map[string]interface{}, len(hubs))
	for i, hub := range hubs {
		// Get the latest working shift for this hub
		var workingShift models.WorkingShift
		if err := s.DB.Where("hub_id = ?", hub.ID).Order("created_at DESC").First(&workingShift).Error; err != nil {
			if !errors.Is(err, gorm.ErrRecordNotFound) {
				return nil, err
			}
			// No working shift found, continue with nil working_shift
			result[i] = map[string]interface{}{
				"id":            hub.ID,
				"name":          hub.Name,
				"address":       hub.Address,
				"working_shift": nil,
			}
			continue
		}

		// Get user information
		var openByUser, closedByUser models.User
		if err := s.DB.Select("id, name, email").Where("id = ?", workingShift.OpenByUserID).First(&openByUser).Error; err != nil {
			if !errors.Is(err, gorm.ErrRecordNotFound) {
				return nil, err
			}
		}

		if workingShift.ClosedByUserID != "" {
			if err := s.DB.Select("id, name, email").Where("id = ?", workingShift.ClosedByUserID).First(&closedByUser).Error; err != nil {
				if !errors.Is(err, gorm.ErrRecordNotFound) {
					return nil, err
				}
			}
		}

		// Update working shift with order data
		if err := s.AssignLocalOrderToShift(&workingShift); err != nil {
			return nil, err
		}

		// Create hub result with working shift
		result[i] = map[string]interface{}{
			"id":      hub.ID,
			"name":    hub.Name,
			"address": hub.Address,
			"working_shift": map[string]interface{}{
				"id":                workingShift.ID,
				"hub_id":            workingShift.HubID,
				"open_by_user_id":   workingShift.OpenByUserID,
				"closed_by_user_id": workingShift.ClosedByUserID,
				"start_time":        workingShift.StartTime,
				"end_time":          workingShift.EndTime,
				"initial_amount":    workingShift.InitialAmount,
				"income":            workingShift.Income,
				"outcome":           workingShift.Outcome,
				"total_amount":      workingShift.TotalAmount,
				"actual_amount":     workingShift.ActualAmount,
				"status":            workingShift.Status,
				"note":              workingShift.Note,
				"report":            workingShift.Report.Data,
				"report_url":        workingShift.ReportURL,
				"created_at":        workingShift.CreatedAt,
				"updated_at":        workingShift.UpdatedAt,
				"open_by_user": map[string]interface{}{
					"id":    openByUser.ID,
					"name":  openByUser.Name,
					"email": openByUser.Email,
				},
				"closed_by_user": map[string]interface{}{
					"id":    closedByUser.ID,
					"name":  closedByUser.Name,
					"email": closedByUser.Email,
				},
			},
		}
	}

	return result, nil
}

// GetLastWorkingShift gets the latest working shift for a specific hub
func (s *WorkingShiftService) GetLastWorkingShift(hubID string) (*models.WorkingShiftWithUserInfo, error) {
	// Check if hub exists
	var hub models.Hub
	if err := s.DB.Where("id = ?", hubID).First(&hub).Error; err != nil {
		return nil, err
	}

	// Get the latest working shift
	var workingShift models.WorkingShift
	if err := s.DB.Where("hub_id = ?", hubID).Order("created_at DESC").First(&workingShift).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil // No working shift found
		}
		return nil, err
	}

	// Update working shift with order data
	if err := s.AssignLocalOrderToShift(&workingShift); err != nil {
		return nil, err
	}

	// Get user information
	var openByUser, closedByUser models.User
	if err := s.DB.Select("id, name, email").Where("id = ?", workingShift.OpenByUserID).First(&openByUser).Error; err != nil {
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, err
		}
	}

	if workingShift.ClosedByUserID != "" {
		if err := s.DB.Select("id, name, email").Where("id = ?", workingShift.ClosedByUserID).First(&closedByUser).Error; err != nil {
			if !errors.Is(err, gorm.ErrRecordNotFound) {
				return nil, err
			}
		}
	}

	// Create result with user info
	result := &models.WorkingShiftWithUserInfo{
		WorkingShift: workingShift,
		OpenByUser: models.UserInfo{
			ID:    openByUser.ID,
			Name:  openByUser.Name,
			Email: openByUser.Email,
		},
	}

	if workingShift.ClosedByUserID != "" {
		result.ClosedByUser = models.UserInfo{
			ID:    closedByUser.ID,
			Name:  closedByUser.Name,
			Email: closedByUser.Email,
		}
	}

	return result, nil
}

// GetWorkingShifts gets a paginated list of working shifts for a specific hub
func (s *WorkingShiftService) GetWorkingShifts(hubID string, page, limit int, status string) (*models.PaginationResponse, error) {
	// Check if hub exists
	var hub models.Hub
	if err := s.DB.Where("id = ?", hubID).First(&hub).Error; err != nil {
		return nil, err
	}

	// Build query
	query := s.DB.Where("hub_id = ?", hubID)
	if status != "" {
		query = query.Where("status = ?", status)
	}

	// Count total records
	var total int64
	if err := query.Model(&models.WorkingShift{}).Count(&total).Error; err != nil {
		return nil, err
	}

	// Get paginated records
	var shifts []models.WorkingShift
	if err := query.Order("start_time DESC").
		Limit(limit).
		Offset((page - 1) * limit).
		Find(&shifts).Error; err != nil {
		return nil, err
	}

	// Process each shift
	for i := range shifts {
		// Update working shift with order data
		if err := s.AssignLocalOrderToShift(&shifts[i]); err != nil {
			return nil, err
		}

		// Get user information
		var openByUser, closedByUser models.User
		if err := s.DB.Select("id, name, email").Where("id = ?", shifts[i].OpenByUserID).First(&openByUser).Error; err == nil {
			shifts[i].OpenByUser = &openByUser
		}

		if shifts[i].ClosedByUserID != "" {
			if err := s.DB.Select("id, name, email").Where("id = ?", shifts[i].ClosedByUserID).First(&closedByUser).Error; err == nil {
				shifts[i].ClosedByUser = &closedByUser
			}
		}
	}

	// Calculate total pages
	totalPages := (int(total) + limit - 1) / limit
	if totalPages < 1 {
		totalPages = 1
	}

	return &models.PaginationResponse{
		Success:    true,
		Data:       shifts,
		TotalPages: totalPages,
		Page:       page,
		PageSize:   limit,
		Total:      total,
		HasNext:    page < totalPages,
		HasPrev:    page > 1,
	}, nil
}

// CreateWorkingShift creates a new working shift
func (s *WorkingShiftService) CreateWorkingShift(hubID string, userID string, initialAmount float64, note string) (*models.WorkingShiftWithUserInfo, error) {
	// Check if hub exists
	var hub models.Hub
	if err := s.DB.Where("id = ?", hubID).First(&hub).Error; err != nil {
		return nil, err
	}

	// Create new working shift
	workingShift := models.WorkingShift{
		HubID:         hubID,
		OpenByUserID:  userID,
		StartTime:     time.Now(),
		InitialAmount: initialAmount,
		Note:          note,
		Status:        models.WorkingShiftStatusOpen,
		Income:        models.JSONArray[models.WorkingShiftIncome]{},
		Outcome:       models.JSONArray[models.WorkingShiftOutcome]{},
	}

	// Save to database
	if err := s.DB.Create(&workingShift).Error; err != nil {
		return nil, err
	}

	// Get user information
	var openByUser models.User
	if err := s.DB.Select("id, name, email").Where("id = ?", userID).First(&openByUser).Error; err != nil {
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, err
		}
	}

	// Create result with user info
	result := &models.WorkingShiftWithUserInfo{
		WorkingShift: workingShift,
		OpenByUser: models.UserInfo{
			ID:    openByUser.ID,
			Name:  openByUser.Name,
			Email: openByUser.Email,
		},
	}

	return result, nil
}

// UpdateWorkingShift updates an existing working shift
func (s *WorkingShiftService) UpdateWorkingShift(hubID, shiftID, userID string, updates map[string]interface{}) (*models.WorkingShiftWithUserInfo, error) {
	// Check if hub exists
	var hub models.Hub
	if err := s.DB.Where("id = ?", hubID).First(&hub).Error; err != nil {
		return nil, err
	}

	// Get the working shift
	var workingShift models.WorkingShift
	if err := s.DB.Where("id = ? AND hub_id = ?", shiftID, hubID).First(&workingShift).Error; err != nil {
		return nil, err
	}

	// Prepare updates
	updateData := make(map[string]interface{})

	// Handle income and outcome updates
	if income, ok := updates["income"]; ok {
		updateData["income"] = income
	}

	if outcome, ok := updates["outcome"]; ok {
		updateData["outcome"] = outcome
	}

	// Calculate total amount if income or outcome was updated
	if _, incomeOk := updates["income"]; incomeOk || updates["outcome"] != nil {
		var totalAmount float64 = workingShift.InitialAmount

		// Add income
		for _, inc := range workingShift.Income {
			totalAmount += inc.Amount
		}

		// Subtract outcome
		for _, out := range workingShift.Outcome {
			totalAmount -= out.Amount
		}

		updateData["total_amount"] = totalAmount
	}

	// Handle other fields
	if status, ok := updates["status"].(string); ok {
		updateData["status"] = status
		if status == string(models.WorkingShiftStatusClosed) {
			updateData["closed_by_user_id"] = userID
			updateData["end_time"] = time.Now()
		}
	}

	if actualAmount, ok := updates["actual_amount"].(float64); ok {
		updateData["actual_amount"] = actualAmount
	}

	if note, ok := updates["note"].(string); ok {
		updateData["note"] = note
	}

	// Update the working shift
	if err := s.DB.Model(&workingShift).Updates(updateData).Error; err != nil {
		return nil, err
	}

	// Reload the working shift
	if err := s.DB.Where("id = ?", shiftID).First(&workingShift).Error; err != nil {
		return nil, err
	}

	// Update working shift with order data
	if err := s.AssignLocalOrderToShift(&workingShift); err != nil {
		return nil, err
	}

	// Get user information
	var openByUser, closedByUser models.User
	if err := s.DB.Select("id, name, email").Where("id = ?", workingShift.OpenByUserID).First(&openByUser).Error; err != nil {
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, err
		}
	}

	if workingShift.ClosedByUserID != "" {
		if err := s.DB.Select("id, name, email").Where("id = ?", workingShift.ClosedByUserID).First(&closedByUser).Error; err != nil {
			if !errors.Is(err, gorm.ErrRecordNotFound) {
				return nil, err
			}
		}
	}

	// Create result with user info
	result := &models.WorkingShiftWithUserInfo{
		WorkingShift: workingShift,
		OpenByUser: models.UserInfo{
			ID:    openByUser.ID,
			Name:  openByUser.Name,
			Email: openByUser.Email,
		},
	}

	if workingShift.ClosedByUserID != "" {
		result.ClosedByUser = models.UserInfo{
			ID:    closedByUser.ID,
			Name:  closedByUser.Name,
			Email: closedByUser.Email,
		}
	}

	// If the shift is closed, trigger print
	if workingShift.Status == models.WorkingShiftStatusClosed {
		go s.PrintWorkingShift(hubID, shiftID)
	}

	return result, nil
}

// DeleteWorkingShift deletes a working shift
func (s *WorkingShiftService) DeleteWorkingShift(hubID, shiftID string) error {
	// Check if hub exists
	var hub models.Hub
	if err := s.DB.Where("id = ?", hubID).First(&hub).Error; err != nil {
		return err
	}

	// Delete the working shift
	result := s.DB.Where("id = ? AND hub_id = ?", shiftID, hubID).Delete(&models.WorkingShift{})
	if result.Error != nil {
		return result.Error
	}

	if result.RowsAffected == 0 {
		return errors.New("working shift not found")
	}

	return nil
}

// GetWorkingShiftsReport gets a report of working shifts for a date range
func (s *WorkingShiftService) GetWorkingShiftsReport(hubIDs []string, startTime, endTime time.Time) (string, error) {
	// Find working shifts in the date range
	var workingShifts []models.WorkingShift
	query := s.DB.Where("created_at BETWEEN ? AND ?", startTime, endTime)

	if len(hubIDs) > 0 {
		query = query.Where("hub_id IN ?", hubIDs)
	}

	if err := query.Find(&workingShifts).Error; err != nil {
		return "", err
	}

	// Process each shift to get user and hub information
	type ReportRow struct {
		HubName         string    `json:"hub_name"`
		HubAddress      string    `json:"hub_address"`
		OpenByUser      string    `json:"open_by_user"`
		ClosedByUser    string    `json:"closed_by_user"`
		StartTime       time.Time `json:"start_time"`
		EndTime         time.Time `json:"end_time"`
		InitialAmount   float64   `json:"initial_amount"`
		ActualAmount    float64   `json:"actual_amount"`
		CashIncome      float64   `json:"cash_income"`
		CashOutcome     float64   `json:"cash_outcome"`
		TransferIncome  float64   `json:"transfer_income"`
		TransferOutcome float64   `json:"transfer_outcome"`
		Status          string    `json:"status"`
	}

	var result []ReportRow
	for _, shift := range workingShifts {
		// Get hub information
		var hub models.Hub
		if err := s.DB.Select("name, address").Where("id = ?", shift.HubID).First(&hub).Error; err != nil {
			continue
		}

		// Get user information
		var openByUser, closedByUser models.User
		if err := s.DB.Select("name").Where("id = ?", shift.OpenByUserID).First(&openByUser).Error; err != nil {
			openByUser.Name = ""
		}

		if shift.ClosedByUserID != "" {
			if err := s.DB.Select("name").Where("id = ?", shift.ClosedByUserID).First(&closedByUser).Error; err != nil {
				closedByUser.Name = ""
			}
		}

		// Calculate income and outcome by payment method
		var cashIncome, cashOutcome, transferIncome, transferOutcome float64

		for _, income := range shift.Income {
			if income.PaymentMethod == "cash" {
				cashIncome += income.Amount
			} else if income.PaymentMethod == "transfer" {
				transferIncome += income.Amount
			}
		}

		for _, outcome := range shift.Outcome {
			if outcome.PaymentMethod == "cash" {
				cashOutcome += outcome.Amount
			} else if outcome.PaymentMethod == "transfer" {
				transferOutcome += outcome.Amount
			}
		}

		// Add row to result
		result = append(result, ReportRow{
			HubName:         hub.Name,
			HubAddress:      hub.Address,
			OpenByUser:      openByUser.Name,
			ClosedByUser:    closedByUser.Name,
			StartTime:       shift.StartTime,
			EndTime:         shift.EndTime,
			InitialAmount:   shift.InitialAmount,
			ActualAmount:    shift.ActualAmount,
			CashIncome:      cashIncome,
			CashOutcome:     cashOutcome,
			TransferIncome:  transferIncome,
			TransferOutcome: transferOutcome,
			Status:          string(shift.Status),
		})
	}

	// Prepare data for Excel
	headers := []string{
		"Tên hub",
		"Địa chỉ",
		"Người mở ca",
		"Người đóng ca",
		"Thời gian bắt đầu",
		"Thời gian kết thúc",
		"Số tiền ban đầu",
		"Số tiền thực tế",
		"Thu tiền mặt",
		"Chi tiền mặt",
		"Thu chuyển khoản",
		"Chi chuyển khoản",
		"Trạng thái",
	}

	data := make([][]interface{}, len(result))
	for i, row := range result {
		data[i] = []interface{}{
			row.HubName,
			row.HubAddress,
			row.OpenByUser,
			row.ClosedByUser,
			excel.FormatTime(row.StartTime),
			excel.FormatTime(row.EndTime),
			excel.FormatCurrency(row.InitialAmount),
			excel.FormatCurrency(row.ActualAmount),
			excel.FormatCurrency(row.CashIncome),
			excel.FormatCurrency(row.CashOutcome),
			excel.FormatCurrency(row.TransferIncome),
			excel.FormatCurrency(row.TransferOutcome),
			row.Status,
		}
	}

	// Generate Excel file
	buffer, err := excel.GenerateExcel("Báo cáo ca", headers, data)
	if err != nil {
		return "", fmt.Errorf("error generating Excel file: %v", err)
	}

	// Generate a unique filename
	filename := fmt.Sprintf("Working_Shifts/Bao_Cao_Ca_%s.xlsx", time.Now().Format("060102150405"))

	// Upload to storage
	fileURL, err := excel.UploadExcel("nexpos-files", filename, buffer)
	if err != nil {
		return "", fmt.Errorf("error uploading Excel file: %v", err)
	}

	return fileURL, nil
}

// PrintWorkingShift generates and sends print data for a working shift
func (s *WorkingShiftService) PrintWorkingShift(hubID, shiftID string) error {
	// Get the working shift
	var workingShift models.WorkingShift
	if err := s.DB.Where("id = ? AND hub_id = ?", shiftID, hubID).First(&workingShift).Error; err != nil {
		return err
	}

	// Get hub information
	var hub models.Hub
	if err := s.DB.Where("id = ?", hubID).First(&hub).Error; err != nil {
		return err
	}

	// Update working shift with order data
	if err := s.AssignLocalOrderToShift(&workingShift); err != nil {
		return err
	}

	// Generate HTML for the shift report
	reportHTML, err := bill.RenderTemplate("templates/bill_for_shift.html", workingShift.Report.Data)
	if err != nil {
		return fmt.Errorf("error rendering shift report template: %v", err)
	}

	// Generate HTML for the shift payment report
	paymentHTML, err := bill.RenderTemplate("templates/bill_for_shift_with_payment.html", workingShift.Report.Data)
	if err != nil {
		return fmt.Errorf("error rendering shift payment template: %v", err)
	}

	// Convert HTML to images
	reportURL, err := bill.HTMLToImage(workingShift.ID, reportHTML)
	if err != nil {
		return fmt.Errorf("error converting report HTML to image: %v", err)
	}

	paymentURL, err := bill.HTMLToImage(workingShift.ID+"_payment", paymentHTML)
	if err != nil {
		return fmt.Errorf("error converting payment HTML to image: %v", err)
	}

	// Update the working shift with the report URL
	if err := s.DB.Model(&models.WorkingShift{}).Where("id = ?", workingShift.ID).
		Update("report_url", reportURL).Error; err != nil {
		return fmt.Errorf("error updating working shift report URL: %v", err)
	}

	// Send to print service
	if s.RedisClient != nil {
		urls := []string{reportURL, paymentURL}

		// Use the SDK's event utility to send the message
		err := utils.SendPrintBillMessage(s.RedisClient, hub.Code, "bill_for_shift", urls)
		if err != nil {
			return fmt.Errorf("error sending print message: %v", err)
		}
	}

	return nil
}

// AssignLocalOrderToShift updates a working shift with order data
func (s *WorkingShiftService) AssignLocalOrderToShift(workingShift *models.WorkingShift) error {
	// Get hub information
	var hub models.Hub
	if err := s.DB.Where("id = ?", workingShift.HubID).First(&hub).Error; err != nil {
		return err
	}

	// Get closed by user information
	var closedByUser models.User
	if workingShift.ClosedByUserID != "" {
		if err := s.DB.Select("name, email").Where("id = ?", workingShift.ClosedByUserID).First(&closedByUser).Error; err != nil {
			// Continue even if user not found
		}
	}

	// Build order filter
	orderFilter := map[string]interface{}{
		"hub_id": workingShift.HubID,
		"status": "FINISH",
	}

	query := s.DB.Where(orderFilter)

	// Add time constraints
	if !workingShift.StartTime.IsZero() {
		query = query.Where(gorm.Expr("created_at >= ?", workingShift.StartTime))
	}

	if workingShift.Status == models.WorkingShiftStatusClosed && !workingShift.EndTime.IsZero() {
		if _, ok := orderFilter["created_at"]; ok {
			query = query.Where(gorm.Expr("created_at BETWEEN ? AND ?", workingShift.StartTime, workingShift.EndTime))
		} else {
			query = query.Where(gorm.Expr("created_at <= ?", workingShift.EndTime))
		}
	}

	// Find orders
	var orders []models.Order
	if err := query.Find(&orders).Error; err != nil {
		return err
	}

	// Initialize report
	report := models.WorkingShiftReport{
		HubName:            hub.Name,
		CreatedAt:          workingShift.CreatedAt.Format("02/01/2006 15:04"),
		CloseBy:            fmt.Sprintf("%s - %s", closedByUser.Name, closedByUser.Email),
		TotalOrder:         len(orders),
		TotalGrossReceived: 0,
		TotalNetReceived:   0,
		Merchants:          make(map[string]models.MerchantReport),
		PaymentMethods:     make(map[string]models.PaymentMethodInfo),
	}

	// Process orders
	var allIncome []models.WorkingShiftIncome

	for _, order := range orders {
		// Extract data from order
		var dataMapping map[string]interface{}
		if order.Data.Data != nil {
			dataMapping = order.Data.Data
		}

		// Process payments
		if payments, ok := getNestedValue(dataMapping, "payments").([]interface{}); ok {
			for _, p := range payments {
				payment, ok := p.(map[string]interface{})
				if !ok {
					continue
				}

				method, _ := payment["method"].(string)
				total, _ := payment["total"].(float64)

				if method != "" {
					// Add to income
					allIncome = append(allIncome, models.WorkingShiftIncome{
						OrderID:       order.OrderID,
						PaymentMethod: method,
						Amount:        total,
						Note:          "",
					})

					// Update payment methods in report
					if _, exists := report.PaymentMethods[method]; !exists {
						report.PaymentMethods[method] = models.PaymentMethodInfo{
							Total: 0,
						}
					}

					info := report.PaymentMethods[method]
					info.Total += total
					report.PaymentMethods[method] = info
				}
			}
		} else if order.Source != "local" && order.Source != "he" {
			// For non-local orders, use the source as payment method
			totalForBiz, _ := getNestedValue(dataMapping, "total_for_biz").(float64)

			allIncome = append(allIncome, models.WorkingShiftIncome{
				OrderID:       order.OrderID,
				PaymentMethod: order.Source,
				Amount:        totalForBiz,
				Note:          "",
			})

			// Update payment methods in report
			if _, exists := report.PaymentMethods[order.Source]; !exists {
				report.PaymentMethods[order.Source] = models.PaymentMethodInfo{
					Total: 0,
				}
			}

			info := report.PaymentMethods[order.Source]
			info.Total += totalForBiz
			report.PaymentMethods[order.Source] = info
		}

		// Update merchant data in report
		if _, exists := report.Merchants[order.Source]; !exists {
			report.Merchants[order.Source] = models.MerchantReport{
				Orders:             []map[string]string{},
				GrossReceived:      []float64{},
				NetReceived:        []float64{},
				TotalOrders:        0,
				TotalGrossReceived: 0,
				TotalNetReceived:   0,
			}
		}

		// Get financial data
		var originalPrice, netReceived float64
		if financeData, ok := getNestedValue(dataMapping, "finance_data").(map[string]interface{}); ok {
			originalPrice, _ = financeData["original_price"].(float64)
			netReceived, _ = financeData["net_received"].(float64)
		}

		// Update merchant report
		merchant := report.Merchants[order.Source]
		merchant.Orders = append(merchant.Orders, map[string]string{
			"id":   order.OrderID,
			"time": order.CreatedAt.Format("15:04"),
		})
		merchant.GrossReceived = append(merchant.GrossReceived, originalPrice)
		merchant.NetReceived = append(merchant.NetReceived, netReceived)
		merchant.TotalOrders++
		merchant.TotalGrossReceived += originalPrice
		merchant.TotalNetReceived += netReceived
		report.Merchants[order.Source] = merchant

		// Update report totals
		report.TotalGrossReceived += originalPrice
		report.TotalNetReceived += netReceived
	}

	// Group income by payment method
	incomeByMethod := make(map[string][]models.WorkingShiftIncome)
	for _, income := range allIncome {
		incomeByMethod[income.PaymentMethod] = append(incomeByMethod[income.PaymentMethod], income)
	}

	// Create aggregated income
	var aggregatedIncome []models.WorkingShiftIncome
	for method, incomes := range incomeByMethod {
		var totalAmount float64
		for _, income := range incomes {
			totalAmount += income.Amount
		}

		aggregatedIncome = append(aggregatedIncome, models.WorkingShiftIncome{
			PaymentMethod: method,
			Amount:        totalAmount,
			Note:          "",
		})
	}

	// Calculate total amount
	totalAmount := workingShift.InitialAmount
	for _, income := range aggregatedIncome {
		totalAmount += income.Amount
	}
	for _, outcome := range workingShift.Outcome {
		totalAmount -= outcome.Amount
	}

	// Update the working shift
	workingShift.Report = models.JSONField[models.WorkingShiftReport]{Data: report}
	workingShift.Income = aggregatedIncome
	workingShift.TotalAmount = totalAmount

	// Save to database
	updates := map[string]interface{}{
		"report":       workingShift.Report,
		"income":       workingShift.Income,
		"total_amount": workingShift.TotalAmount,
	}

	if err := s.DB.Model(&models.WorkingShift{}).Where("id = ?", workingShift.ID).Updates(updates).Error; err != nil {
		return err
	}

	return nil
}

// getNestedValue safely extracts a nested value from a map
func getNestedValue(data map[string]interface{}, key string) interface{} {
	if data == nil {
		return nil
	}

	parts := strings.Split(key, ".")
	current := data

	for i, part := range parts {
		if i == len(parts)-1 {
			return current[part]
		}

		if next, ok := current[part].(map[string]interface{}); ok {
			current = next
		} else {
			return nil
		}
	}

	return nil
}

// WorkingShiftOrderGroup represents grouped order data for working shift history
type WorkingShiftOrderGroup struct {
	OrderID  string  `json:"order_id"`
	Quantity int     `json:"quantity"`
	Channel  string  `json:"channel"`
	Amount   float64 `json:"amount"`
}

// WorkingShiftPaymentGroup represents grouped payment method data for working shift history
type WorkingShiftPaymentGroup struct {
	PaymentMethod string  `json:"payment_method"`
	TotalAmount   float64 `json:"total_amount"`
	NetAmount     float64 `json:"net_amount"`
}

// WorkingShiftHistoryItem represents a single working shift history item
type WorkingShiftHistoryItem struct {
	StartDate            string                     `json:"start_date"`
	EndDate              string                     `json:"end_date"`
	StartDateTime        string                     `json:"start_date_time"`
	EndDateTime          string                     `json:"end_date_time"`
	EmployeeID           string                     `json:"employee_id"`
	EmployeeName         string                     `json:"employee_name"`
	InitialCash          float64                    `json:"initial_cash"`
	TotalCash            float64                    `json:"total_cash"`
	TotalShift           float64                    `json:"total_shift"`
	GroupByOrder         []WorkingShiftOrderGroup   `json:"group_by_order"`
	GroupByPaymentMethod []WorkingShiftPaymentGroup `json:"group_by_payment_method"`
}

// WorkingShiftHistoryQueryParams represents query parameters for working shift history
type WorkingShiftHistoryQueryParams struct {
	Page      int    `form:"page"`
	Limit     int    `form:"limit"`
	StartDate string `form:"start_date"`
	EndDate   string `form:"end_date"`
	Status    string `form:"status"`
}

// GetWorkingShiftHistory gets paginated working shift history with enhanced data
func (s *WorkingShiftService) GetWorkingShiftHistory(hubID string, params WorkingShiftHistoryQueryParams) (*models.PaginationResponse, error) {
	// Check if hub exists
	var hub models.Hub
	if err := s.DB.Where("id = ?", hubID).First(&hub).Error; err != nil {
		return nil, err
	}

	// Build query
	query := s.DB.Where("hub_id = ?", hubID)

	// Add date filters
	if params.StartDate != "" {
		startTime, err := time.Parse("2006-01-02", params.StartDate)
		if err != nil {
			return nil, fmt.Errorf("invalid start_date format, expected DD/MM/YYYY: %v", err)
		}
		query = query.Where("start_time >= ?", startTime)
	}

	if params.EndDate != "" {
		endTime, err := time.Parse("2006-01-02", params.EndDate)
		if err != nil {
			return nil, fmt.Errorf("invalid end_date format, expected DD/MM/YYYY: %v", err)
		}
		// Add 24 hours to include the entire end date
		endTime = endTime.Add(24 * time.Hour)
		query = query.Where("start_time < ?", endTime)
	}

	// Add status filter
	if params.Status != "" {
		query = query.Where("status = ?", params.Status)
	}

	// Count total records
	var total int64
	if err := query.Model(&models.WorkingShift{}).Count(&total).Error; err != nil {
		return nil, err
	}

	// Get paginated records
	var shifts []models.WorkingShift
	if err := query.Order("start_time DESC").
		Limit(params.Limit).
		Offset((params.Page - 1) * params.Limit).
		Find(&shifts).Error; err != nil {
		return nil, err
	}

	// Process each shift to create history items
	var historyItems []WorkingShiftHistoryItem
	for _, shift := range shifts {
		// Get user information
		var openByUser models.User
		if err := s.DB.Select("id, name, email").Where("id = ?", shift.OpenByUserID).First(&openByUser).Error; err != nil {
			if !errors.Is(err, gorm.ErrRecordNotFound) {
				return nil, err
			}
		}

		// Update working shift with order data
		if err := s.AssignLocalOrderToShift(&shift); err != nil {
			return nil, err
		}

		// Create history item
		historyItem := s.buildWorkingShiftHistoryItem(&shift, &openByUser)
		historyItems = append(historyItems, historyItem)
	}

	// Calculate total pages
	totalPages := (int(total) + params.Limit - 1) / params.Limit
	if totalPages < 1 {
		totalPages = 1
	}

	return &models.PaginationResponse{
		Success:    true,
		Data:       historyItems,
		TotalPages: totalPages,
		Page:       params.Page,
		PageSize:   params.Limit,
		Total:      total,
		HasNext:    params.Page < totalPages,
		HasPrev:    params.Page > 1,
	}, nil
}

// buildWorkingShiftHistoryItem builds a history item from working shift data
func (s *WorkingShiftService) buildWorkingShiftHistoryItem(shift *models.WorkingShift, openByUser *models.User) WorkingShiftHistoryItem {
	// Format dates
	startDate := shift.StartTime.Format("02/01/2006")
	endDate := startDate
	if !shift.EndTime.IsZero() {
		endDate = shift.EndTime.Format("02/01/2006")
	}

	startDateTime := shift.StartTime.Format("15:04")
	endDateTime := "00:00"
	if !shift.EndTime.IsZero() {
		endDateTime = shift.EndTime.Format("15:04")
	}

	// Calculate totals
	totalCash := shift.InitialAmount
	totalShift := shift.TotalAmount

	// Group orders by order_id
	orderGroups := s.buildOrderGroups(shift)

	// Group payments by payment method
	paymentGroups := s.buildPaymentGroups(shift)

	// Calculate total cash from income
	for _, income := range shift.Income {
		if income.PaymentMethod == "CASH" {
			totalCash += income.Amount
		}
	}

	return WorkingShiftHistoryItem{
		StartDate:            startDate,
		EndDate:              endDate,
		StartDateTime:        startDateTime,
		EndDateTime:          endDateTime,
		EmployeeID:           shift.OpenByUserID,
		EmployeeName:         openByUser.Name,
		InitialCash:          shift.InitialAmount,
		TotalCash:            totalCash,
		TotalShift:           totalShift,
		GroupByOrder:         orderGroups,
		GroupByPaymentMethod: paymentGroups,
	}
}

// buildOrderGroups builds order groups from working shift income data
func (s *WorkingShiftService) buildOrderGroups(shift *models.WorkingShift) []WorkingShiftOrderGroup {
	orderMap := make(map[string]*WorkingShiftOrderGroup)

	// Process income to group by order_id
	for _, income := range shift.Income {
		if income.OrderID != "" {
			if existing, exists := orderMap[income.OrderID]; exists {
				existing.Amount += income.Amount
				existing.Quantity++
			} else {
				// Get order details to determine channel
				channel := s.getOrderChannel(income.OrderID)
				orderMap[income.OrderID] = &WorkingShiftOrderGroup{
					OrderID:  income.OrderID,
					Quantity: 1,
					Channel:  channel,
					Amount:   income.Amount,
				}
			}
		}
	}

	// Convert map to slice
	orderGroups := []WorkingShiftOrderGroup{}
	for _, group := range orderMap {
		orderGroups = append(orderGroups, *group)
	}

	return orderGroups
}

// buildPaymentGroups builds payment method groups from working shift data
func (s *WorkingShiftService) buildPaymentGroups(shift *models.WorkingShift) []WorkingShiftPaymentGroup {
	paymentMap := make(map[string]*WorkingShiftPaymentGroup)

	// Process income by payment method
	for _, income := range shift.Income {
		method := income.PaymentMethod
		if method == "" {
			method = "CASH"
		}

		if existing, exists := paymentMap[method]; exists {
			existing.TotalAmount += income.Amount
			existing.NetAmount += income.Amount // For now, net amount equals total amount
		} else {
			paymentMap[method] = &WorkingShiftPaymentGroup{
				PaymentMethod: method,
				TotalAmount:   income.Amount,
				NetAmount:     income.Amount, // For now, net amount equals total amount
			}
		}
	}

	// Convert map to slice
	paymentGroups := []WorkingShiftPaymentGroup{}
	for _, group := range paymentMap {
		paymentGroups = append(paymentGroups, *group)
	}

	return paymentGroups
}

// getOrderChannel gets the channel/source for an order
func (s *WorkingShiftService) getOrderChannel(orderID string) string {
	var order models.Order
	if err := s.DB.Select("source").Where("order_id = ? OR short_order_id = ?", orderID, orderID).First(&order).Error; err != nil {
		return "Unknown"
	}

	// Map source to readable channel name
	switch order.Source {
	case "grab":
		return "Grab"
	case "be":
		return "Be"
	case "shopee":
		return "Shopee"
	case "pos":
		return "POS"
	case "web":
		return "Website"
	default:
		return order.Source
	}
}
