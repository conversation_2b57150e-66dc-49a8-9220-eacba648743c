package service

import (
	"fmt"
	"math"
	"strings"
	"time"

	lmodels "github.com/nexdorvn/nexpos-backend/golang-services/brand-service/models"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"
	"github.com/spf13/cast"
	"gorm.io/gorm"
)

// ReportService handles report-related operations
type ReportService struct {
	DB *gorm.DB
}

// NewReportService creates a new ReportService
func NewReportService(db *gorm.DB) *ReportService {
	return &ReportService{
		DB: db,
	}
}

// GetReport generates a report for a specific hub within a time range
func (s *ReportService) GetReport(query lmodels.ReportQuery) (*lmodels.ReportResponse, error) {
	// Define merchants to include in the report
	merchants := []string{"all", "grab", "grab_food", "grab_mart", "shopee", "shopee_fresh", "be", "momo"}

	// Initialize response
	response := &lmodels.ReportResponse{
		MerchantSummaries: []lmodels.MerchantSummary{},
		SiteSummaries:     []lmodels.SiteSummary{},
	}

	// Build the query
	dbQuery := s.DB.Where("status = ? AND data_mapping->>'delivery_time_unix' >= ? AND data_mapping->>'delivery_time_unix' < ?",
		"FINISH", cast.ToString(query.From.Unix()), cast.ToString(query.To.Unix()))

	// Add hub filter if specified
	if query.HubID != "" && query.HubID != "all" {
		dbQuery = dbQuery.Where("hub_id = ?", query.HubID)
	}

	// Add app filter if specified
	if len(query.Apps) > 0 {
		dbQuery = dbQuery.Where("source IN (?)", query.Apps)
	}

	// Query orders
	var orders []models.Order
	if err := dbQuery.Find(&orders).Error; err != nil {
		return nil, err
	}

	// Group orders by merchant
	ordersByMerchant := make(map[string][]models.Order)
	for _, merchant := range merchants {
		if merchant == "all" {
			ordersByMerchant[merchant] = orders
		} else {
			var merchantOrders []models.Order
			for _, order := range orders {
				if order.Source == merchant {
					merchantOrders = append(merchantOrders, order)
				}
			}
			ordersByMerchant[merchant] = merchantOrders
		}
	}

	// Group orders by site
	ordersBySite := make(map[string][]models.Order)
	for _, order := range orders {
		if _, exists := ordersBySite[order.SiteID]; !exists {
			ordersBySite[order.SiteID] = []models.Order{}
		}
		ordersBySite[order.SiteID] = append(ordersBySite[order.SiteID], order)
	}

	// Get site information
	var siteIDs []string
	for siteID := range ordersBySite {
		siteIDs = append(siteIDs, siteID)
	}

	var sites []models.Site
	if len(siteIDs) > 0 {
		if err := s.DB.Where("id IN ?", siteIDs).Find(&sites).Error; err != nil {
			return nil, err
		}
	}

	// Create site map for quick lookup
	siteMap := make(map[string]models.Site)
	for _, site := range sites {
		siteMap[site.ID] = site
	}

	// Calculate totals for each merchant and create merchant summaries
	var totalRevenue, totalRevenueBeforePromo, totalRevenueAfterPromo float64
	var totalOrderCount int

	for _, merchant := range merchants {
		merchantOrders := ordersByMerchant[merchant]
		if merchant != "all" {
			totalOrderCount += len(merchantOrders)
		}

		var totalOriginalPrice, totalNetReceived, totalGrossReceived, totalCoFund, totalCommission float64

		for _, order := range merchantOrders {
			if order.DataMapping.Data.FinanceData.OriginalPrice > 0 {
				totalOriginalPrice += order.DataMapping.Data.FinanceData.OriginalPrice
			}
			if order.DataMapping.Data.FinanceData.NetReceived > 0 {
				totalNetReceived += order.DataMapping.Data.FinanceData.NetReceived
			}
			if order.DataMapping.Data.FinanceData.GrossReceived > 0 {
				totalGrossReceived += order.DataMapping.Data.FinanceData.GrossReceived
			}
			// Safely access finance data fields
			if order.DataMapping.Data.FinanceData.CoFundPromotionPrice > 0 {
				totalCoFund += order.DataMapping.Data.FinanceData.CoFundPromotionPrice
			}
			totalCommission += order.DataMapping.Data.FinanceData.Commission
		}

		if merchant != "all" {
			totalRevenue += totalNetReceived
			totalRevenueBeforePromo += totalOriginalPrice
			totalRevenueAfterPromo += totalGrossReceived
		}

		// Add merchant summary
		if merchant != "all" && len(merchantOrders) > 0 {
			response.MerchantSummaries = append(response.MerchantSummaries, lmodels.MerchantSummary{
				Name:    merchant,
				Count:   len(merchantOrders),
				Revenue: totalNetReceived,
			})
		}
	}

	// Calculate site summaries
	for siteID, siteOrders := range ordersBySite {
		site, exists := siteMap[siteID]
		if !exists {
			continue
		}

		var siteRevenue, siteRevenueBeforePromo, siteRevenueAfterPromo float64
		for _, order := range siteOrders {
			if order.DataMapping.Data.FinanceData.OriginalPrice > 0 {
				siteRevenueBeforePromo += order.DataMapping.Data.FinanceData.OriginalPrice
			}
			if order.DataMapping.Data.FinanceData.NetReceived > 0 {
				siteRevenue += order.DataMapping.Data.FinanceData.NetReceived
			}
			if order.DataMapping.Data.FinanceData.GrossReceived > 0 {
				siteRevenueAfterPromo += order.DataMapping.Data.FinanceData.GrossReceived
			}
		}

		response.SiteSummaries = append(response.SiteSummaries, lmodels.SiteSummary{
			Name:               site.Name,
			OrderCount:         len(siteOrders),
			RevenueBeforePromo: siteRevenueBeforePromo,
			RevenueAfterPromo:  siteRevenueAfterPromo,
			TotalRevenue:       siteRevenue,
		})
	}

	// Set summary data
	response.TotalRevenue = totalRevenue
	response.OrderCount = totalOrderCount
	response.RevenueBeforePromo = totalRevenueBeforePromo
	response.RevenueAfterPromo = totalRevenueAfterPromo

	return response, nil
}

// GetReportDetail generates a detailed report with paginated item-level data
func (s *ReportService) GetReportDetail(query lmodels.ReportDetailQuery) (*models.PaginationResponse, error) {
	// Build the base query for orders with brand information
	dbQuery := s.DB.Table("orders").
		Select("orders.short_order_id, orders.order_id, orders.source, orders.status, orders.site_id, orders.hub_id, sites.name as site_name, hubs.name as hub_name, brands.name as brand_name, orders.data_mapping").
		Joins("LEFT JOIN sites ON orders.site_id = sites.id").
		Joins("LEFT JOIN hubs ON orders.hub_id = hubs.id").
		Joins("LEFT JOIN brands ON sites.brand_id = brands.id").
		Where("orders.status = ? AND orders.data_mapping->>'delivery_time_unix' >= ? AND orders.data_mapping->>'delivery_time_unix' < ?",
			"FINISH", cast.ToString(query.From.Unix()), cast.ToString(query.To.Unix()))

	// Add brand filter if specified
	if len(query.BrandIDs) > 0 {
		dbQuery = dbQuery.Where("sites.brand_id IN (?)", query.BrandIDs)
	}

	// Add app filter if specified
	if len(query.Apps) > 0 {
		dbQuery = dbQuery.Where("orders.source IN (?)", query.Apps)
	}

	// Execute the query to get all orders (we'll handle pagination at item level)
	var results []struct {
		ShortOrderID string                               `json:"short_order_id"`
		OrderID      string                               `json:"order_id"`
		Source       string                               `json:"source"`
		Status       string                               `json:"status"`
		SiteID       string                               `json:"site_id"`
		HubID        string                               `json:"hub_id"`
		SiteName     string                               `json:"site_name"`
		HubName      string                               `json:"hub_name"`
		BrandName    string                               `json:"brand_name"`
		DataMapping  models.JSONField[models.DataMapping] `json:"data_mapping"`
	}

	if err := dbQuery.
		Order("orders.data_mapping->>'delivery_time_unix' DESC").
		Find(&results).Error; err != nil {
		return nil, fmt.Errorf("error fetching orders: %v", err)
	}

	// Transform results to ReportDetailItem (item-level data)
	var allDetailedItems []lmodels.ReportDetailItem

	for _, result := range results {
		dataMapping := result.DataMapping.Data

		// Parse order time and delivery time
		var orderTime, deliveryTime *time.Time
		if dataMapping.OrderTime != "" {
			if t, err := time.Parse(time.RFC3339, dataMapping.OrderTime); err == nil {
				orderTime = &t
			} else if t, err := time.Parse("2006-01-02T15:04:05Z", dataMapping.OrderTime); err == nil {
				orderTime = &t
			}
		}
		if dataMapping.DeliveryTime != "" {
			if t, err := time.Parse(time.RFC3339, dataMapping.DeliveryTime); err == nil {
				deliveryTime = &t
			} else if t, err := time.Parse("2006-01-02T15:04:05Z", dataMapping.DeliveryTime); err == nil {
				deliveryTime = &t
			}
		}

		// Process each dish in the order
		for dishIndex, dish := range dataMapping.Dishes {
			// Calculate item-level pricing
			itemOriginalPrice := dish.Price
			itemAppliedPrice := dish.Price - dish.Discount
			itemTotalAmount := float64(dish.Quantity) * itemAppliedPrice
			itemPromotion := dish.Discount * float64(dish.Quantity)

			// For total promotion, we need to distribute order-level promotions proportionally
			orderTotalOriginal := dataMapping.Total + dataMapping.TotalDiscount
			var proportionalOrderDiscount float64
			if orderTotalOriginal > 0 {
				proportionalOrderDiscount = (itemTotalAmount / orderTotalOriginal) * dataMapping.TotalDiscount
			}
			totalItemPromotion := itemPromotion + proportionalOrderDiscount
			amountAfterPromotion := itemTotalAmount - proportionalOrderDiscount

			detailedItem := lmodels.ReportDetailItem{
				OrderID:        result.OrderID,
				ShortOrderID:   result.ShortOrderID,
				Source:         result.Source,
				SiteName:       result.SiteName,
				HubName:        result.HubName,
				BrandName:      result.BrandName,
				ItemCode:       fmt.Sprintf("%s-%d", result.ShortOrderID, dishIndex+1), // Generate item code
				Category:       dish.CategoryName,
				ItemName:       dish.Name,
				DriverName:     dataMapping.DriverName,
				Quantity:       dish.Quantity,
				Price:          itemOriginalPrice,
				DiscountPrice:  itemAppliedPrice,
				Total:          itemTotalAmount,
				Discount:       itemPromotion,
				TotalDiscount:  totalItemPromotion,
				FinalAmount:    amountAfterPromotion,
				Status:         result.Status,
				OrderTime:      orderTime,
				DeliveryTime:   deliveryTime,
				CompletionTime: deliveryTime, // Using delivery time as completion time
			}
			allDetailedItems = append(allDetailedItems, detailedItem)
		}
	}

	// Apply pagination to the detailed items
	total := int64(len(allDetailedItems))
	offset := (query.Page - 1) * query.ItemsPerPage
	end := offset + query.ItemsPerPage

	if offset > len(allDetailedItems) {
		offset = len(allDetailedItems)
	}
	if end > len(allDetailedItems) {
		end = len(allDetailedItems)
	}

	paginatedItems := allDetailedItems[offset:end]

	// Add STT (sequential numbers) to paginated items
	for i := range paginatedItems {
		paginatedItems[i].STT = offset + i + 1
	}

	totalPages := int(math.Ceil(float64(total) / float64(query.ItemsPerPage)))

	// Return paginated response
	return &models.PaginationResponse{
		Success:    true,
		Data:       paginatedItems,
		Total:      total,
		Page:       query.Page,
		PageSize:   query.ItemsPerPage,
		TotalPages: totalPages,
		HasNext:    query.Page < totalPages,
		HasPrev:    query.Page > 1,
	}, nil
}

// GetCompletedOrdersForExcel generates detailed completed orders data for Excel export
func (s *ReportService) GetCompletedOrdersForExcel(query lmodels.ReportDetailQuery) ([]lmodels.CompletedOrderExcelItem, error) {
	// Build the base query for completed orders with all necessary joins
	dbQuery := s.DB.Table("orders").
		Select(`
			orders.short_order_id,
			orders.order_id,
			orders.source,
			orders.site_id,
			orders.hub_id,
			sites.name as site_name,
			hubs.name as hub_name,
			brands.name as brand_name,
			orders.data_mapping
		`).
		Joins("LEFT JOIN sites ON orders.site_id = sites.id").
		Joins("LEFT JOIN hubs ON orders.hub_id = hubs.id").
		Joins("LEFT JOIN brands ON sites.brand_id = brands.id").
		Where("orders.status = ? AND orders.data_mapping->>'delivery_time_unix' >= ? AND orders.data_mapping->>'delivery_time_unix' < ?",
			"FINISH", cast.ToString(query.From.Unix()), cast.ToString(query.To.Unix()))

	// Add brand filter if specified
	if len(query.BrandIDs) > 0 {
		dbQuery = dbQuery.Where("sites.brand_id IN (?)", query.BrandIDs)
	}

	// Add app filter if specified
	if len(query.Apps) > 0 {
		dbQuery = dbQuery.Where("orders.source IN (?)", query.Apps)
	}

	// Execute the query without pagination to get all completed orders
	var results []struct {
		ShortOrderID string                               `json:"short_order_id"`
		OrderID      string                               `json:"order_id"`
		Source       string                               `json:"source"`
		SiteID       string                               `json:"site_id"`
		HubID        string                               `json:"hub_id"`
		SiteName     string                               `json:"site_name"`
		HubName      string                               `json:"hub_name"`
		BrandName    string                               `json:"brand_name"`
		DataMapping  models.JSONField[models.DataMapping] `json:"data_mapping"`
	}

	if err := dbQuery.
		Order("orders.data_mapping->>'delivery_time_unix' DESC").
		Find(&results).Error; err != nil {
		return nil, fmt.Errorf("error fetching completed orders: %v", err)
	}

	// Transform results to CompletedOrderExcelItem
	var excelItems []lmodels.CompletedOrderExcelItem
	for i, result := range results {
		dataMapping := result.DataMapping.Data
		financeData := dataMapping.FinanceData

		// Extract promotion codes from coupons
		var promoCodes []string
		for _, coupon := range dataMapping.Coupons {
			if coupon.Code != "" {
				promoCodes = append(promoCodes, coupon.Code)
			}
		}
		promoCodeStr := strings.Join(promoCodes, ", ")

		// Parse order time and delivery time
		orderDate, orderTime := parseDateTime(dataMapping.OrderTime)
		deliveryDate, deliveryTime := parseDateTime(dataMapping.DeliveryTime)

		// Get display name for source
		source := result.Source

		// Calculate financial values based on the finance data structure
		revenueBeforePromo := financeData.OriginalPrice
		productDiscount := financeData.OtherPromotionPrice
		revenueAfterProductDiscount := revenueBeforePromo - productDiscount
		orderDiscount := financeData.CoFundPromotionPrice
		totalPromotion := productDiscount + orderDiscount
		revenueAfterPromo := revenueBeforePromo - totalPromotion
		additionalIncome := financeData.AdditionalIncome
		shippingFee := financeData.TotalShipment
		customerPaid := revenueAfterPromo + additionalIncome + shippingFee

		excelItem := lmodels.CompletedOrderExcelItem{
			STT:                         i + 1,
			ReferenceCode:               result.OrderID,
			OrderCode:                   result.ShortOrderID,
			Source:                      source,
			HubName:                     result.SiteName,
			BrandName:                   result.BrandName,
			SiteName:                    result.SiteName, // Using site name as store name
			RevenueBeforePromo:          revenueBeforePromo,
			ProductDiscount:             productDiscount,
			RevenueAfterProductDiscount: revenueAfterProductDiscount,
			OrderDiscount:               orderDiscount,
			TotalPromotion:              totalPromotion,
			RevenueAfterPromo:           revenueAfterPromo,
			AdditionalIncome:            additionalIncome,
			ShippingFee:                 shippingFee,
			CustomerPaid:                customerPaid,
			PromoCode:                   promoCodeStr,
			OrderDate:                   orderDate,
			OrderTime:                   orderTime,
			DeliveryDate:                deliveryDate,
			DeliveryTime:                deliveryTime,
		}
		excelItems = append(excelItems, excelItem)
	}

	return excelItems, nil
}

// parseDateTime parses a datetime string and returns separate date and time strings
func parseDateTime(dateTimeStr string) (string, string) {
	if dateTimeStr == "" {
		return "", ""
	}

	// Try to parse the datetime string
	t, err := time.Parse(time.RFC3339, dateTimeStr)
	if err != nil {
		// Try alternative format
		t, err = time.Parse("2006-01-02T15:04:05Z", dateTimeStr)
		if err != nil {
			// If parsing fails, return the original string
			return dateTimeStr, ""
		}
	}

	// Format to Vietnamese date/time format
	date := t.Format("02-01-2006")
	timeStr := t.Format("15:04:05")

	return date, timeStr
}

// parseUnixDateTime parses a unix timestamp and returns separate date and time strings
func parseUnixDateTime(unixTimestamp int64) (string, string) {
	if unixTimestamp == 0 {
		return "", ""
	}

	// Convert unix timestamp to time
	t := time.Unix(unixTimestamp, 0)

	// Format to Vietnamese date/time format
	date := t.Format("02-01-2006")
	timeStr := t.Format("15:04:05")

	return date, timeStr
}

// GetCompletedOrdersPaginated generates paginated completed orders data
func (s *ReportService) GetCompletedOrdersPaginated(query lmodels.ReportDetailQuery) (*models.PaginationResponse, error) {
	// Build the base query for completed orders with all necessary joins
	dbQuery := s.DB.Table("orders").
		Select(`
			orders.short_order_id,
			orders.order_id,
			orders.source,
			orders.site_id,
			orders.hub_id,
			sites.name as site_name,
			hubs.name as hub_name,
			brands.name as brand_name,
			orders.data_mapping
		`).
		Joins("LEFT JOIN sites ON orders.site_id = sites.id").
		Joins("LEFT JOIN hubs ON orders.hub_id = hubs.id").
		Joins("LEFT JOIN brands ON sites.brand_id = brands.id").
		Where("orders.status = ? AND orders.data_mapping->>'delivery_time_unix' >= ? AND orders.data_mapping->>'delivery_time_unix' < ?",
			"FINISH", cast.ToString(query.From.Unix()), cast.ToString(query.To.Unix()))

	// Add brand filter if specified
	if len(query.BrandIDs) > 0 {
		dbQuery = dbQuery.Where("sites.brand_id IN (?)", query.BrandIDs)
	}

	// Add app filter if specified
	if len(query.Apps) > 0 {
		dbQuery = dbQuery.Where("orders.source IN (?)", query.Apps)
	}

	// Count total records
	var total int64
	if err := dbQuery.Count(&total).Error; err != nil {
		return nil, fmt.Errorf("error counting completed orders: %v", err)
	}

	// Set default pagination values if not provided
	if query.Page == 0 {
		query.Page = 1
	}
	if query.ItemsPerPage == 0 {
		query.ItemsPerPage = 20
	}

	// Calculate pagination
	offset := (query.Page - 1) * query.ItemsPerPage
	totalPages := int(math.Ceil(float64(total) / float64(query.ItemsPerPage)))

	// Execute the query with pagination
	var results []struct {
		ShortOrderID string                               `json:"short_order_id"`
		OrderID      string                               `json:"order_id"`
		Source       string                               `json:"source"`
		SiteID       string                               `json:"site_id"`
		HubID        string                               `json:"hub_id"`
		SiteName     string                               `json:"site_name"`
		HubName      string                               `json:"hub_name"`
		BrandName    string                               `json:"brand_name"`
		DataMapping  models.JSONField[models.DataMapping] `json:"data_mapping"`
	}

	if err := dbQuery.
		Order("orders.data_mapping->>'delivery_time_unix' DESC").
		Offset(offset).
		Limit(query.ItemsPerPage).
		Find(&results).Error; err != nil {
		return nil, fmt.Errorf("error fetching completed orders: %v", err)
	}

	// Transform results to CompletedOrderExcelItem (reuse the same structure)
	var completedOrders []lmodels.CompletedOrderExcelItem
	for i, result := range results {
		dataMapping := result.DataMapping.Data
		financeData := dataMapping.FinanceData

		// Extract promotion codes from coupons
		var promoCodes []string
		for _, coupon := range dataMapping.Coupons {
			if coupon.Code != "" {
				promoCodes = append(promoCodes, coupon.Code)
			}
		}
		promoCodeStr := strings.Join(promoCodes, ", ")

		// Parse order time and delivery time
		orderDate, orderTime := parseDateTime(dataMapping.OrderTime)
		deliveryDate, deliveryTime := parseDateTime(dataMapping.DeliveryTime)

		// Get display name for source
		source := result.Source

		// Calculate financial values based on the finance data structure
		revenueBeforePromo := financeData.OriginalPrice
		productDiscount := financeData.OtherPromotionPrice
		revenueAfterProductDiscount := revenueBeforePromo - productDiscount
		orderDiscount := financeData.CoFundPromotionPrice
		totalPromotion := productDiscount + orderDiscount
		revenueAfterPromo := revenueBeforePromo - totalPromotion
		additionalIncome := financeData.AdditionalIncome
		shippingFee := financeData.TotalShipment
		customerPaid := revenueAfterPromo + additionalIncome + shippingFee

		completedOrder := lmodels.CompletedOrderExcelItem{
			STT:                         offset + i + 1,
			ReferenceCode:               result.OrderID,
			OrderCode:                   result.ShortOrderID,
			Source:                      source,
			HubName:                     result.SiteName,
			BrandName:                   result.BrandName,
			SiteName:                    result.SiteName, // Using site name as store name
			RevenueBeforePromo:          revenueBeforePromo,
			ProductDiscount:             productDiscount,
			RevenueAfterProductDiscount: revenueAfterProductDiscount,
			OrderDiscount:               orderDiscount,
			TotalPromotion:              totalPromotion,
			RevenueAfterPromo:           revenueAfterPromo,
			AdditionalIncome:            additionalIncome,
			ShippingFee:                 shippingFee,
			CustomerPaid:                customerPaid,
			PromoCode:                   promoCodeStr,
			OrderDate:                   orderDate,
			OrderTime:                   orderTime,
			DeliveryDate:                deliveryDate,
			DeliveryTime:                deliveryTime,
		}
		completedOrders = append(completedOrders, completedOrder)
	}

	// Return paginated response
	return &models.PaginationResponse{
		Success:    true,
		Data:       completedOrders,
		Total:      total,
		Page:       query.Page,
		PageSize:   query.ItemsPerPage,
		TotalPages: totalPages,
		HasNext:    query.Page < totalPages,
		HasPrev:    query.Page > 1,
	}, nil
}

// GetBrandReportForExcel generates brand-grouped completed orders data for Excel export
func (s *ReportService) GetBrandReportForExcel(query lmodels.ReportDetailQuery) ([]lmodels.BrandReportExcelItem, error) {
	// Build the base query for completed orders with brand information
	dbQuery := s.DB.Table("orders").
		Select(`
			brands.name as brand_name,
			orders.data_mapping
		`).
		Joins("LEFT JOIN sites ON orders.site_id = sites.id").
		Joins("LEFT JOIN brands ON sites.brand_id = brands.id").
		Where("orders.status = ? AND orders.data_mapping->>'delivery_time_unix' >= ? AND orders.data_mapping->>'delivery_time_unix' < ?",
			"FINISH", cast.ToString(query.From.Unix()), cast.ToString(query.To.Unix()))

	// Add brand filter if specified
	if len(query.BrandIDs) > 0 {
		dbQuery = dbQuery.Where("sites.brand_id IN (?)", query.BrandIDs)
	}

	// Add app filter if specified
	if len(query.Apps) > 0 {
		dbQuery = dbQuery.Where("orders.source IN (?)", query.Apps)
	}

	// Execute the query to get all completed orders
	var results []struct {
		BrandName   string                               `json:"brand_name"`
		DataMapping models.JSONField[models.DataMapping] `json:"data_mapping"`
	}

	if err := dbQuery.Find(&results).Error; err != nil {
		return nil, fmt.Errorf("error fetching completed orders for brand report: %v", err)
	}

	// Group orders by brand and calculate totals
	brandTotals := make(map[string]*lmodels.BrandReportExcelItem)
	var grandTotalRevenueBeforePromo, grandTotalPromotion, grandTotalRevenueAfterPromo float64

	for _, result := range results {
		brandName := result.BrandName
		if brandName == "" {
			brandName = "Unknown Brand"
		}

		dataMapping := result.DataMapping.Data
		financeData := dataMapping.FinanceData

		// Calculate financial values
		revenueBeforePromo := financeData.OriginalPrice
		totalPromotion := financeData.OtherPromotionPrice + financeData.CoFundPromotionPrice
		revenueAfterPromo := revenueBeforePromo - totalPromotion

		// Initialize brand totals if not exists
		if _, exists := brandTotals[brandName]; !exists {
			brandTotals[brandName] = &lmodels.BrandReportExcelItem{
				BrandName:          brandName,
				RevenueBeforePromo: 0,
				TotalPromotion:     0,
				RevenueAfterPromo:  0,
			}
		}

		// Add to brand totals
		brandTotals[brandName].RevenueBeforePromo += revenueBeforePromo
		brandTotals[brandName].TotalPromotion += totalPromotion
		brandTotals[brandName].RevenueAfterPromo += revenueAfterPromo

		// Add to grand totals
		grandTotalRevenueBeforePromo += revenueBeforePromo
		grandTotalPromotion += totalPromotion
		grandTotalRevenueAfterPromo += revenueAfterPromo
	}

	// Convert map to slice and sort by brand name
	var brandReportItems []lmodels.BrandReportExcelItem
	for _, item := range brandTotals {
		brandReportItems = append(brandReportItems, *item)
	}

	// Sort brands alphabetically
	for i := 0; i < len(brandReportItems)-1; i++ {
		for j := i + 1; j < len(brandReportItems); j++ {
			if strings.ToLower(brandReportItems[i].BrandName) > strings.ToLower(brandReportItems[j].BrandName) {
				brandReportItems[i], brandReportItems[j] = brandReportItems[j], brandReportItems[i]
			}
		}
	}

	// Add grand total row at the end
	brandReportItems = append(brandReportItems, lmodels.BrandReportExcelItem{
		BrandName:          "TỔNG",
		RevenueBeforePromo: grandTotalRevenueBeforePromo,
		TotalPromotion:     grandTotalPromotion,
		RevenueAfterPromo:  grandTotalRevenueAfterPromo,
	})

	return brandReportItems, nil
}

// GetBrandReportPaginated generates paginated brand report data
func (s *ReportService) GetBrandReportPaginated(query lmodels.ReportDetailQuery) (*models.PaginationResponse, error) {
	// Build the base query for completed orders with brand information
	dbQuery := s.DB.Table("orders").
		Select(`
			brands.name as brand_name,
			orders.data_mapping
		`).
		Joins("LEFT JOIN sites ON orders.site_id = sites.id").
		Joins("LEFT JOIN brands ON sites.brand_id = brands.id").
		Where("orders.status = ? AND orders.data_mapping->>'delivery_time_unix' >= ? AND orders.data_mapping->>'delivery_time_unix' < ?",
			"FINISH", cast.ToString(query.From.Unix()), cast.ToString(query.To.Unix()))

	// Add brand filter if specified
	if len(query.BrandIDs) > 0 {
		dbQuery = dbQuery.Where("sites.brand_id IN (?)", query.BrandIDs)
	}

	// Add app filter if specified
	if len(query.Apps) > 0 {
		dbQuery = dbQuery.Where("orders.source IN (?)", query.Apps)
	}

	// Execute the query to get all completed orders for grouping
	var results []struct {
		BrandName   string                               `json:"brand_name"`
		DataMapping models.JSONField[models.DataMapping] `json:"data_mapping"`
	}

	if err := dbQuery.Find(&results).Error; err != nil {
		return nil, fmt.Errorf("error fetching completed orders for brand report: %v", err)
	}

	// Group orders by brand and calculate totals
	brandTotals := make(map[string]*lmodels.BrandReportExcelItem)
	var grandTotalRevenueBeforePromo, grandTotalPromotion, grandTotalRevenueAfterPromo float64

	for _, result := range results {
		brandName := result.BrandName
		if brandName == "" {
			brandName = "Unknown Brand"
		}

		dataMapping := result.DataMapping.Data
		financeData := dataMapping.FinanceData

		// Calculate financial values
		revenueBeforePromo := financeData.OriginalPrice
		totalPromotion := financeData.OtherPromotionPrice + financeData.CoFundPromotionPrice
		revenueAfterPromo := revenueBeforePromo - totalPromotion

		// Initialize brand totals if not exists
		if _, exists := brandTotals[brandName]; !exists {
			brandTotals[brandName] = &lmodels.BrandReportExcelItem{
				BrandName:          brandName,
				RevenueBeforePromo: 0,
				TotalPromotion:     0,
				RevenueAfterPromo:  0,
			}
		}

		// Add to brand totals
		brandTotals[brandName].RevenueBeforePromo += revenueBeforePromo
		brandTotals[brandName].TotalPromotion += totalPromotion
		brandTotals[brandName].RevenueAfterPromo += revenueAfterPromo

		// Add to grand totals
		grandTotalRevenueBeforePromo += revenueBeforePromo
		grandTotalPromotion += totalPromotion
		grandTotalRevenueAfterPromo += revenueAfterPromo
	}

	// Convert map to slice and sort by brand name
	var brandReportItems []lmodels.BrandReportExcelItem
	for _, item := range brandTotals {
		brandReportItems = append(brandReportItems, *item)
	}

	// Sort brands alphabetically
	for i := 0; i < len(brandReportItems)-1; i++ {
		for j := i + 1; j < len(brandReportItems); j++ {
			if strings.ToLower(brandReportItems[i].BrandName) > strings.ToLower(brandReportItems[j].BrandName) {
				brandReportItems[i], brandReportItems[j] = brandReportItems[j], brandReportItems[i]
			}
		}
	}

	// Add grand total row at the end
	brandReportItems = append(brandReportItems, lmodels.BrandReportExcelItem{
		BrandName:          "TỔNG",
		RevenueBeforePromo: grandTotalRevenueBeforePromo,
		TotalPromotion:     grandTotalPromotion,
		RevenueAfterPromo:  grandTotalRevenueAfterPromo,
	})

	// Set default pagination values if not provided
	if query.Page == 0 {
		query.Page = 1
	}
	if query.ItemsPerPage == 0 {
		query.ItemsPerPage = 20
	}

	// Apply pagination to the brand report items
	total := int64(len(brandReportItems))
	totalPages := int(math.Ceil(float64(total) / float64(query.ItemsPerPage)))
	offset := (query.Page - 1) * query.ItemsPerPage

	// Get the paginated slice
	var paginatedItems []lmodels.BrandReportExcelItem
	if offset < len(brandReportItems) {
		end := offset + query.ItemsPerPage
		if end > len(brandReportItems) {
			end = len(brandReportItems)
		}
		paginatedItems = brandReportItems[offset:end]
	}

	// Return paginated response
	return &models.PaginationResponse{
		Success:    true,
		Data:       paginatedItems,
		Total:      total,
		Page:       query.Page,
		PageSize:   query.ItemsPerPage,
		TotalPages: totalPages,
		HasNext:    query.Page < totalPages,
		HasPrev:    query.Page > 1,
	}, nil
}

// GetCancelledOrdersForExcel generates detailed cancelled orders data for Excel export
func (s *ReportService) GetCancelledOrdersForExcel(query lmodels.ReportDetailQuery) ([]lmodels.CancelledOrderExcelItem, error) {
	// Build the base query for cancelled orders with all necessary joins
	dbQuery := s.DB.Table("orders").
		Select(`
			orders.short_order_id,
			orders.order_id,
			orders.source,
			orders.site_id,
			orders.hub_id,
			sites.name as site_name,
			hubs.name as hub_name,
			brands.name as brand_name,
			orders.data_mapping
		`).
		Joins("LEFT JOIN sites ON orders.site_id = sites.id").
		Joins("LEFT JOIN hubs ON orders.hub_id = hubs.id").
		Joins("LEFT JOIN brands ON sites.brand_id = brands.id").
		Where("orders.status = ? AND orders.data_mapping->>'order_time_sort' >= ? AND orders.data_mapping->>'order_time_sort' < ?",
			"CANCEL", cast.ToString(query.From.Unix()), cast.ToString(query.To.Unix()))

	// Add brand filter if specified
	if len(query.BrandIDs) > 0 {
		dbQuery = dbQuery.Where("sites.brand_id IN (?)", query.BrandIDs)
	}

	// Add app filter if specified
	if len(query.Apps) > 0 {
		dbQuery = dbQuery.Where("orders.source IN (?)", query.Apps)
	}

	// Execute the query without pagination to get all cancelled orders
	var results []struct {
		ShortOrderID string                               `json:"short_order_id"`
		OrderID      string                               `json:"order_id"`
		Source       string                               `json:"source"`
		SiteID       string                               `json:"site_id"`
		HubID        string                               `json:"hub_id"`
		SiteName     string                               `json:"site_name"`
		HubName      string                               `json:"hub_name"`
		BrandName    string                               `json:"brand_name"`
		DataMapping  models.JSONField[models.DataMapping] `json:"data_mapping"`
	}

	if err := dbQuery.
		Order("orders.data_mapping->>'order_time_sort' DESC").
		Find(&results).Error; err != nil {
		return nil, fmt.Errorf("error fetching cancelled orders: %v", err)
	}

	// Transform results to CancelledOrderExcelItem
	var excelItems []lmodels.CancelledOrderExcelItem
	for i, result := range results {
		dataMapping := result.DataMapping.Data

		// Parse order time
		orderDate, orderTime := parseDateTime(dataMapping.OrderTime)

		// Get display name for source
		source := result.Source

		// Count total items in the order
		itemCount := 0
		for _, dish := range dataMapping.Dishes {
			itemCount += int(dish.Quantity)
		}

		// Map cancel_by to Vietnamese
		cancelledBy := mapCancelledBy(dataMapping.CancelBy)

		// Get cancel reason or use a default message
		cancelReason := dataMapping.CancelReason
		if cancelReason == "" {
			cancelReason = mapCancelType(dataMapping.CancelType)
		}

		excelItem := lmodels.CancelledOrderExcelItem{
			STT:           i + 1,
			ReferenceCode: result.OrderID,
			OrderCode:     result.ShortOrderID,
			Source:        source,
			HubName:       result.SiteName,
			BrandName:     result.BrandName,
			SiteName:      result.SiteName, // Using site name as store name
			ItemCount:     itemCount,
			OrderDate:     orderDate,
			OrderTime:     orderTime,
			CancelledBy:   cancelledBy,
			CancelReason:  cancelReason,
		}
		excelItems = append(excelItems, excelItem)
	}

	return excelItems, nil
}

// GetCancelledOrdersPaginated generates paginated cancelled orders data
func (s *ReportService) GetCancelledOrdersPaginated(query lmodels.ReportDetailQuery) (*models.PaginationResponse, error) {
	// Build the base query for cancelled orders with all necessary joins
	dbQuery := s.DB.Table("orders").
		Select(`
			orders.short_order_id,
			orders.order_id,
			orders.source,
			orders.site_id,
			orders.hub_id,
			sites.name as site_name,
			hubs.name as hub_name,
			brands.name as brand_name,
			orders.data_mapping
		`).
		Joins("LEFT JOIN sites ON orders.site_id = sites.id").
		Joins("LEFT JOIN hubs ON orders.hub_id = hubs.id").
		Joins("LEFT JOIN brands ON sites.brand_id = brands.id").
		Where("orders.status = ? AND orders.data_mapping->>'order_time_sort' >= ? AND orders.data_mapping->>'order_time_sort' < ?",
			"CANCEL", cast.ToString(query.From.Unix()), cast.ToString(query.To.Unix()))

	// Add brand filter if specified
	if len(query.BrandIDs) > 0 {
		dbQuery = dbQuery.Where("sites.brand_id IN (?)", query.BrandIDs)
	}

	// Add app filter if specified
	if len(query.Apps) > 0 {
		dbQuery = dbQuery.Where("orders.source IN (?)", query.Apps)
	}

	// Count total records
	var total int64
	if err := dbQuery.Count(&total).Error; err != nil {
		return nil, fmt.Errorf("error counting cancelled orders: %v", err)
	}

	// Set default pagination values if not provided
	if query.Page == 0 {
		query.Page = 1
	}
	if query.ItemsPerPage == 0 {
		query.ItemsPerPage = 20
	}

	// Calculate pagination
	offset := (query.Page - 1) * query.ItemsPerPage
	totalPages := int(math.Ceil(float64(total) / float64(query.ItemsPerPage)))

	// Execute the query with pagination
	var results []struct {
		ShortOrderID string                               `json:"short_order_id"`
		OrderID      string                               `json:"order_id"`
		Source       string                               `json:"source"`
		SiteID       string                               `json:"site_id"`
		HubID        string                               `json:"hub_id"`
		SiteName     string                               `json:"site_name"`
		HubName      string                               `json:"hub_name"`
		BrandName    string                               `json:"brand_name"`
		DataMapping  models.JSONField[models.DataMapping] `json:"data_mapping"`
	}

	if err := dbQuery.
		Order("orders.data_mapping->>'order_time_sort' DESC").
		Offset(offset).
		Limit(query.ItemsPerPage).
		Find(&results).Error; err != nil {
		return nil, fmt.Errorf("error fetching cancelled orders: %v", err)
	}

	// Transform results to CancelledOrderExcelItem
	var cancelledOrders []lmodels.CancelledOrderExcelItem
	for i, result := range results {
		dataMapping := result.DataMapping.Data

		// Parse order time
		orderDate, orderTime := parseDateTime(dataMapping.OrderTime)

		// Get display name for source
		source := result.Source

		// Count total items in the order
		itemCount := 0
		for _, dish := range dataMapping.Dishes {
			itemCount += int(dish.Quantity)
		}

		// Map cancel_by to Vietnamese
		cancelledBy := mapCancelledBy(dataMapping.CancelBy)

		// Get cancel reason or use a default message
		cancelReason := dataMapping.CancelReason
		if cancelReason == "" {
			cancelReason = mapCancelType(dataMapping.CancelType)
		}

		cancelledOrder := lmodels.CancelledOrderExcelItem{
			STT:           offset + i + 1,
			ReferenceCode: result.OrderID,
			OrderCode:     result.ShortOrderID,
			Source:        source,
			HubName:       result.SiteName,
			BrandName:     result.BrandName,
			SiteName:      result.SiteName, // Using site name as store name
			ItemCount:     itemCount,
			OrderDate:     orderDate,
			OrderTime:     orderTime,
			CancelledBy:   cancelledBy,
			CancelReason:  cancelReason,
		}
		cancelledOrders = append(cancelledOrders, cancelledOrder)
	}

	// Return paginated response
	return &models.PaginationResponse{
		Success:    true,
		Data:       cancelledOrders,
		Total:      total,
		Page:       query.Page,
		PageSize:   query.ItemsPerPage,
		TotalPages: totalPages,
		HasNext:    query.Page < totalPages,
		HasPrev:    query.Page > 1,
	}, nil
}

// mapCancelledBy maps cancel_by values to Vietnamese display text
func mapCancelledBy(cancelBy string) string {
	cancelByMap := map[string]string{
		"user":     "user",
		"merchant": "merchant",
		"system":   "system",
		"driver":   "driver",
	}

	if mapped, exists := cancelByMap[cancelBy]; exists {
		return mapped
	}
	return cancelBy
}

// mapCancelType maps cancel_type to Vietnamese reason text
func mapCancelType(cancelType string) string {
	cancelTypeMap := map[string]string{
		"out_stock":       "Hết hàng",
		"merchant_busy":   "Quán bận",
		"incorrect_order": "Đơn hàng không chính xác",
	}

	if mapped, exists := cancelTypeMap[cancelType]; exists {
		return mapped
	}
	return "Khác"
}

// GetOrderFeedbacksForExcel generates detailed order feedbacks data for Excel export
func (s *ReportService) GetOrderFeedbacksForExcel(query lmodels.ReportDetailQuery) ([]lmodels.OrderFeedbackExcelItem, error) {
	// Build the base query for order feedbacks with all necessary joins
	dbQuery := s.DB.Table("order_feedbacks").
		Select(`
			order_feedbacks.ref_id,
			order_feedbacks.order_id,
			order_feedbacks.source,
			order_feedbacks.customer_name,
			order_feedbacks.rating,
			order_feedbacks.comment,
			order_feedbacks.created_at_unix,
			orders.short_order_id,
			orders.data_mapping,
			sites.name as site_name,
			hubs.name as hub_name,
			brands.name as brand_name
		`).
		Joins("LEFT JOIN orders ON order_feedbacks.order_id = orders.order_id").
		Joins("LEFT JOIN sites ON order_feedbacks.site_id = sites.id").
		Joins("LEFT JOIN hubs ON sites.hub_id = hubs.id").
		Joins("LEFT JOIN brands ON sites.brand_id = brands.id").
		Where("order_feedbacks.created_at_unix >= ? AND order_feedbacks.created_at_unix < ?",
			query.From.Unix(), query.To.Unix())

	// Add brand filter if specified
	if len(query.BrandIDs) > 0 {
		dbQuery = dbQuery.Where("sites.brand_id IN (?)", query.BrandIDs)
	}

	// Add app filter if specified
	if len(query.Apps) > 0 {
		dbQuery = dbQuery.Where("order_feedbacks.source IN (?)", query.Apps)
	}

	// Execute the query
	var results []struct {
		RefID         string                               `json:"ref_id"`
		OrderID       string                               `json:"order_id"`
		Source        string                               `json:"source"`
		CustomerName  string                               `json:"customer_name"`
		Rating        int                                  `json:"rating"`
		Comment       string                               `json:"comment"`
		CreatedAtUnix int64                                `json:"created_at_unix"`
		ShortOrderID  string                               `json:"short_order_id"`
		DataMapping   models.JSONField[models.DataMapping] `json:"data_mapping"`
		SiteName      string                               `json:"site_name"`
		HubName       string                               `json:"hub_name"`
		BrandName     string                               `json:"brand_name"`
	}

	if err := dbQuery.
		Order("order_feedbacks.created_at_unix DESC").
		Find(&results).Error; err != nil {
		return nil, fmt.Errorf("error fetching order feedbacks: %v", err)
	}

	// Transform results to OrderFeedbackExcelItem
	var excelItems []lmodels.OrderFeedbackExcelItem
	for i, result := range results {
		// Parse rating date time from unix timestamp
		var ratingDateTime *time.Time
		if result.CreatedAtUnix > 0 {
			t := time.Unix(result.CreatedAtUnix, 0)
			ratingDateTime = &t
		}

		// Parse order and delivery times from data mapping if available
		var orderDateTime, deliveryDateTime *time.Time
		if result.DataMapping.Data.OrderTime != "" {
			if t, err := time.Parse(time.RFC3339, result.DataMapping.Data.OrderTime); err == nil {
				orderDateTime = &t
			} else if t, err := time.Parse("2006-01-02T15:04:05Z", result.DataMapping.Data.OrderTime); err == nil {
				orderDateTime = &t
			}
		}
		if result.DataMapping.Data.DeliveryTime != "" {
			if t, err := time.Parse(time.RFC3339, result.DataMapping.Data.DeliveryTime); err == nil {
				deliveryDateTime = &t
			} else if t, err := time.Parse("2006-01-02T15:04:05Z", result.DataMapping.Data.DeliveryTime); err == nil {
				deliveryDateTime = &t
			}
		}

		excelItem := lmodels.OrderFeedbackExcelItem{
			STT:              i + 1,
			ReferenceCode:    result.RefID,
			OrderCode:        result.ShortOrderID,
			Source:           result.Source, // Keep original source data
			HubName:          result.HubName,
			BrandName:        result.BrandName,
			StoreName:        result.SiteName, // Using site name as store name
			CustomerName:     result.CustomerName,
			Rating:           result.Rating,
			Comment:          result.Comment,
			RatingDateTime:   ratingDateTime,
			OrderDateTime:    orderDateTime,
			DeliveryDateTime: deliveryDateTime,
		}
		excelItems = append(excelItems, excelItem)
	}

	return excelItems, nil
}

// GetOrderFeedbacksPaginated generates paginated order feedbacks data
func (s *ReportService) GetOrderFeedbacksPaginated(query lmodels.ReportDetailQuery) (*models.PaginationResponse, error) {
	// Build the base query for order feedbacks with all necessary joins
	dbQuery := s.DB.Table("order_feedbacks").
		Select(`
			order_feedbacks.ref_id,
			order_feedbacks.order_id,
			order_feedbacks.source,
			order_feedbacks.customer_name,
			order_feedbacks.rating,
			order_feedbacks.comment,
			order_feedbacks.created_at_unix,
			orders.short_order_id,
			orders.data_mapping,
			sites.name as site_name,
			hubs.name as hub_name,
			brands.name as brand_name
		`).
		Joins("LEFT JOIN orders ON order_feedbacks.order_id = orders.order_id").
		Joins("LEFT JOIN sites ON order_feedbacks.site_id = sites.id").
		Joins("LEFT JOIN hubs ON sites.hub_id = hubs.id").
		Joins("LEFT JOIN brands ON sites.brand_id = brands.id").
		Where("order_feedbacks.created_at_unix >= ? AND order_feedbacks.created_at_unix < ?",
			query.From.Unix(), query.To.Unix())

	// Add brand filter if specified
	if len(query.BrandIDs) > 0 {
		dbQuery = dbQuery.Where("sites.brand_id IN (?)", query.BrandIDs)
	}

	// Add app filter if specified
	if len(query.Apps) > 0 {
		dbQuery = dbQuery.Where("order_feedbacks.source IN (?)", query.Apps)
	}

	// Count total records
	var total int64
	if err := dbQuery.Count(&total).Error; err != nil {
		return nil, fmt.Errorf("error counting order feedbacks: %v", err)
	}

	// Set default pagination values if not provided
	if query.Page == 0 {
		query.Page = 1
	}
	if query.ItemsPerPage == 0 {
		query.ItemsPerPage = 20
	}

	// Calculate pagination
	offset := (query.Page - 1) * query.ItemsPerPage
	totalPages := int(math.Ceil(float64(total) / float64(query.ItemsPerPage)))

	// Execute the query with pagination
	var results []struct {
		RefID         string                               `json:"ref_id"`
		OrderID       string                               `json:"order_id"`
		Source        string                               `json:"source"`
		CustomerName  string                               `json:"customer_name"`
		Rating        int                                  `json:"rating"`
		Comment       string                               `json:"comment"`
		CreatedAtUnix int64                                `json:"created_at_unix"`
		ShortOrderID  string                               `json:"short_order_id"`
		DataMapping   models.JSONField[models.DataMapping] `json:"data_mapping"`
		SiteName      string                               `json:"site_name"`
		HubName       string                               `json:"hub_name"`
		BrandName     string                               `json:"brand_name"`
	}

	if err := dbQuery.
		Order("order_feedbacks.created_at_unix DESC").
		Offset(offset).
		Limit(query.ItemsPerPage).
		Find(&results).Error; err != nil {
		return nil, fmt.Errorf("error fetching order feedbacks: %v", err)
	}

	// Transform results to OrderFeedbackExcelItem
	var orderFeedbacks []lmodels.OrderFeedbackExcelItem
	for i, result := range results {
		// Parse rating date time from unix timestamp
		var ratingDateTime *time.Time
		if result.CreatedAtUnix > 0 {
			t := time.Unix(result.CreatedAtUnix, 0)
			ratingDateTime = &t
		}

		// Parse order and delivery times from data mapping if available
		var orderDateTime, deliveryDateTime *time.Time
		if result.DataMapping.Data.OrderTime != "" {
			if t, err := time.Parse(time.RFC3339, result.DataMapping.Data.OrderTime); err == nil {
				orderDateTime = &t
			} else if t, err := time.Parse("2006-01-02T15:04:05Z", result.DataMapping.Data.OrderTime); err == nil {
				orderDateTime = &t
			}
		}
		if result.DataMapping.Data.DeliveryTime != "" {
			if t, err := time.Parse(time.RFC3339, result.DataMapping.Data.DeliveryTime); err == nil {
				deliveryDateTime = &t
			} else if t, err := time.Parse("2006-01-02T15:04:05Z", result.DataMapping.Data.DeliveryTime); err == nil {
				deliveryDateTime = &t
			}
		}

		orderFeedback := lmodels.OrderFeedbackExcelItem{
			STT:              offset + i + 1,
			ReferenceCode:    result.RefID,
			OrderCode:        result.ShortOrderID,
			Source:           result.Source, // Keep original source data
			HubName:          result.HubName,
			BrandName:        result.BrandName,
			StoreName:        result.SiteName, // Using site name as store name
			CustomerName:     result.CustomerName,
			Rating:           result.Rating,
			Comment:          result.Comment,
			RatingDateTime:   ratingDateTime,
			OrderDateTime:    orderDateTime,
			DeliveryDateTime: deliveryDateTime,
		}
		orderFeedbacks = append(orderFeedbacks, orderFeedback)
	}

	// Return paginated response
	return &models.PaginationResponse{
		Success:    true,
		Data:       orderFeedbacks,
		Total:      total,
		Page:       query.Page,
		PageSize:   query.ItemsPerPage,
		TotalPages: totalPages,
		HasNext:    query.Page < totalPages,
		HasPrev:    query.Page > 1,
	}, nil
}
