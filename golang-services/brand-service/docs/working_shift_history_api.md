# Working Shift History APIs

This document describes the new Working Shift History APIs that provide enhanced functionality for viewing and analyzing working shift data.

## Overview

The Working Shift History APIs extend the existing working shift functionality to provide:
- Paginated history with advanced filtering
- Detailed shift analysis with payment breakdowns
- Statistical summaries and time-series data

## Endpoints

### 1. Get Working Shift History

**Endpoint:** `GET /v1/brand-service/hubs/:hub_id/working-shifts/history`

**Description:** Get paginated working shift history with enhanced filtering options.

**Query Parameters:**
- `page` (optional, default: 1): Page number for pagination
- `limit` (optional, default: 10, max: 100): Number of items per page
- `status` (optional): Filter by status (`open` or `closed`)
- `start_date` (optional): Start date filter (format: YYYY-MM-DD)
- `end_date` (optional): End date filter (format: YYYY-MM-DD)
- `search` (optional): Search by user name

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "shift_id",
      "hub_id": "hub_id",
      "open_by_user_id": "user_id",
      "closed_by_user_id": "user_id",
      "start_time": "2024-01-15T08:00:00Z",
      "end_time": "2024-01-15T16:00:00Z",
      "initial_amount": 1000000,
      "total_amount": 1500000,
      "actual_amount": 1480000,
      "status": "closed",
      "open_by_user": {
        "id": "user_id",
        "name": "John Doe",
        "email": "<EMAIL>"
      },
      "closed_by_user": {
        "id": "user_id",
        "name": "Jane Smith",
        "email": "<EMAIL>"
      },
      "duration": "8h 0m",
      "summary": {
        "initial_amount": 1000000,
        "total_income": 600000,
        "total_outcome": 100000,
        "net_amount": 1500000,
        "actual_amount": 1480000,
        "variance": -20000,
        "total_orders": 25,
        "status": "closed"
      }
    }
  ],
  "total_pages": 5,
  "page": 1,
  "page_size": 10,
  "total": 45,
  "has_next": true,
  "has_prev": false
}
```

### 2. Get Working Shift History Detail

**Endpoint:** `GET /v1/brand-service/hubs/:hub_id/working-shifts/:shift_id/history`

**Description:** Get detailed history of a specific working shift including payment method breakdown.

**Response:**
```json
{
  "success": true,
  "data": {
    "shift": {
      "id": "shift_id",
      "hub_id": "hub_id",
      "start_time": "2024-01-15T08:00:00Z",
      "end_time": "2024-01-15T16:00:00Z",
      "initial_amount": 1000000,
      "income": [
        {
          "order_id": "order_123",
          "payment_method": "CASH",
          "amount": 150000,
          "note": "Order payment"
        }
      ],
      "outcome": [
        {
          "payment_method": "CASH",
          "amount": 50000,
          "note": "Change given"
        }
      ],
      "total_amount": 1500000,
      "actual_amount": 1480000,
      "status": "closed"
    },
    "hub": {
      "id": "hub_id",
      "name": "Hub Name",
      "address": "Hub Address"
    },
    "open_by_user": {
      "id": "user_id",
      "name": "John Doe",
      "email": "<EMAIL>"
    },
    "closed_by_user": {
      "id": "user_id",
      "name": "Jane Smith",
      "email": "<EMAIL>"
    },
    "duration": "8h 0m",
    "payment_breakdown": {
      "income_by_method": {
        "CASH": 400000,
        "TRANSFER": 200000
      },
      "outcome_by_method": {
        "CASH": 100000
      },
      "net_by_method": {
        "CASH": 300000,
        "TRANSFER": 200000
      },
      "orders_by_method": {
        "CASH": 15,
        "TRANSFER": 10
      }
    },
    "summary": {
      "initial_amount": 1000000,
      "total_income": 600000,
      "total_outcome": 100000,
      "net_amount": 1500000,
      "actual_amount": 1480000,
      "variance": -20000,
      "total_orders": 25,
      "status": "closed"
    }
  }
}
```

### 3. Get Working Shift Statistics

**Endpoint:** `GET /v1/brand-service/hubs/:hub_id/working-shifts/statistics`

**Description:** Get statistical summary of working shifts over a specified period.

**Query Parameters:**
- `start_date` (required): Start date (format: YYYY-MM-DD)
- `end_date` (required): End date (format: YYYY-MM-DD)
- `group_by` (optional, default: day): Group data by `day`, `week`, or `month`

**Response:**
```json
{
  "success": true,
  "data": {
    "hub": {
      "id": "hub_id",
      "name": "Hub Name",
      "address": "Hub Address"
    },
    "period": {
      "start_date": "2024-01-01",
      "end_date": "2024-01-31",
      "group_by": "day"
    },
    "summary": {
      "total_shifts": 31,
      "closed_shifts": 30,
      "open_shifts": 1,
      "total_revenue": 45000000,
      "total_initial_amount": 31000000,
      "total_actual_amount": 44800000,
      "average_duration": "8h 15m",
      "average_revenue": 1500000
    },
    "payment_methods": {
      "CASH": 25000000,
      "TRANSFER": 15000000,
      "CARD": 5000000
    },
    "time_series": [
      {
        "date": "2024-01-01",
        "shifts_count": 1,
        "total_revenue": 1450000
      },
      {
        "date": "2024-01-02",
        "shifts_count": 1,
        "total_revenue": 1520000
      }
    ]
  }
}
```

## Error Responses

All endpoints return standardized error responses:

```json
{
  "success": false,
  "error_code": "ERROR_CODE",
  "error_message": "Detailed error message"
}
```

Common error codes:
- `INVALID_PARAMETERS`: Invalid query parameters
- `FETCH_HISTORY_FAILED`: Failed to fetch history data
- `FETCH_HISTORY_DETAIL_FAILED`: Failed to fetch detailed history
- `FETCH_STATISTICS_FAILED`: Failed to fetch statistics

## Authentication

All endpoints require authentication via Bearer token in the Authorization header:

```
Authorization: Bearer <access_token>
```

## Permissions

Users need the `PermViewHubs` permission to access these endpoints.

## Usage Examples

### Get recent working shifts
```bash
curl -X GET "http://localhost:3000/v1/brand-service/hubs/hub_123/working-shifts/history?page=1&limit=10" \
  -H "Authorization: Bearer <token>"
```

### Get shifts for a specific date range
```bash
curl -X GET "http://localhost:3000/v1/brand-service/hubs/hub_123/working-shifts/history?start_date=2024-01-01&end_date=2024-01-31" \
  -H "Authorization: Bearer <token>"
```

### Get detailed shift information
```bash
curl -X GET "http://localhost:3000/v1/brand-service/hubs/hub_123/working-shifts/shift_456/history" \
  -H "Authorization: Bearer <token>"
```

### Get monthly statistics
```bash
curl -X GET "http://localhost:3000/v1/brand-service/hubs/hub_123/working-shifts/statistics?start_date=2024-01-01&end_date=2024-01-31&group_by=month" \
  -H "Authorization: Bearer <token>"
```
