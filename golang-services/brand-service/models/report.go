package models

import (
	"time"
)

// ReportQuery represents the query parameters for the report endpoint
type ReportQuery struct {
	HubID      string    `form:"hub_id"`
	Apps       []string  `form:"apps"`
	TimeFilter string    `form:"time_filter"`
	From       time.Time `form:"from" binding:"required" time_format:"2006-01-02T15:04:05Z"`
	To         time.Time `form:"to" binding:"required" time_format:"2006-01-02T15:04:05Z"`
}

// ReportDetailQuery represents the query parameters for the report detail endpoint
type ReportDetailQuery struct {
	BrandIDs     []string  `form:"brand_ids"`
	Apps         []string  `form:"apps"`
	Headers      []string  `form:"headers"`
	From         time.Time `form:"from" binding:"required" time_format:"2006-01-02T15:04:05Z"`
	To           time.Time `form:"to" binding:"required" time_format:"2006-01-02T15:04:05Z"`
	Page         int       `form:"page"`
	ItemsPerPage int       `form:"items_per_page"`
}

// MerchantSummary represents the summary data for a specific merchant/app
type MerchantSummary struct {
	Name    string  `json:"name"`
	Count   int     `json:"count"`
	Revenue float64 `json:"revenue"`
}

// SiteSummary represents the summary data for a specific site
type SiteSummary struct {
	Name               string  `json:"name"`
	OrderCount         int     `json:"order_count"`
	RevenueBeforePromo float64 `json:"revenue_before_promo"`
	RevenueAfterPromo  float64 `json:"revenue_after_promo"`
	TotalRevenue       float64 `json:"total_revenue"`
}

// ChartData represents the chart data for the report
type ChartData struct {
	DataLabels         []string    `json:"data_labels"`
	AxisLabels         []string    `json:"axis_labels"`
	Datasets           [][]float64 `json:"datasets"`
	OrderCountDatasets [][]int     `json:"order_count_datasets"`
}

// ReportResponse represents the response data for the report endpoint
type ReportResponse struct {
	// Summary data
	TotalRevenue       float64 `json:"total_revenue"`
	OrderCount         int     `json:"order_count"`
	RevenueBeforePromo float64 `json:"revenue_before_promo"`
	RevenueAfterPromo  float64 `json:"revenue_after_promo"`

	// Detailed data
	MerchantSummaries []MerchantSummary `json:"merchant_summaries"`
	SiteSummaries     []SiteSummary     `json:"site_summaries"`
}

// ReportDetailItem represents a single row in the report detail with item-level data
type ReportDetailItem struct {
	STT            int        `json:"stt"`             // STT - Sequential number
	OrderID        string     `json:"order_id"`        // Mã tham chiếu (Reference code)
	ShortOrderID   string     `json:"short_order_id"`  // Mã đơn hàng (Order code)
	Source         string     `json:"source"`          // Kinh doanh (Business/Sales channel)
	SiteName       string     `json:"site_name"`       // Điểm đến (Destination)
	HubName        string     `json:"hub_name"`        // Thuộc nhóm (Group)
	BrandName      string     `json:"brand_name"`      // Gián hàng (Store)
	ItemCode       string     `json:"item_code"`       // Mã món (Item code)
	Category       string     `json:"category"`        // Phần loại (Category)
	ItemName       string     `json:"item_name"`       // Tên món (Item name)
	DriverName     string     `json:"driver_name"`     // Tên tài xế (Driver name)
	Quantity       int64      `json:"quantity"`        // SL bán (Quantity sold)
	Price          float64    `json:"price"`           // Giá gốc (Original price)
	DiscountPrice  float64    `json:"discount_price"`  // Giá đã áp dụng (Applied price)
	Total          float64    `json:"total"`           // Tổng tiền hàng (Total item amount)
	Discount       float64    `json:"discount"`        // Khuyến mãi (Item promotion)
	TotalDiscount  float64    `json:"total_discount"`  // Tổng khuyến mãi (Total promotion)
	FinalAmount    float64    `json:"final_amount"`    // Thành tiền sau KM (Amount after promotion)
	Status         string     `json:"status"`          // Trạng thái (Status)
	OrderTime      *time.Time `json:"order_time"`      // Giờ đặt hàng (Order time)
	DeliveryTime   *time.Time `json:"delivery_time"`   // Giờ giao hàng (Delivery time)
	CompletionTime *time.Time `json:"completion_time"` // Thời gian hoàn thành (Completion time)
}

// CompletedOrderExcelItem represents a single row in the completed orders Excel export
type CompletedOrderExcelItem struct {
	STT                         int     `json:"stt"`                            // STT
	ReferenceCode               string  `json:"reference_code"`                 // Mã tham chiếu
	OrderCode                   string  `json:"order_code"`                     // Mã đơn hàng
	Source                      string  `json:"source"`                         // Kênh bán
	HubName                     string  `json:"hub_name"`                       // Điểm bán
	BrandName                   string  `json:"brand_name"`                     // Thương hiệu
	SiteName                    string  `json:"site_name"`                      // Gian hàng
	RevenueBeforePromo          float64 `json:"revenue_before_promo"`           // Doanh thu trước KM (1)
	ProductDiscount             float64 `json:"product_discount"`               // Giảm giá sản phẩm (2)
	RevenueAfterProductDiscount float64 `json:"revenue_after_product_discount"` // Thành tiền sau giảm giá SP (3) = (1) - (2)
	OrderDiscount               float64 `json:"order_discount"`                 // Giảm giá tổng đơn (4)
	TotalPromotion              float64 `json:"total_promotion"`                // Tổng KM (5) = (2) + (4)
	RevenueAfterPromo           float64 `json:"revenue_after_promo"`            // Doanh thu sau KM (6) = (1) - (5)
	AdditionalIncome            float64 `json:"additional_income"`              // Thu khác (7)
	ShippingFee                 float64 `json:"shipping_fee"`                   // Tiền ship (8)
	CustomerPaid                float64 `json:"customer_paid"`                  // Thực thu khách hàng (9) = (6) + (7) + (8)
	PromoCode                   string  `json:"promo_code"`                     // Mã KM
	OrderDate                   string  `json:"order_date"`                     // Ngày đặt hàng
	OrderTime                   string  `json:"order_time"`                     // Giờ đặt hàng
	DeliveryDate                string  `json:"delivery_date"`                  // Ngày giao hàng
	DeliveryTime                string  `json:"delivery_time"`                  // Giờ giao hàng
}

// BrandReportExcelItem represents a single row in the brand report Excel export
type BrandReportExcelItem struct {
	BrandName          string  `json:"brand_name"`           // Thương hiệu
	RevenueBeforePromo float64 `json:"revenue_before_promo"` // Doanh thu trước KM (1)
	TotalPromotion     float64 `json:"total_promotion"`      // Tổng KM (2)
	RevenueAfterPromo  float64 `json:"revenue_after_promo"`  // Doanh thu sau KM (3) = (1) - (2)
}

// CancelledOrderExcelItem represents a single row in the cancelled orders Excel export
type CancelledOrderExcelItem struct {
	STT           int    `json:"stt"`            // STT
	ReferenceCode string `json:"reference_code"` // Mã tham chiếu
	OrderCode     string `json:"order_code"`     // Mã đơn hàng
	Source        string `json:"source"`         // Kênh bán
	HubName       string `json:"hub_name"`       // Điểm bán
	BrandName     string `json:"brand_name"`     // Thương hiệu
	SiteName      string `json:"site_name"`      // Gian hàng
	ItemCount     int    `json:"item_count"`     // Số lượng món
	OrderDate     string `json:"order_date"`     // Ngày đặt hàng
	OrderTime     string `json:"order_time"`     // Giờ đặt hàng
	CancelledBy   string `json:"cancelled_by"`   // Hủy bởi
	CancelReason  string `json:"cancel_reason"`  // Lý do hủy
}

// OrderFeedbackExcelItem represents a single row in the order feedbacks Excel export
type OrderFeedbackExcelItem struct {
	STT              int        `json:"stt"`                // STT
	ReferenceCode    string     `json:"reference_code"`     // Mã tham chiếu
	OrderCode        string     `json:"order_code"`         // Mã đơn hàng
	Source           string     `json:"source"`             // Kênh bán
	HubName          string     `json:"hub_name"`           // Điểm bán
	BrandName        string     `json:"brand_name"`         // Thương hiệu
	StoreName        string     `json:"store_name"`         // Gian hàng
	CustomerName     string     `json:"customer_name"`      // Tên KH
	Rating           int        `json:"rating"`             // Điểm đánh giá
	Comment          string     `json:"comment"`            // Bình luận
	RatingDateTime   *time.Time `json:"rating_date_time"`   // Ngày đánh giá
	OrderDateTime    *time.Time `json:"order_date_time"`    // Ngày đặt hàng
	DeliveryDateTime *time.Time `json:"delivery_date_time"` // Ngày giao hàng
}
