# User Meta Data API Documentation

This document describes the User Meta Data APIs that allow users to store and manage custom metadata as key-value pairs.

## Overview

The User Meta Data APIs provide CRUD operations for managing user-specific metadata. Each metadata entry consists of:
- `user_id`: Foreign key to the users table
- `meta_key`: String identifier for the metadata
- `meta_value`: JSONB field containing the actual data

## Database Schema

```sql
CREATE TABLE user_meta_data (
    id VARCHAR PRIMARY KEY DEFAULT gen_object_id(),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    user_id VARCHAR NOT NULL REFERENCES users(id),
    meta_key VARCHAR NOT NULL,
    meta_value JSONB NOT NULL
);

CREATE INDEX idx_user_meta_data_user_id ON user_meta_data(user_id);
CREATE INDEX idx_user_meta_data_meta_key ON user_meta_data(meta_key);
```

## API Endpoints

All endpoints require authentication via the `requireAuth()` middleware.

### 1. Get All User Meta Data

**GET** `/v1/user-service/user/meta_data`

Returns all metadata entries for the authenticated user.

**Response:**
```json
{
    "success": true,
    "data": [
        {
            "meta_key": "preferences",
            "meta_value": {
                "theme": "dark",
                "language": "en",
                "notifications": true
            }
        },
        {
            "meta_key": "settings",
            "meta_value": {
                "auto_save": true,
                "backup_frequency": "daily"
            }
        }
    ]
}
```

### 2. Get Meta Data by Key

**GET** `/v1/user-service/user/meta_data/:key`

Returns specific metadata entry by key for the authenticated user.

**Parameters:**
- `key` (path): The meta key to retrieve

**Response (Success):**
```json
{
    "success": true,
    "data": {
        "meta_key": "preferences",
        "meta_value": {
            "theme": "dark",
            "language": "en",
            "notifications": true
        }
    }
}
```

**Response (Not Found):**
```json
{
    "success": false,
    "error_code": "meta_data_not_found",
    "error_message": "Meta data not found"
}
```

### 3. Create or Update Meta Data

**POST** `/v1/user-service/user/meta_data`

Creates new metadata or updates existing metadata for the authenticated user.

**Request Body:**
```json
{
    "meta_key": "preferences",
    "meta_value": {
        "theme": "dark",
        "language": "en",
        "notifications": true
    }
}
```

**Response (Created - 201):**
```json
{
    "success": true,
    "data": {
        "meta_key": "preferences",
        "meta_value": {
            "theme": "dark",
            "language": "en",
            "notifications": true
        }
    }
}
```

**Response (Updated - 200):**
```json
{
    "success": true,
    "data": {
        "meta_key": "preferences",
        "meta_value": {
            "theme": "light",
            "language": "vi",
            "notifications": false
        }
    }
}
```

### 4. Delete Meta Data

**DELETE** `/v1/user-service/user/meta_data/:key`

Deletes metadata entry by key for the authenticated user.

**Parameters:**
- `key` (path): The meta key to delete

**Response (Success):**
```json
{
    "success": true,
    "message": "Meta data deleted successfully"
}
```

**Response (Not Found):**
```json
{
    "success": false,
    "error_code": "meta_data_not_found",
    "error_message": "Meta data not found"
}
```

## Error Responses

All endpoints follow the standard error response format:

```json
{
    "success": false,
    "error_code": "error_type",
    "error_message": "Human readable error message"
}
```

Common error codes:
- `invalid_request`: Request validation failed
- `meta_data_not_found`: Requested metadata not found
- `database_error`: Database operation failed
- `unauthorized`: Authentication required

## Usage Examples

### Store User Preferences
```bash
curl -X POST "http://localhost:3000/v1/user-service/user/meta_data" \
  -H "Content-Type: application/json" \
  -H "x-access-token: YOUR_JWT_TOKEN" \
  -d '{
    "meta_key": "preferences",
    "meta_value": {
      "theme": "dark",
      "language": "en",
      "notifications": true
    }
  }'
```

### Store App Settings
```bash
curl -X POST "http://localhost:3000/v1/user-service/user/meta_data" \
  -H "Content-Type: application/json" \
  -H "x-access-token: YOUR_JWT_TOKEN" \
  -d '{
    "meta_key": "app_settings",
    "meta_value": {
      "auto_save": true,
      "backup_frequency": "daily",
      "sync_enabled": false
    }
  }'
```

### Retrieve User Preferences
```bash
curl -X GET "http://localhost:3000/v1/user-service/user/meta_data/preferences" \
  -H "x-access-token: YOUR_JWT_TOKEN"
```

### Delete User Settings
```bash
curl -X DELETE "http://localhost:3000/v1/user-service/user/meta_data/app_settings" \
  -H "x-access-token: YOUR_JWT_TOKEN"
```

## Implementation Notes

1. **Authentication**: All endpoints require valid JWT token via `x-access-token` header
2. **User Isolation**: Users can only access their own metadata
3. **JSONB Storage**: Meta values are stored as JSONB for efficient querying and indexing
4. **Upsert Logic**: POST endpoint creates new entries or updates existing ones based on meta_key
5. **Error Handling**: Consistent error response format with appropriate HTTP status codes
6. **Database Indexes**: Optimized for user_id and meta_key lookups

## Security Considerations

- All operations are scoped to the authenticated user
- No cross-user data access is possible
- Input validation prevents malformed JSON in meta_value
- Standard authentication middleware protects all endpoints
