package router

import (
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/middlewares"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"

	"github.com/gin-gonic/gin"
)

func LoadHandlers(r gin.IRouter) gin.IRouter {
	requireAuth := middlewares.Authorize
	requireAuthWithPermission := middlewares.AuthorizeWithPermission
	requiredInternal := middlewares.InternalServiceAuth

	api := r.Group("v1/user-service")
	{
		api.GET("health", HealthCheck)
		// Auth & User Management
		api.POST("login", Login)
		api.POST("login_as_guest", LoginAsGuest)
		api.POST("register", Register)
		api.POST("active_account", ActiveAccount)
		api.POST("verify_token", VerifyToken)
		api.POST("v2/login", Login)
		api.POST("v2/logout", requireAuth(), Logout)
		// api.POST("otp/verify", VerifyOTP)
		// api.POST("otp/resend", ResendOTP)
		// api.POST("v2/verify_token", VerifyTokenV2)

		// User Management
		usersGroup := api.Group("users")
		{
			usersGroup.GET("", requireAuthWithPermission(models.PermViewUsers), GetUserList)
			usersGroup.GET(":user_id", requireAuthWithPermission(models.PermViewUsers), GetUserByID)
			// usersGroup.GET("phone/:phone", GetUserByPhone)
			usersGroup.POST("", requireAuthWithPermission(models.PermCreateUser), CreateUser)
			usersGroup.PUT(":user_id", requireAuthWithPermission(models.PermUpdateUser), UpdateUser)
			usersGroup.DELETE(":user_id", requireAuthWithPermission(models.PermDeleteUser), DeleteUser)

		}

		userGroup := api.Group("user")
		{
			userGroup.GET("current_user", requireAuth(), GetCurrentUser)
			// userGroup.PUT("basic_infos", required(), UpdateUserBasicInfos)
			userGroup.POST("change_password", requireAuth(), ChangePassword)
			userGroup.POST("forgot_password", ForgotPassword)
			userGroup.POST("change_password_by_codes", ChangePasswordByCode)
			userGroup.POST("contact_us", ContactUs)
			userGroup.GET("notifications", requireAuth(), GetUserNotifications)
			userGroup.POST("notifications", requireAuth(), UserReadNotifications)

			// User Address Management
			userGroup.GET("addresses", requireAuth(), GetUserAddressList)
			userGroup.POST("addresses", requireAuth(), CreateUserAddress)
			userGroup.PUT("addresses/:address_id", requireAuth(), UpdateUserAddress)
			userGroup.DELETE("addresses/:address_id", requireAuth(), DeleteUserAddress)

			// User Meta Data Management
			userGroup.GET("meta_data", requireAuth(), GetUserMetaDataList)
			userGroup.GET("meta_data/:key", requireAuth(), GetUserMetaDataByKey)
			userGroup.POST("meta_data", requireAuth(), CreateOrUpdateUserMetaData)
			userGroup.DELETE("meta_data/:key", requireAuth(), DeleteUserMetaData)
		}

		// Role Management
		api.GET("roles", requireAuth(), GetRoleList)
		// api.POST("roles", required("role"), CreateRole)
		// api.PUT("roles/:role_id", required("role"), UpdateRole)
		// api.DELETE("roles/:role_id", required("role"), DeleteRole)

		// Subscription Management
		// api.POST("subscription/init", required(), InitSubscription)
		api.GET("subscription/payment/check", requireAuth(), CheckSubscriptionPayment)
		api.POST("subscription/payment/webhook", requiredInternal(), SubscriptionPaymentServerCallback)
		api.GET("subscription/current", requireAuth(), GetCurrentSubscription)
		api.POST("subscription/upgrade", requireAuth(), UpdateSubscriptionPlan)
		api.POST("subscription/cancel", requireAuth(), CancelSubscriptionRequest)
		api.POST("subscription/repay", requireAuth(), RepaySubscriptionRequest)
		api.GET("subscription/resource-usage", requireAuthWithPermission(models.PermManageSubscription), GetResourceUsage)
	}
	return r
}
