package router

import (
	"net/http"
	"time"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/middlewares"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/utils"

	"github.com/gin-gonic/gin"
)

// GetUserMetaDataList retrieves all meta data for the current user
// Jira: https://nexdor-tech.atlassian.net/browse/NP-1928
func GetUserMetaDataList(c *gin.Context) {
	// Get database connection
	db := middlewares.GetDB(c)

	// Get current user
	user := middlewares.GetUser(c)
	// Get all meta data for the user
	var metaDataList []models.UserMetaData
	if err := db.Where("user_id = ?", user.ID).
		Order("created_at DESC").
		Find(&metaDataList).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success":       false,
			"error_code":    "database_error",
			"error_message": err.Error(),
		})
		return
	}

	// Transform to return only meta_key and meta_value
	var result []gin.H
	for _, metaData := range metaDataList {
		result = append(result, gin.H{
			"meta_key":   metaData.MetaKey,
			"meta_value": metaData.MetaValue.Data,
		})
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    result,
	})
}

// GetUserMetaDataByKey retrieves specific meta data by key for the current user
// Jira: https://nexdor-tech.atlassian.net/browse/NP-1928
func GetUserMetaDataByKey(c *gin.Context) {
	// Get database connection
	db := middlewares.GetDB(c)

	// Get current user
	user := middlewares.GetUser(c)

	// Get meta key from URL parameter
	metaKey := c.Param("key")

	// Find meta data by key
	var metaData models.UserMetaData
	if err := db.Where("user_id = ? AND meta_key = ?", user.ID, metaKey).
		First(&metaData).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success":       false,
			"error_code":    "meta_data_not_found",
			"error_message": "Meta data not found",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"meta_key":   metaData.MetaKey,
			"meta_value": metaData.MetaValue.Data,
		},
	})
}

// CreateOrUpdateUserMetaData creates or updates meta data for the current user
// Jira: https://nexdor-tech.atlassian.net/browse/NP-1928
func CreateOrUpdateUserMetaData(c *gin.Context) {
	// Get database connection
	db := middlewares.GetDB(c)

	// Get current user
	user := middlewares.GetUser(c)

	// Define request structure
	var request struct {
		MetaKey   string         `json:"meta_key" binding:"required"`
		MetaValue map[string]any `json:"meta_value" binding:"required"`
	}

	// Parse request body
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success":       false,
			"error_code":    "invalid_request",
			"error_message": err.Error(),
		})
		return
	}

	// Check if meta data already exists
	var existingMetaData models.UserMetaData
	exists := db.Where("user_id = ? AND meta_key = ?", user.ID, request.MetaKey).
		First(&existingMetaData).Error == nil

	if exists {
		// Update existing meta data
		existingMetaData.MetaValue = models.JSONField[map[string]any]{Data: request.MetaValue}
		existingMetaData.UpdatedAt = time.Now()

		if err := db.Save(&existingMetaData).Error; err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"success":       false,
				"error_code":    "database_error",
				"error_message": err.Error(),
			})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"data": gin.H{
				"meta_key":   existingMetaData.MetaKey,
				"meta_value": existingMetaData.MetaValue.Data,
			},
		})
	} else {
		// Create new meta data
		newMetaData := models.UserMetaData{
			ID:        utils.GenObjectID(),
			UserID:    user.ID,
			MetaKey:   request.MetaKey,
			MetaValue: models.JSONField[map[string]any]{Data: request.MetaValue},
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		}

		if err := db.Create(&newMetaData).Error; err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"success":       false,
				"error_code":    "database_error",
				"error_message": err.Error(),
			})
			return
		}

		c.JSON(http.StatusCreated, gin.H{
			"success": true,
			"data": gin.H{
				"meta_key":   newMetaData.MetaKey,
				"meta_value": newMetaData.MetaValue.Data,
			},
		})
	}
}

// DeleteUserMetaData deletes meta data by key for the current user
func DeleteUserMetaData(c *gin.Context) {
	// Get database connection
	db := middlewares.GetDB(c)

	// Get current user
	user := middlewares.GetUser(c)

	// Get meta key from URL parameter
	metaKey := c.Param("key")

	// Find and delete meta data
	var metaData models.UserMetaData
	if err := db.Where("user_id = ? AND meta_key = ?", user.ID, metaKey).
		First(&metaData).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success":       false,
			"error_code":    "meta_data_not_found",
			"error_message": "Meta data not found",
		})
		return
	}

	// Delete the meta data
	if err := db.Delete(&metaData).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success":       false,
			"error_code":    "database_error",
			"error_message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Meta data deleted successfully",
	})
}
