package main

import (
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/mapping"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/middlewares"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"
)

func Test1() {
	db := middlewares.NewDB()
	orders := []models.Order{}
	db.Where("source = 'be'").Find(&orders)
	// fmt.Println(orders)
	for i := range orders {

		mapOrder := mapping.MapOrder(&models.MerchantOrder{
			Source:       "be",
			LongOrderID:  orders[i].OrderID,
			ShortOrderID: orders[i].ShortOrderID,
			DataInDetail: orders[i].Data.Data["raw"],
		})
		orders[i].DataMapping.Data = mapOrder.DataMapping.Data
		db.Save(&orders[i])
	}

}

func main() {
	Test1()
}
