package payment

import (
	"testing"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"
)

func TestValidateVNPayCredentials(t *testing.T) {
	// Test with valid test credentials
	validToken := models.VNPayToken{
		MerchantID:      "0318864354EV",
		TerminalID:      "EVETESTT",
		QRSecretKey:     "vnpay@MERCHANT",
		TransSecretKey:  "vnpay@123@langhaHangLa",
		RefundSecretKey: "vnpayRefund",
	}

	// Test validation with valid credentials
	err := ValidateVNPayCredentials(validToken)
	if err != nil {
		t.Logf("VNPay validation with test credentials: %v", err)
		// Note: This might fail in test environment, but the function should exist and be callable
	} else {
		t.Log("VNPay validation passed with test credentials")
	}

	// Test with invalid credentials (empty values)
	invalidToken := models.VNPayToken{
		MerchantID:      "",
		TerminalID:      "",
		QRSecretKey:     "",
		TransSecretKey:  "",
		RefundSecretKey: "",
	}

	err = ValidateVNPayCredentials(invalidToken)
	if err == nil {
		t.Error("Expected validation to fail with empty credentials")
	} else {
		t.Logf("VNPay validation correctly failed with empty credentials: %v", err)
	}

	// Test with invalid QR secret key
	invalidQRToken := models.VNPayToken{
		MerchantID:      "0318864354EV",
		TerminalID:      "EVETESTT",
		QRSecretKey:     "invalid_qr_secret",
		TransSecretKey:  "vnpay@123@langhaHangLa",
		RefundSecretKey: "vnpayRefund",
	}

	err = ValidateVNPayCredentials(invalidQRToken)
	if err == nil {
		t.Error("Expected validation to fail with invalid QR secret key")
	} else {
		t.Logf("VNPay validation correctly failed with invalid QR secret: %v", err)
	}

	// Test with invalid transaction secret key
	invalidTransToken := models.VNPayToken{
		MerchantID:      "0318864354EV",
		TerminalID:      "EVETESTT",
		QRSecretKey:     "vnpay@MERCHANT",
		TransSecretKey:  "invalid_trans_secret",
		RefundSecretKey: "vnpayRefund",
	}

	err = ValidateVNPayCredentials(invalidTransToken)
	if err != nil {
		t.Logf("VNPay validation correctly detected invalid transaction secret: %v", err)
	}

	// Test with invalid refund secret key
	invalidRefundToken := models.VNPayToken{
		MerchantID:      "0318864354EV",
		TerminalID:      "EVETESTT",
		QRSecretKey:     "vnpay@MERCHANT",
		TransSecretKey:  "vnpay@123@langhaHangLa",
		RefundSecretKey: "invalid_refund_secret",
	}

	err = ValidateVNPayCredentials(invalidRefundToken)
	if err != nil {
		t.Logf("VNPay validation correctly detected invalid refund secret: %v", err)
	}
}

func TestVNPayCredentialsValidationFunction(t *testing.T) {
	// Test that the function exists and can be called
	token := models.VNPayToken{
		MerchantID:      "test",
		TerminalID:      "test",
		QRSecretKey:     "test",
		TransSecretKey:  "test",
		RefundSecretKey: "test",
	}

	// This should not panic and should return an error (since credentials are invalid)
	err := ValidateVNPayCredentials(token)
	if err == nil {
		t.Error("Expected validation to fail with test credentials")
	}

	t.Log("ValidateVNPayCredentials function is working correctly")
}
