# VNPay QRCode Payment Integration

This document describes the VNPay QRCode payment integration implementation for the Nexpos backend system.

## Overview

The VNPay QRCode implementation provides a complete payment solution using VNPay's QRCode API, including:

- **QRCode Payment Creation**: Generate QR codes for payments
- **Transaction Status Checking**: Check payment status in real-time
- **Refund Processing**: Handle partial and full refunds
- **Webhook Processing**: Handle payment callbacks

## Test Credentials

VNPay has provided the following test credentials:

### QR Information
- **merchantCode**: `0318864354EV`
- **merchantName**: `EVERY TECH`
- **merchantType**: `4513`
- **terminalId**: `EVETESTT`

### API Information

#### Gen QR API
- **URL**: `https://doitac-tran.vnpaytest.vn/QRCreateAPIRestV2/rest/CreateQrcodeApi/createQrcode`
- **secretKey**: `vnpay@MERCHANT`
- **appID**: `MERCHANT`

#### Check Transaction API
- **URL**: `https://doitac-tran.vnpaytest.vn/CheckTransaction/rest/api/CheckTrans`
- **secretKey**: `vnpay@123@langhaHangLa`

#### Refund API
- **URL**: `https://doitac-tran.vnpaytest.vn/mms/refund`
- **secretKey**: `vnpayRefund`

### Test Tool Information
- **Account**: `**********`
- **Password**: `aaaa1111`
- **OTP**: `123456`

### Mobile Testing
- **iOS TestFlight**: https://testflight.apple.com/join/jNWVzY6E
- **Android**: Contact VNPay to be added to App Tester

## Implementation Details

### 1. QRCode Payment Creation

```go
// Create VNPay client
vnpayClient := NewVNPayClient()

// Create payment request
paymentRequest := &PaymentRequest{
    OrderID:        "ORDER_001",
    Amount:         100000, // 100,000 VND
    Currency:       "VND",
    Description:    "Payment for order ORDER_001",
    ClientCallback: "https://your-app.com/payment/callback",
    ServerCallback: "https://your-app.com/payment/webhook",
    PaymentMethod:  "VNPAY_QR",
    Language:       "vn",
}

// Create token with QR creation credentials
token := &models.Token{
    SiteID:   "0318864354EV",
    Password: "vnpay@MERCHANT",
}

// Create payment
response, err := vnpayClient.CreatePaymentLink(token, paymentRequest)
```

### 2. Transaction Status Checking

```go
// Create token with check transaction credentials
checkToken := &models.Token{
    SiteID:   "0318864354EV",
    Password: "vnpay@123@langhaHangLa",
}

// Check transaction status
status, err := vnpayClient.GetTransactionStatus(checkToken, transactionID)
```

### 3. Refund Processing

```go
// Create refund request
refundRequest := &RefundRequest{
    TransactionID: "TRANSACTION_ID",
    Amount:        50000, // Partial refund
    Reason:        "Customer requested refund",
}

// Create token with refund credentials
refundToken := &models.Token{
    SiteID:   "0318864354EV",
    Password: "vnpayRefund",
}

// Process refund
refundResponse, err := vnpayClient.Refund(refundToken, refundRequest)
```

### 4. Webhook Processing

```go
// Process callback data from VNPay
callbackData := map[string]interface{}{
    "vnp_Amount":       "********",
    "vnp_BankCode":     "VNPAYQR",
    "vnp_OrderInfo":    "Payment for order ORDER_001",
    "vnp_ResponseCode": "00",
    "vnp_TmnCode":      "0318864354EV",
    "vnp_TxnRef":       transactionID,
    // ... other callback parameters
}

callbackResponse, err := vnpayClient.ProcessCallback(callbackData)
```

## API Structures

### Request Structures

#### VNPayQRCreateRequest
```go
type VNPayQRCreateRequest struct {
    MerchantCode string `json:"merchantCode"`
    MerchantName string `json:"merchantName"`
    MerchantType string `json:"merchantType"`
    TerminalId   string `json:"terminalId"`
    OrderId      string `json:"orderId"`
    Amount       int64  `json:"amount"`
    OrderInfo    string `json:"orderInfo"`
    AppId        string `json:"appId"`
    Signature    string `json:"signature"`
}
```

#### VNPayCheckTransRequest
```go
type VNPayCheckTransRequest struct {
    MerchantCode string `json:"merchantCode"`
    TerminalId   string `json:"terminalId"`
    OrderId      string `json:"orderId"`
    Signature    string `json:"signature"`
}
```

#### VNPayRefundRequest
```go
type VNPayRefundRequest struct {
    MerchantCode    string `json:"merchantCode"`
    TerminalId      string `json:"terminalId"`
    OrderId         string `json:"orderId"`
    TransactionNo   string `json:"transactionNo"`
    RefundAmount    int64  `json:"refundAmount"`
    RefundOrderId   string `json:"refundOrderId"`
    RefundOrderInfo string `json:"refundOrderInfo"`
    Signature       string `json:"signature"`
}
```

### Response Structures

#### VNPayQRCreateResponse
```go
type VNPayQRCreateResponse struct {
    Code    string `json:"code"`
    Message string `json:"message"`
    Data    struct {
        QRCode    string `json:"qrCode"`
        QRDataURL string `json:"qrDataURL"`
    } `json:"data"`
}
```

## Signature Generation

Each API uses different signature generation methods:

### QR Creation API
- **Method**: MD5
- **Format**: `merchantCode|merchantName|merchantType|terminalId|orderId|amount|orderInfo|appId` + secretKey

### Check Transaction API
- **Method**: SHA256
- **Format**: `merchantCode|terminalId|orderId` + secretKey

### Refund API
- **Method**: SHA256
- **Format**: `merchantCode|terminalId|orderId|transactionNo|refundAmount|refundOrderId|refundOrderInfo` + secretKey

## Status Mapping

VNPay response codes are mapped to standard statuses:

- `00`: `COMPLETED` - Transaction successful
- `01`, `02`, `03`: `PENDING` - Transaction pending
- `04`-`09`: `FAILED` - Transaction failed
- Others: `PENDING` - Default pending status

## Error Handling

The implementation includes comprehensive error handling:

- Network errors during API calls
- JSON parsing errors
- Invalid response codes
- Missing required fields
- Signature validation failures

## Testing

Use the provided example file `vnpay_example.go` to test the implementation:

```go
// Run all examples
ExampleUsage()

// Or run individual examples
VNPayQRCodeExample()
VNPayTestCredentials()
```

## Security Considerations

1. **Secret Key Management**: Store secret keys securely and never expose them in client-side code
2. **Signature Validation**: Always validate signatures for webhook callbacks
3. **HTTPS Only**: Use HTTPS for all API communications
4. **Input Validation**: Validate all input parameters before processing
5. **Rate Limiting**: Implement rate limiting for API calls

## Production Deployment

When moving to production:

1. Replace test URLs with production URLs
2. Update merchant credentials with production values
3. Configure proper webhook endpoints
4. Implement proper logging and monitoring
5. Set up error alerting

## Support

For technical support and integration questions, contact VNPay support team with your merchant credentials and integration details.
