package middlewares

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"os"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"
	"github.com/spf13/cast"
	"github.com/thoas/go-funk"
	"gorm.io/gorm"
)

// Claims represents the JWT claims structure
type Claims struct {
	Username string `json:"username"`
	jwt.StandardClaims
}

// UserAgent represents user agent information
type UserAgent struct {
	Browser  string `json:"browser"`
	Version  string `json:"version"`
	OS       string `json:"os"`
	Platform string `json:"platform"`
	Source   string `json:"source"`
}

// IsUserActive checks if a user account is active and not expired
func IsUserActive(user *models.User) bool {
	if user == nil {
		return false
	}
	if user.Status != models.UserStatusActive {
		return false
	}
	if user.ExpiredAt != nil && user.ExpiredAt.Before(time.Now()) {
		return false
	}
	return true
}

// RequireAuth creates a middleware that checks for required permissions
func RequireAuth(permissions ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		db := GetDB(c)
		if db == nil {
			return
		}

		// Get token from cookie or header
		token := getToken(c)
		if token == "" {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{
				"success": false,
				"error":   "Access denied. No token provided.",
			})
			return
		}

		// Check for internal API access
		if funk.ContainsString(permissions, "internal") {
			if token == os.Getenv("INTERNAL_API_KEY") {
				c.Next()
				return
			}
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{
				"success": false,
				"error":   "Invalid token.",
			})
			return
		}

		// Verify JWT token with revocation check
		claims, err := verifyTokenWithRevocationCheck(c, token)
		if err != nil {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{
				"success": false,
				"error":   "Invalid token.",
			})
			return
		}

		// Get user from database
		var user models.User
		if err := db.Where("username = ?", claims.Username).First(&user).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{
					"success": false,
					"error":   "User not found.",
				})
				return
			}
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   "Database error.",
			})
			return
		}

		// Check if user is active
		if !IsUserActive(&user) {
			// If user is expired, update their status
			if user.ExpiredAt != nil && user.ExpiredAt.Before(time.Now()) {
				user.Status = models.UserStatusInactive
				db.Save(&user)
			}
			c.AbortWithStatusJSON(http.StatusForbidden, gin.H{
				"success": false,
				"error":   "Account is not active or has expired",
			})
			return
		}

		// Set context values
		c.Set("user", &user)
		c.Next()
	}
}

func GetUser(c *gin.Context) *models.User {
	user, exists := c.Get("user")
	if !exists {
		return nil
	}
	return user.(*models.User)
}

// func GetRole(c *gin.Context) *models.Role {
// 	role, exists := c.Get("role")
// 	if !exists {
// 		return nil
// 	}
// 	return role.(*models.Role)
// }

// GetBrandPermissions returns a map of brand IDs to staff roles for the current user
// func GetBrandPermissions(c *gin.Context) map[string]models.StaffRole {
// 	db := GetDB(c)
// 	user := GetUser(c)
// 	if db == nil || user == nil {
// 		return nil
// 	}

// 	// Create a map to store brand ID to role mapping
// 	brandRoles := make(map[string]models.StaffRole)

// 	// Get all brand staff records for the current user
// 	var brandStaffs []models.BrandStaff
// 	if err := db.Where("user_id = ?", user.ID).Find(&brandStaffs).Error; err != nil {
// 		return nil
// 	}

// 	// Populate the map with brand ID to role mapping
// 	for _, staff := range brandStaffs {
// 		brandRoles[staff.BrandID] = staff.Role
// 	}

// 	return brandRoles
// }

// GetHubPermissions returns a map of hub IDs to staff roles for the current user
// func GetHubPermissions(c *gin.Context) map[string]models.StaffRole {
// 	db := GetDB(c)
// 	user := GetUser(c)
// 	if db == nil || user == nil {
// 		return nil
// 	}

// 	// Create a map to store hub ID to role mapping
// 	hubRoles := make(map[string]models.StaffRole)

// 	// Get all hub staff records for the current user
// 	var hubStaffs []models.HubStaff
// 	if err := db.Where("user_id = ?", user.ID).Find(&hubStaffs).Error; err != nil {
// 		return nil
// 	}

// 	// Populate the map with hub ID to role mapping
// 	for _, staff := range hubStaffs {
// 		hubRoles[staff.HubID] = staff.Role
// 	}

// 	return hubRoles
// }

// GetUserAccessibleSites returns a list of site IDs that the current user has access to
// based on their brand and hub assignments
func GetUserAccessibleSites(c *gin.Context) []string {
	db := GetDB(c)
	user := GetUser(c)
	if db == nil || user == nil {
		return nil
	}

	// Get brand and hub IDs directly from user model
	brandIDs := user.Brands
	hubIDs := user.Hubs

	// If user has no assigned brands or hubs, return empty result
	if len(brandIDs) == 0 && len(hubIDs) == 0 {
		return nil
	}

	// Get all sites the user has access to
	var accessibleSiteIDs []string

	// Get sites from brand assignments
	if len(brandIDs) > 0 {
		var brandSites []models.Site
		if err := db.Where("brand_id IN (?)", brandIDs).Find(&brandSites).Error; err == nil {
			for _, site := range brandSites {
				accessibleSiteIDs = append(accessibleSiteIDs, site.ID)
			}
		}
	}

	// Get sites from hub assignments
	if len(hubIDs) > 0 {
		var hubSites []models.Site
		if err := db.Where("hub_id IN (?)", hubIDs).Find(&hubSites).Error; err == nil {
			for _, site := range hubSites {
				accessibleSiteIDs = append(accessibleSiteIDs, site.ID)
			}
		}
	}

	// Remove duplicates using go-funk
	accessibleSiteIDs = funk.UniqString(accessibleSiteIDs)

	return accessibleSiteIDs
}

// Helper functions

func getToken(c *gin.Context) string {
	token, err := c.Cookie(os.Getenv("COOKIE_TOKEN_KEY"))
	if err == nil {
		return token
	}
	return c.GetHeader("x-access-token")
}

func verifyToken(tokenString string) (*Claims, error) {
	claims := &Claims{}
	token, err := jwt.ParseWithClaims(tokenString, claims, func(token *jwt.Token) (any, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return []byte(os.Getenv("JWT_SECRET")), nil
	})

	if err != nil {
		return nil, err
	}
	if !token.Valid {
		return nil, errors.New("invalid token")
	}
	return claims, nil
}

// InternalServiceAuth creates a middleware that checks for internal service authentication
func InternalServiceAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		token := c.GetHeader("x-access-token")
		if token != os.Getenv("INTERNAL_API_KEY") {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{
				"success": false,
				"error":   "Invalid token.",
			})
			return
		}

		c.Next()
	}
}

// Authorize creates a middleware that performs basic user authentication
func Authorize() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get database connection from context
		db := GetDB(c)
		if db == nil {
			c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{
				"success": false,
				"error":   "Database connection not available.",
			})
			return
		}

		// Get token from cookie or header
		token := getToken(c)
		if token == "" {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{
				"success": false,
				"error":   "Access denied. No token provided.",
			})
			return
		}

		// Verify JWT token with revocation check
		claims, err := verifyTokenWithRevocationCheck(c, token)
		if err != nil {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{
				"success": false,
				"error":   "Invalid token: " + err.Error(),
			})
			return
		}

		// Get user from database using the username from claims
		var user models.User
		if err := db.Where("username = ?", claims.Username).First(&user).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{
					"success": false,
					"error":   "User not found.",
				})
				return
			}
			c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{
				"success": false,
				"error":   "Database error: " + err.Error(),
			})
			return
		}

		// Check if user is active using the same check as RequireAuth
		if !IsUserActive(&user) {
			// If user is expired, update their status
			if user.ExpiredAt != nil && user.ExpiredAt.Before(time.Now()) {
				user.Status = models.UserStatusInactive
				db.Save(&user)
			}
			c.AbortWithStatusJSON(http.StatusForbidden, gin.H{
				"success": false,
				"error":   "Account is not active or has expired",
			})
			return
		}

		// Set basic user information in context
		c.Set("user", &user)

		c.Next()
	}
}

// AuthorizeWithPermission creates a middleware that validates user authentication,
// role permissions, and subscription status
func AuthorizeWithPermission(permission models.Permission) gin.HandlerFunc {
	// Special internal permission handled separately for backward compatibility
	if permission == "internal" {
		return func(c *gin.Context) {
			token := getToken(c)
			if token == os.Getenv("INTERNAL_API_KEY") {
				c.Next()
				return
			}
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{
				"success": false,
				"error":   "Invalid internal API token.",
			})
		}
	}

	return func(c *gin.Context) {
		// Run base authorization first
		auth := Authorize()
		auth(c)

		// Check if the request was aborted by the base authorization
		if c.IsAborted() {
			return
		}

		// Get user from context
		user := GetUser(c)
		if user == nil {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{
				"success": false,
				"error":   "User not found in context.",
			})
			return
		}

		db := GetDB(c)
		if db == nil {
			c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{
				"success": false,
				"error":   "Database connection not available.",
			})
			return
		}

		// Get user's role information
		role, found := models.GetRoleByID(user.Role)
		if !found {
			c.AbortWithStatusJSON(http.StatusForbidden, gin.H{
				"success": false,
				"error":   "Invalid role assigned to user.",
			})
			return
		}

		// Check if user's role has the required permission
		hasPermission := false
		for _, p := range role.Permissions {
			if p == permission {
				hasPermission = true
				break
			}
		}
		if !hasPermission {
			c.AbortWithStatusJSON(http.StatusForbidden, gin.H{
				"success": false,
				"error":   fmt.Sprintf("Permission denied. Required: %s", permission),
			})
			return
		}

		// Get user's active subscription and plan
		subscription, _, err := GetUserActiveSubscription(db, user)
		if err != nil {
			c.AbortWithStatusJSON(http.StatusPaymentRequired, gin.H{
				"success": false,
				"error":   err.Error(),
			})
			return
		}

		c.Set("subscription", subscription)
		c.Next()
	}
}

// verifyTokenWithRevocationCheck performs comprehensive token verification including password change revocation
func verifyTokenWithRevocationCheck(c *gin.Context, tokenString string) (*Claims, error) {
	claims := &Claims{}
	token, err := jwt.ParseWithClaims(tokenString, claims, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return []byte(os.Getenv("JWT_SECRET")), nil
	})

	if err != nil {
		return nil, err
	}
	if !token.Valid {
		return nil, errors.New("invalid token")
	}

	// Check if token was revoked due to password change
	if IsTokenRevokedByPasswordChange(c, claims.Username, token) {
		return nil, errors.New("token has been revoked due to password change")
	}

	return claims, nil
}

// IsTokenRevokedByPasswordChange checks if a token was issued before the user's password was changed
func IsTokenRevokedByPasswordChange(c *gin.Context, username string, token *jwt.Token) bool {
	redisClient := GetRedis(c)
	if redisClient == nil {
		// If Redis is not available, assume token is valid
		return false
	}

	ctx := context.Background()

	// Get the password change timestamp from Redis
	passwordChangeKey := fmt.Sprintf("user_password_changed:%s", username)
	passwordChangeTimestampStr, err := redisClient.Get(ctx, passwordChangeKey).Result()
	if err != nil {
		// If no password change timestamp exists, token is valid
		return false
	}

	// Parse the password change timestamp
	passwordChangeTimestamp, err := strconv.ParseInt(passwordChangeTimestampStr, 10, 64)
	if err != nil {
		// If we can't parse the timestamp, assume token is valid
		return false
	}

	if claims, ok := token.Claims.(jwt.MapClaims); ok {
		return cast.ToInt64(claims["iat"]) < passwordChangeTimestamp
	}

	// If we can't determine the token's issued time, assume it's valid
	return false
}
