package ptr

import "time"

// String returns a pointer of a given string.
func String(s string) *string {
	return &s
}

// StringValue returns the value of a string pointer or empty string if nil.
func StringValue(s *string) string {
	if s == nil {
		return ""
	}
	return *s
}

// Int returns a pointer of a given int.
func Int(i int) *int {
	return &i
}

// IntValue returns the value of an int pointer or 0 if nil.
func IntValue(i *int) int {
	if i == nil {
		return 0
	}
	return *i
}

// Int8 returns a pointer of a given int8.
func Int8(i int8) *int8 {
	return &i
}

// Int8Value returns the value of an int8 pointer or 0 if nil.
func Int8Value(i *int8) int8 {
	if i == nil {
		return 0
	}
	return *i
}

// Int16 returns a pointer of a given int16.
func Int16(i int16) *int16 {
	return &i
}

// Int16Value returns the value of an int16 pointer or 0 if nil.
func Int16Value(i *int16) int16 {
	if i == nil {
		return 0
	}
	return *i
}

// Int32 returns a pointer of a given int32.
func Int32(i int32) *int32 {
	return &i
}

// Int32Value returns the value of an int32 pointer or 0 if nil.
func Int32Value(i *int32) int32 {
	if i == nil {
		return 0
	}
	return *i
}

// Int64 returns a pointer of a given int64.
func Int64(i int64) *int64 {
	return &i
}

// Int64Value returns the value of an int64 pointer or 0 if nil.
func Int64Value(i *int64) int64 {
	if i == nil {
		return 0
	}
	return *i
}

// Uint returns a pointer of a given uint.
func Uint(u uint) *uint {
	return &u
}

// UintValue returns the value of a uint pointer or 0 if nil.
func UintValue(u *uint) uint {
	if u == nil {
		return 0
	}
	return *u
}

// Uint8 returns a pointer of a given uint8.
func Uint8(u uint8) *uint8 {
	return &u
}

// Uint8Value returns the value of a uint8 pointer or 0 if nil.
func Uint8Value(u *uint8) uint8 {
	if u == nil {
		return 0
	}
	return *u
}

// Uint16 returns a pointer of a given uint16.
func Uint16(u uint16) *uint16 {
	return &u
}

// Uint16Value returns the value of a uint16 pointer or 0 if nil.
func Uint16Value(u *uint16) uint16 {
	if u == nil {
		return 0
	}
	return *u
}

// Uint32 returns a pointer of a given uint32.
func Uint32(u uint32) *uint32 {
	return &u
}

// Uint32Value returns the value of a uint32 pointer or 0 if nil.
func Uint32Value(u *uint32) uint32 {
	if u == nil {
		return 0
	}
	return *u
}

// Uint64 returns a pointer of a given uint64.
func Uint64(u uint64) *uint64 {
	return &u
}

// Uint64Value returns the value of a uint64 pointer or 0 if nil.
func Uint64Value(u *uint64) uint64 {
	if u == nil {
		return 0
	}
	return *u
}

// Float32 returns a pointer of a given float32.
func Float32(f float32) *float32 {
	return &f
}

// Float32Value returns the value of a float32 pointer or 0.0 if nil.
func Float32Value(f *float32) float32 {
	if f == nil {
		return 0.0
	}
	return *f
}

// Float64 returns a pointer of a given float64.
func Float64(f float64) *float64 {
	return &f
}

// Float64Value returns the value of a float64 pointer or 0.0 if nil.
func Float64Value(f *float64) float64 {
	if f == nil {
		return 0.0
	}
	return *f
}

// Bool returns a pointer of a given bool.
func Bool(b bool) *bool {
	return &b
}

// BoolValue returns the value of a bool pointer or false if nil.
func BoolValue(b *bool) bool {
	if b == nil {
		return false
	}
	return *b
}

// Time returns a pointer of a given time.Time.
func Time(t time.Time) *time.Time {
	return &t
}

// TimeValue returns the value of a time.Time pointer or zero time if nil.
func TimeValue(t *time.Time) time.Time {
	if t == nil {
		return time.Time{}
	}
	return *t
}
