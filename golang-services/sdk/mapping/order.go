package mapping

import (
	"strings"
	"time"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/utils"

	"github.com/samber/lo"
	"github.com/spf13/cast"
	"github.com/tidwall/gjson"
)

var CANCEL_REASON_MESSAGE = map[string]string{
	"merchant": "Hủy bởi quán",
	"system":   "Hủy bởi hệ thống",
	"user":     "Hủy bởi khách",
	"driver":   "Hủy bởi tài xế",
}

func grabStrToNumber(str string) float64 {
	return cast.ToFloat64(strings.ReplaceAll(str, ".", ""))
}

// MapOrder maps a MerchantOrder to an Order with DataMapping
func MapOrder(o *models.MerchantOrder) *models.Order {
	orderJ := utils.StructToJSON(o.DataInDetail)

	// Create the base order with only the fields from the SQL query
	order := &models.Order{
		OrderID:      o.LongOrderID,
		ShortOrderID: o.ShortOrderID,
		Source:       o.Source,
		Data:         models.JSONField[map[string]any]{Data: map[string]any{"raw": o.DataInDetail}},
	}

	// Create data mapping based on the source
	var dataMapping models.DataMapping

	if o.Source == "shopee_food" || o.Source == "shopee_fresh" {
		dataMapping = mapShopeeOrder(o, orderJ)
	} else if o.Source == "grab" || o.Source == "grab_mart" {
		dataMapping = mapGrabOrder(o, orderJ)
	} else if o.Source == "be" {
		dataMapping = mapBeOrder(o, orderJ)
	} else if o.Source == "local" {
		dataMapping = mapLocalOrder(o, orderJ)
	} else if o.Source == "shopee_ecom" {
		dataMapping = mapShopeeEcomOrder(o, orderJ)
	} else {
		// Default empty mapping
		dataMapping = models.DataMapping{
			ID:      o.LongOrderID,
			OrderID: o.ShortOrderID,
			Source:  o.Source,
		}
	}

	// Set the DataMapping field
	order.DataMapping = models.JSONField[models.DataMapping]{Data: dataMapping}

	return order
}

// mapShopeeOrder maps a Shopee order to DataMapping
func mapShopeeOrder(o *models.MerchantOrder, orderJ gjson.Result) models.DataMapping {
	// Create data mapping
	dataMapping := models.DataMapping{
		ID:               o.LongOrderID,
		OrderID:          o.ShortOrderID,
		Source:           o.Source,
		OrderTime:        time.Unix(orderJ.Get("order_time").Int(), 0).Format(time.RFC3339),
		PickTime:         time.Unix(orderJ.Get("actual_pick_time").Int(), 0).Format(time.RFC3339),
		DeliveryTime:     time.Unix(orderJ.Get("delivery_time_unix").Int(), 0).Format(time.RFC3339),
		DeliveryTimeUnix: orderJ.Get("delivery_time_unix").Int(),
		OrderTimeSort:    orderJ.Get("order_time").Int(),
		DriverName:       orderJ.Get("assignee.name").String(),
		DriverPhone:      orderJ.Get("assignee.phone").String(),
		CustomerName:     utils.OneOf(orderJ.Get("deliver_address.contact_name").String(), orderJ.Get("order_user.name").String()),
		CustomerPhone:    orderJ.Get("order_user.phone").String(),
		CustomerAddress:  orderJ.Get("deliver_address.address").String(),
		Dishes:           []models.OrderDish{},
		Total:            orderJ.Get("order_value_amount").Float(),
		Commission:       orderJ.Get("commission.amount").Float(),
		TotalForBiz:      orderJ.Get("total_value_amount").Float(),
		Note:             utils.ShortIf(orderJ.Get("is_remove_plastic").Bool(), "Không cần dùng cụ ăn uống nhựa", "Cần dụng cụ ăn uống") + orderJ.Get("notes.order_note").String(),
		CancelReason:     "",
		Coupons:          []models.OrderCoupon{},
		Payments: []models.Payment{
			{
				Method: "SHOPEE",
				Total:  orderJ.Get("total_value_amount").Float(),
				Status: "COMPLETED",
				Note:   "Thanh toán qua Shopee",
			},
		},
		CustomerData: models.CustomerData{},
		FinanceData:  models.FinanceData{},
		Raw:          o,
	}

	// Process dishes
	orderJ.Get("order_items").ForEach(func(_, item gjson.Result) bool {
		dish := models.OrderDish{
			Name:          item.Get("dish.name").String(),
			Description:   item.Get("dish.description").String(),
			Quantity:      item.Get("quantity").Int(),
			Price:         item.Get("original_price").Float() * item.Get("quantity").Float(),
			Discount:      (item.Get("original_price").Float() - item.Get("discount_price").Float()) * item.Get("quantity").Float(),
			DiscountPrice: item.Get("discount_price").Float() * item.Get("quantity").Float(),
			Note:          item.Get("note").String(),
			Options:       [][]models.DishOption{},
		}

		// Process options
		if item.Get("options_groups").Exists() {
			item.Get("options_groups").ForEach(func(_, optGroup gjson.Result) bool {
				var options []models.DishOption
				optGroup.Get("options").ForEach(func(_, opt gjson.Result) bool {
					if opt.Get("quantity").Float() > 0 {
						options = append(options, models.DishOption{
							Name:       optGroup.Get("name").String() + " " + opt.Get("name").String(),
							Quantity:   opt.Get("quantity").Int(),
							OptionName: optGroup.Get("name").String(),
							OptionItem: opt.Get("name").String(),
						})
					}
					return true
				})
				if len(options) > 0 {
					dish.Options = append(dish.Options, options)
				}
				return true
			})
		}

		dataMapping.Dishes = append(dataMapping.Dishes, dish)
		return true
	})

	// Coupons
	orderJ.Get("merchant_discounts").ForEach(func(_, value gjson.Result) bool {
		dataMapping.Coupons = append(dataMapping.Coupons, models.OrderCoupon{
			Name:  value.Get("name").String(),
			Code:  value.Get("code").String(),
			Total: value.Get("amount").Float(),
		})
		if value.Get("type").Int() == 1 {
			dataMapping.FinanceData.CoFundPromotionPrice += value.Get("amount").Float()
		}
		return true
	})

	// CustomerData
	orderJ.Get("order_items").ForEach(func(_, value gjson.Result) bool {
		dataMapping.CustomerData.OriginalPrice += value.Get("original_price").Float() * value.Get("quantity").Float()
		dataMapping.CustomerData.SellPrice += value.Get("discount_price").Float() * value.Get("quantity").Float()
		return true
	})
	dataMapping.CustomerData.ShipmentFee = orderJ.Get("customer_bill.shipping_fee").Float()
	dataMapping.CustomerData.OrderDiscount = orderJ.Get("customer_bill.total_discount").Float() - orderJ.Get("customer_bill.item_discount").Float()
	dataMapping.CustomerData.ShipmentDiscount = 0
	dataMapping.CustomerData.AdditionalIncome = 0
	dataMapping.CustomerData.TotalPaid = orderJ.Get("customer_bill.total_amount").Float()

	// FinanceData
	dataMapping.FinanceData.OriginalPrice = dataMapping.CustomerData.OriginalPrice
	dataMapping.FinanceData.SellPrice = dataMapping.CustomerData.SellPrice
	// dataMapping.FinanceData.CoFundPromotionPrice set in coupons processing
	dataMapping.FinanceData.Commission = dataMapping.Commission
	dataMapping.FinanceData.OtherFee = 0
	dataMapping.FinanceData.TotalShipment = 0
	dataMapping.FinanceData.NetReceived = orderJ.Get("total_value_amount").Float()
	dataMapping.FinanceData.AdditionalIncome = 0
	dataMapping.FinanceData.RealReceived = orderJ.Get("total_value_amount").Float()

	dataMapping.FinanceData.OtherPromotionPrice = lo.SumBy(dataMapping.Dishes, func(dish models.OrderDish) float64 {
		return dish.Discount
	})
	dataMapping.FinanceData.TotalPromotionPrice = dataMapping.FinanceData.OtherPromotionPrice + dataMapping.FinanceData.CoFundPromotionPrice
	dataMapping.FinanceData.GrossReceived = dataMapping.FinanceData.OriginalPrice - dataMapping.FinanceData.TotalPromotionPrice

	if val := orderJ.Get("cancel_info.type").Int(); val > 0 {
		cancelByMap := map[int64]string{
			2: "merchant",
			3: "system",
			4: "user",
			5: "driver",
		}
		if val, ok := cancelByMap[val]; ok {
			dataMapping.CancelBy = val
			dataMapping.CancelReason = CANCEL_REASON_MESSAGE[val]
		} else {
			dataMapping.CancelBy = "merchant"
			dataMapping.CancelReason = CANCEL_REASON_MESSAGE["merchant"]
		}
	}
	if val := orderJ.Get("cancel_info.reason").String(); val != "" {
		dataMapping.CancelReason += ", Chi tiết:" + val
	}
	dataMapping.TotalDiscount = dataMapping.Total - dataMapping.TotalForBiz - dataMapping.Commission

	return dataMapping
}

// mapGrabOrder maps a Grab order to DataMapping
func mapGrabOrder(o *models.MerchantOrder, orderJ gjson.Result) models.DataMapping {
	// Create data mapping
	dataMapping := models.DataMapping{
		ID:          o.LongOrderID,
		OrderID:     o.ShortOrderID,
		Source:      o.Source,
		Dishes:      []models.OrderDish{},
		StockDishes: []models.StockDish{},
		Coupons:     []models.OrderCoupon{},
		Raw:         o.DataInDetail,
	}

	// Process times
	displayedAt := orderJ.Get("times.displayedAt").String()
	createdAt := orderJ.Get("times.createdAt").String()
	readyAt := orderJ.Get("times.readyAt").String()
	deliveredAt := orderJ.Get("times.deliveredAt").String()

	orderTime := utils.OneOf(displayedAt, createdAt)
	pickTime := utils.OneOf(readyAt, deliveredAt)

	if orderTime != "" {
		t, _ := time.Parse(time.RFC3339, orderTime)
		dataMapping.OrderTime = t.Format(time.RFC3339)
		dataMapping.OrderTimeSort = t.Unix()
	}
	if pickTime != "" {
		t, _ := time.Parse(time.RFC3339, pickTime)
		dataMapping.PickTime = t.Format(time.RFC3339)
	}
	if deliveredAt != "" {
		t, _ := time.Parse(time.RFC3339, deliveredAt)
		dataMapping.DeliveryTime = t.Format(time.RFC3339)
		dataMapping.DeliveryTimeUnix = t.Unix()
	}

	// Process driver and customer info
	dataMapping.DriverName = orderJ.Get("driver.name").String()
	dataMapping.DriverPhone = orderJ.Get("driver.mobileNumber").String()
	dataMapping.CustomerName = orderJ.Get("eater.name").String()
	dataMapping.CustomerPhone = orderJ.Get("eater.mobileNumber").String()
	dataMapping.CustomerAddress = orderJ.Get("eater.address.keywords").String()

	// Process cutlery preference
	if orderJ.Get("cutlery").Int() == 1 {
		dataMapping.Note = "Cần dụng cụ ăn uống"
	} else {
		dataMapping.Note = "Không cần dùng cụ ăn uống nhựa"
	}

	// Process cancellation
	cancelBy := orderJ.Get("cancelBy").String()
	cancelMsg := orderJ.Get("cancelledMsg").String()

	cancelByMap := map[string]string{
		"CANCELLED_OPERATOR":  "system",
		"CANCELLED_MAX":       "merchant",
		"CANCELLED_PASSENGER": "user",
		"CANCELLED_DRIVER":    "driver",
	}

	if val, ok := cancelByMap[cancelBy]; ok {
		dataMapping.CancelBy = val
		dataMapping.CancelReason = CANCEL_REASON_MESSAGE[val]
	} else if cancelBy != "" {
		dataMapping.CancelBy = "merchant"
		dataMapping.CancelReason = CANCEL_REASON_MESSAGE["merchant"]
	}

	if cancelMsg != "" {
		if dataMapping.CancelReason != "" {
			dataMapping.CancelReason += ", Chi tiết: " + cancelMsg
		} else {
			dataMapping.CancelReason = cancelMsg
		}
	}

	// Replace dots in the string and convert to float64
	subTotal := grabStrToNumber(orderJ.Get("fare.subTotalDisplay").String())
	total := grabStrToNumber(orderJ.Get("fare.totalDisplay").String())
	revampedSubtotal := grabStrToNumber(orderJ.Get("fare.revampedSubtotalDisplay").String())
	deliveryFee := grabStrToNumber(orderJ.Get("fare.deliveryFeeDisplay").String())
	promotion := grabStrToNumber(orderJ.Get("fare.promotionDisplay").String())
	reducedPrice := grabStrToNumber(orderJ.Get("fare.reducedPriceDisplay").String())

	dataMapping.Total = subTotal
	dataMapping.TotalForBiz = total
	dataMapping.Commission = 0 // Will be set from transaction if available

	// Process dishes
	orderJ.Get("itemInfo.items").ForEach(func(_, item gjson.Result) bool {
		price := grabStrToNumber(item.Get("fare.priceDisplay").String())

		dish := models.OrderDish{
			Name:        item.Get("name").String(),
			Description: item.Get("menu_item.description").String(),
			Quantity:    item.Get("quantity").Int(),
			Price:       price,
			Discount:    0, // Will calculate below
			Note:        item.Get("comment").String(),
			Options:     [][]models.DishOption{},
		}

		// Calculate discount
		var totalDiscount float64
		item.Get("discountInfo").ForEach(func(_, discount gjson.Result) bool {
			discVal := grabStrToNumber(discount.Get("itemDiscountPriceDisplay").String())
			totalDiscount += discVal
			return true
		})
		dish.Discount = totalDiscount
		dish.DiscountPrice = dish.Price - dish.Discount

		// Process options
		if item.Get("modifierGroups").Exists() {
			item.Get("modifierGroups").ForEach(func(_, modGroup gjson.Result) bool {
				var options []models.DishOption
				modGroup.Get("modifiers").ForEach(func(_, mod gjson.Result) bool {
					qty := mod.Get("quantity").Int()
					if qty > 0 {
						options = append(options, models.DishOption{
							Name:       modGroup.Get("modifierGroupName").String() + ": " + mod.Get("modifierName").String(),
							Quantity:   qty,
							OptionName: modGroup.Get("modifierGroupName").String(),
							OptionItem: mod.Get("modifierName").String(),
						})
					}
					return true
				})
				if len(options) > 0 {
					dish.Options = append(dish.Options, options)
				}
				return true
			})
		}

		dataMapping.Dishes = append(dataMapping.Dishes, dish)
		return true
	})

	// Process order bookings (multiple orders)
	if orderJ.Get("orderBookings").Exists() && orderJ.Get("orderBookings.#").Int() > 1 {
		var orderIDs []string
		var orderDetails []string

		orderJ.Get("orderBookings").ForEach(func(_, booking gjson.Result) bool {
			shortID := booking.Get("shortOrderID").String()
			driverName := booking.Get("driver.name").String()
			driverPhone := booking.Get("driver.mobileNumber").String()

			orderIDs = append(orderIDs, shortID)
			orderDetails = append(orderDetails, shortID+" Tài xế: "+driverName+" ("+driverPhone+")")

			// Update dish notes with sub-order info
			booking.Get("items.items").ForEach(func(_, subDish gjson.Result) bool {
				subDishName := subDish.Get("name").String()
				subDishQty := subDish.Get("quantity").Float()

				for i, dish := range dataMapping.Dishes {
					if dish.Name == subDishName {
						if dish.Note != "" {
							dataMapping.Dishes[i].Note += ", "
						}
						dataMapping.Dishes[i].Note += "Có " + cast.ToString(subDishQty) + " món thuộc đơn " + shortID
						break
					}
				}
				return true
			})

			return true
		})

		dataMapping.OrderID = strings.Join(orderIDs, "_")
		if dataMapping.Note != "" {
			dataMapping.Note += ", "
		}
		dataMapping.Note += "Đơn hàng tách thành " + cast.ToString(float64(len(orderIDs))) + " đơn: " + strings.Join(orderDetails, ", ")
	}

	// Process payment
	dataMapping.Payments = []models.Payment{
		{
			Method: "GRAB",
			Total:  total,
			Status: "COMPLETED",
			Note:   "Thanh toán qua Grab",
		},
	}

	// Process coupons
	orderJ.Get("orderLevelDiscounts").ForEach(func(_, discount gjson.Result) bool {
		dataMapping.Coupons = append(dataMapping.Coupons, models.OrderCoupon{
			Name:  "Giảm giá",
			Code:  discount.Get("discountName").String(),
			Total: discount.Get("discountAmountValueInMin").Float(),
		})
		return true
	})

	// Process customer data
	dataMapping.CustomerData = models.CustomerData{
		OriginalPrice:    subTotal,
		SellPrice:        revampedSubtotal,
		ShipmentFee:      deliveryFee,
		OrderDiscount:    promotion,
		ShipmentDiscount: 0,
		AdditionalIncome: 0,
		TotalPaid:        reducedPrice,
	}

	// Calculate additional income
	dataMapping.CustomerData.AdditionalIncome = dataMapping.CustomerData.SellPrice +
		dataMapping.CustomerData.ShipmentFee -
		dataMapping.CustomerData.OrderDiscount -
		dataMapping.CustomerData.TotalPaid

	// Process finance data if transaction exists
	if orderJ.Get("transaction").Exists() {
		coFundPromotion := float64(0)
		orderJ.Get("orderLevelDiscounts").ForEach(func(_, discount gjson.Result) bool {
			if discount.Get("discountType").String() == "order" {
				coFundPromotion += discount.Get("discountAmountValueInMin").Float()
			}
			return true
		})

		otherPromotion := float64(0)
		for _, dish := range dataMapping.Dishes {
			otherPromotion += dish.Discount
		}

		totalPromotion := -1 * orderJ.Get("transaction.mex_fund_discount").Float()

		dataMapping.FinanceData = models.FinanceData{
			OriginalPrice:        orderJ.Get("transaction.order_value").Float(),
			SellPrice:            revampedSubtotal,
			CoFundPromotionPrice: coFundPromotion,
			OtherPromotionPrice:  otherPromotion,
			TotalPromotionPrice:  totalPromotion,
			GrossReceived:        orderJ.Get("transaction.net_sales").Float(),
			Commission:           -1 * orderJ.Get("transaction.gf_total_commission").Float(),
			OtherFee:             0,
			TotalShipment:        0,
			ShippingFee:          0,
			NetReceived:          orderJ.Get("transaction.net_total").Float(),
			AdditionalIncome:     0,
			RealReceived:         orderJ.Get("transaction.net_total").Float(),
		}

		// If other values weren't set correctly, recalculate
		if dataMapping.FinanceData.OtherPromotionPrice == 0 {
			dataMapping.FinanceData.OtherPromotionPrice = dataMapping.FinanceData.TotalPromotionPrice - dataMapping.FinanceData.CoFundPromotionPrice
		}

		dataMapping.Commission = dataMapping.FinanceData.Commission
	}

	dataMapping.TotalDiscount = dataMapping.Total - dataMapping.TotalForBiz - dataMapping.Commission

	return dataMapping
}

// mapBeOrder maps a Be order to DataMapping
func mapBeOrder(o *models.MerchantOrder, orderJ gjson.Result) models.DataMapping {
	// Create data mapping
	dataMapping := models.DataMapping{
		ID:          o.LongOrderID,
		OrderID:     o.ShortOrderID,
		Source:      "be",
		Dishes:      []models.OrderDish{},
		StockDishes: []models.StockDish{},
		Coupons:     []models.OrderCoupon{},
		Raw:         o.DataInDetail,
	}

	// Process times - BE times may need timezone adjustment
	orderTime := cast.ToTime(orderJ.Get("order_time").String())
	pickupTime := cast.ToTime(orderJ.Get("delivery_details.pickup_time").String())

	if pickupTime.Unix() <= 0 {
		pickupTime = orderTime
	}

	deliveryTime := cast.ToTime(orderJ.Get("to_be_delivered_at").String()).Add(-7 * time.Hour)
	if deliveryTime.Unix() <= 0 {
		deliveryTime = cast.ToTime(orderJ.Get("delivered_at").String())
	}
	if deliveryTime.Unix() <= 0 {
		deliveryTime = cast.ToTime(orderJ.Get("order_created_at").String())
	}

	dataMapping.OrderTime = orderTime.Format(time.RFC3339)
	dataMapping.OrderTimeSort = orderTime.Unix()
	dataMapping.PickTime = pickupTime.Format(time.RFC3339)
	dataMapping.DeliveryTime = deliveryTime.Format(time.RFC3339)
	dataMapping.DeliveryTimeUnix = deliveryTime.Unix()

	// Process customer and driver info
	dataMapping.DriverName = orderJ.Get("driver_name").String()
	dataMapping.DriverPhone = orderJ.Get("driver_phone_no").String()
	dataMapping.CustomerPhone = orderJ.Get("phone_no").String()
	dataMapping.CustomerName = orderJ.Get("user_name").String() + " " + orderJ.Get("user_email").String()
	dataMapping.CustomerAddress = orderJ.Get("delivery_details.drop_location_address").String()
	dataMapping.Note = orderJ.Get("delivery_note").String()

	// Process financial data
	dataMapping.Total = orderJ.Get("order_amount").Float()
	dataMapping.Commission = orderJ.Get("jugnoo_commission").Float()
	dataMapping.TotalForBiz = orderJ.Get("final_amount").Float()

	// Process cancellation
	orderStatus := orderJ.Get("order_status").Int()
	cancelReasons := map[int64][]string{
		3:  {"Hủy bởi khách", "user"},
		33: {"Hủy bởi khách: chờ quá lâu", "user"},
		9:  {"Hủy bởi quán", "merchant"},
		10: {"Hủy bởi admin", "system"},
		17: {"Hủy bởi admin", "system"},
		21: {"Hủy bởi Be: Order cancelled by system after Z time", "system"},
		25: {"Hủy bởi Be", "system"},
	}

	if reason, ok := cancelReasons[orderStatus]; ok {
		dataMapping.CancelReason = reason[0]
		dataMapping.CancelBy = reason[1]
	}

	// Process dishes
	orderJ.Get("order_items").ForEach(func(_, item gjson.Result) bool {
		dish := models.OrderDish{
			Name:          item.Get("item_name").String(),
			Description:   item.Get("item_details").String(),
			Quantity:      item.Get("item_quantity").Int(),
			Price:         item.Get("original_amount").Float(),
			Discount:      item.Get("original_amount").Float() - item.Get("item_amount").Float(),
			DiscountPrice: item.Get("item_amount").Float(),
			Note:          item.Get("item_note").String(),
			Options:       [][]models.DishOption{},
		}

		// Process customize options
		if item.Get("customize_item").Exists() {
			item.Get("customize_item").ForEach(func(_, customItem gjson.Result) bool {
				var options []models.DishOption

				customItem.Get("customize_options").ForEach(func(_, customOpt gjson.Result) bool {
					options = append(options, models.DishOption{
						Name:       customItem.Get("customize_item_name").String() + " " + customOpt.Get("customize_option_name").String(),
						Quantity:   1, // BE seems to always use quantity 1 for options
						OptionName: customItem.Get("customize_item_name").String(),
						OptionItem: customOpt.Get("customize_option_name").String(),
					})
					return true
				})

				if len(options) > 0 {
					dish.Options = append(dish.Options, options)
				}
				return true
			})
		}

		dataMapping.Dishes = append(dataMapping.Dishes, dish)
		return true
	})

	// Process coupons
	orderJ.Get("offers.food_discounts").ForEach(func(_, discount gjson.Result) bool {
		dataMapping.Coupons = append(dataMapping.Coupons, models.OrderCoupon{
			Name:  "Giảm giá",
			Code:  discount.Get("title").String(),
			Total: discount.Get("partner_discount").Float(),
		})
		return true
	})

	// Process payment
	dataMapping.Payments = []models.Payment{
		{
			Method: "BE",
			Total:  dataMapping.TotalForBiz,
			Status: "COMPLETED",
			Note:   "Thanh toán qua Be",
		},
	}

	// Process customer data
	var originalPrice, sellPrice float64
	for _, dish := range dataMapping.Dishes {
		originalPrice += dish.Price
		sellPrice += dish.DiscountPrice
	}

	dataMapping.CustomerData = models.CustomerData{
		OriginalPrice:    originalPrice,
		SellPrice:        sellPrice,
		ShipmentFee:      0, // BE doesn't provide shipping fee in the data
		OrderDiscount:    0,
		ShipmentDiscount: 0,
		AdditionalIncome: 0,
		TotalPaid:        orderJ.Get("order_billable_amount").Float(),
	}

	// Process finance data
	discount := orderJ.Get("discount").Float()
	otherPromoPrice := float64(0)
	for _, dish := range dataMapping.Dishes {
		otherPromoPrice += dish.Discount
	}

	dataMapping.FinanceData = models.FinanceData{
		OriginalPrice:        originalPrice,
		SellPrice:            sellPrice,
		CoFundPromotionPrice: discount,
		OtherPromotionPrice:  otherPromoPrice,
		TotalPromotionPrice:  discount + otherPromoPrice,
		GrossReceived:        originalPrice - (discount + otherPromoPrice),
		Commission:           dataMapping.Commission,
		OtherFee:             0,
		TotalShipment:        0,
		ShippingFee:          0,
		NetReceived:          dataMapping.TotalForBiz,
		AdditionalIncome:     0,
		RealReceived:         dataMapping.TotalForBiz,
	}

	// Calculate total discount based on original calculation
	dataMapping.TotalDiscount = dataMapping.FinanceData.TotalPromotionPrice + dataMapping.Commission

	return dataMapping
}

// mapLocalOrder maps a MerchantOrder to DataMapping for local orders
func mapLocalOrder(o *models.MerchantOrder, orderJ gjson.Result) models.DataMapping {
	// Create data mapping with basic order information
	dataMapping := models.DataMapping{
		ID:          o.LongOrderID,
		OrderID:     o.ShortOrderID,
		Source:      "local",
		Dishes:      []models.OrderDish{},
		StockDishes: []models.StockDish{},
		Coupons:     []models.OrderCoupon{},
		Raw:         o.DataInDetail,
	}

	// Process times
	orderTime := orderJ.Get("order_time").String()
	pickTime := orderJ.Get("pick_time").String()
	deliveryTime := orderJ.Get("delivery_time").String()

	// Parse time fields
	if orderTime != "" {
		dataMapping.OrderTime = orderTime
		dataMapping.OrderTimeSort = orderJ.Get("order_time_sort").Int()
	}

	if pickTime != "" {
		dataMapping.PickTime = pickTime
	}

	if deliveryTime != "" {
		dataMapping.DeliveryTime = deliveryTime
		dataMapping.DeliveryTimeUnix = orderJ.Get("delivery_time_unix").Int()
	}

	// Process customer and driver info
	dataMapping.DriverName = orderJ.Get("driver_name").String()
	dataMapping.DriverPhone = orderJ.Get("driver_phone").String()
	dataMapping.CustomerPhone = orderJ.Get("customer_phone").String()
	dataMapping.CustomerName = orderJ.Get("customer_name").String()
	dataMapping.CustomerAddress = orderJ.Get("customer_address").String()
	dataMapping.Note = orderJ.Get("note").String()

	// Process cancellation
	dataMapping.CancelReason = orderJ.Get("cancel_reason").String()
	dataMapping.CancelBy = orderJ.Get("cancel_by").String()

	// Process dishes
	orderJ.Get("dishes").ForEach(func(_, item gjson.Result) bool {
		dish := models.OrderDish{
			Name:          item.Get("name").String(),
			Description:   item.Get("description").String(),
			Quantity:      item.Get("quantity").Int(),
			Price:         item.Get("price").Float(),
			Discount:      item.Get("discount").Float(),
			DiscountPrice: item.Get("discount_price").Float(),
			Note:          item.Get("note").String(),
			Options:       [][]models.DishOption{},
		}

		// Process dish options
		item.Get("options").ForEach(func(_, option gjson.Result) bool {
			dishOption := models.DishOption{
				ID:         option.Get("id").String(),
				Name:       option.Get("name").String(),
				Quantity:   option.Get("quantity").Int(),
				OptionName: option.Get("option_name").String(),
				OptionItem: option.Get("option_item").String(),
				Price:      option.Get("option_price").Float(), // Use Price instead of OptionPrice
			}

			// Append to the first group of options - this is simplified
			// compared to the full nested structure in JavaScript
			if len(dish.Options) == 0 {
				dish.Options = append(dish.Options, []models.DishOption{})
			}
			dish.Options[0] = append(dish.Options[0], dishOption)
			return true
		})

		dataMapping.Dishes = append(dataMapping.Dishes, dish)
		return true
	})

	// Process shipment info
	dataMapping.ShipmentFee = orderJ.Get("shipment_fee").Float()
	dataMapping.TotalShipment = orderJ.Get("total_shipment").Float()
	dataMapping.ShipmentDiscount = orderJ.Get("shipment_discount").Float()

	// Process coupons
	orderJ.Get("coupons").ForEach(func(_, coupon gjson.Result) bool {
		dataMapping.Coupons = append(dataMapping.Coupons, models.OrderCoupon{
			Name:  coupon.Get("name").String(),
			Code:  coupon.Get("code").String(),
			Total: coupon.Get("total").Float(),
		})
		return true
	})

	// Process payments
	var payments []models.Payment
	orderJ.Get("payments").ForEach(func(_, payment gjson.Result) bool {
		payments = append(payments, models.Payment{
			Method: payment.Get("method").String(),
			Total:  payment.Get("total").Float(),
			Status: payment.Get("status").String(),
			Note:   payment.Get("note").String(),
		})
		return true
	})

	dataMapping.Payments = payments

	// Process stock dishes
	orderJ.Get("stock_dishes").ForEach(func(_, stockDish gjson.Result) bool {
		dataMapping.StockDishes = append(dataMapping.StockDishes, models.StockDish{
			ID:       stockDish.Get("id").String(),
			Quantity: stockDish.Get("quantity").Int(),
		})
		return true
	})

	// Process financial data
	// Calculate totals
	originalPrice := 0.0
	sellPrice := 0.0

	for _, dish := range dataMapping.Dishes {
		originalPrice += dish.Price
		sellPrice += dish.DiscountPrice * float64(dish.Quantity)
	}

	// Set total values
	dataMapping.Total = orderJ.Get("total").Float()
	if dataMapping.Total == 0 {
		dataMapping.Total = originalPrice
	}

	dataMapping.Commission = orderJ.Get("commission").Float()
	dataMapping.TotalForBiz = orderJ.Get("total_for_biz").Float()

	// Calculate total discount
	dataMapping.TotalDiscount = orderJ.Get("total_discount").Float()
	if dataMapping.TotalDiscount == 0 {
		// Calculate from available data
		otherPromotionPrice := 0.0
		for _, dish := range dataMapping.Dishes {
			otherPromotionPrice += dish.Discount
		}

		coFundPromotionPrice := dataMapping.ShipmentDiscount
		for _, coupon := range dataMapping.Coupons {
			coFundPromotionPrice += coupon.Total
		}

		dataMapping.TotalDiscount = otherPromotionPrice + coFundPromotionPrice
	}

	// Process customer data
	dataMapping.CustomerData = models.CustomerData{
		OriginalPrice:    originalPrice,
		SellPrice:        sellPrice,
		ShipmentFee:      dataMapping.ShipmentFee,
		OrderDiscount:    dataMapping.TotalDiscount,
		ShipmentDiscount: dataMapping.ShipmentDiscount,
		AdditionalIncome: 0,
		TotalPaid:        0,
	}

	// Sum total paid from payments
	for _, payment := range dataMapping.Payments {
		dataMapping.CustomerData.TotalPaid += payment.Total
	}

	// Process finance data
	additionalIncome := orderJ.Get("total_surcharge").Float()

	dataMapping.FinanceData = models.FinanceData{
		OriginalPrice:        originalPrice,
		SellPrice:            sellPrice,
		CoFundPromotionPrice: dataMapping.ShipmentDiscount,
		OtherPromotionPrice:  0,
		TotalPromotionPrice:  0,
		GrossReceived:        0,
		Commission:           dataMapping.Commission,
		OtherFee:             0,
		TotalShipment:        dataMapping.TotalShipment,
		ShippingFee:          dataMapping.ShipmentFee,
		NetReceived:          dataMapping.TotalForBiz,
		AdditionalIncome:     additionalIncome,
		RealReceived:         dataMapping.TotalForBiz,
	}

	// Calculate other values for finance data
	for _, dish := range dataMapping.Dishes {
		dataMapping.FinanceData.OtherPromotionPrice += dish.Discount
	}

	for _, coupon := range dataMapping.Coupons {
		dataMapping.FinanceData.CoFundPromotionPrice += coupon.Total
	}

	dataMapping.FinanceData.TotalPromotionPrice = dataMapping.FinanceData.OtherPromotionPrice + dataMapping.FinanceData.CoFundPromotionPrice
	dataMapping.FinanceData.GrossReceived = dataMapping.FinanceData.OriginalPrice + dataMapping.FinanceData.AdditionalIncome - dataMapping.FinanceData.TotalPromotionPrice

	return dataMapping
}

// mapShopeeEcomOrder maps a Shopee E-commerce order to DataMapping
func mapShopeeEcomOrder(o *models.MerchantOrder, orderJ gjson.Result) models.DataMapping {
	// Create data mapping
	dataMapping := models.DataMapping{
		ID:              o.LongOrderID,
		OrderID:         o.ShortOrderID,
		Source:          o.Source,
		OrderTime:       time.Unix(orderJ.Get("create_time").Int(), 0).Format(time.RFC3339),
		PickTime:        time.Unix(orderJ.Get("ship_by_date").Int(), 0).Format(time.RFC3339),
		DeliveryTime:    time.Unix(orderJ.Get("update_time").Int(), 0).Format(time.RFC3339),
		OrderTimeSort:   orderJ.Get("create_time").Int(),
		CustomerName:    orderJ.Get("recipient_address.name").String(),
		CustomerPhone:   orderJ.Get("recipient_address.phone").String(),
		CustomerAddress: orderJ.Get("recipient_address.full_address").String(),
		Dishes:          []models.OrderDish{},
		Note:            orderJ.Get("message_to_seller").String(),
		CancelReason:    "",
		Coupons:         []models.OrderCoupon{},
		Payments:        []models.Payment{},
		CustomerData:    models.CustomerData{},
		FinanceData:     models.FinanceData{},
		Raw:             o.DataInDetail,
	}

	// Process items
	orderJ.Get("item_list").ForEach(func(_, item gjson.Result) bool {
		dish := models.OrderDish{
			Name:     item.Get("item_name").String(),
			Quantity: item.Get("model_quantity_purchased").Int(),
			// Original price * quantity for total price
			Price: item.Get("model_original_price").Float() * float64(item.Get("model_quantity_purchased").Int()),
			// Calculate discount: (original - discounted) * quantity
			Discount: (item.Get("model_original_price").Float() - item.Get("model_discounted_price").Float()) * float64(item.Get("model_quantity_purchased").Int()),
			// Discounted price * quantity for final price
			DiscountPrice: item.Get("model_discounted_price").Float() * float64(item.Get("model_quantity_purchased").Int()),
			Note:          item.Get("item_sku").String(),
			Options:       [][]models.DishOption{},
		}
		dataMapping.Dishes = append(dataMapping.Dishes, dish)
		return true
	})

	// Calculate totals
	var total, totalDiscount float64
	for _, dish := range dataMapping.Dishes {
		total += dish.Price
		totalDiscount += dish.Discount
	}
	dataMapping.Total = total
	dataMapping.TotalForBiz = orderJ.Get("total_amount").Float() - orderJ.Get("actual_shipping_fee").Float()

	// Add commission and shipping fees
	dataMapping.Commission = orderJ.Get("service_fee").Float()
	dataMapping.ShipmentFee = orderJ.Get("actual_shipping_fee").Float()
	if dataMapping.ShipmentFee == 0 {
		dataMapping.ShipmentFee = orderJ.Get("estimated_shipping_fee").Float()
	}
	dataMapping.TotalShipment = dataMapping.ShipmentFee

	// Set payment information
	var paymentStatus string
	if orderJ.Get("pay_time").Int() > 0 {
		paymentStatus = "COMPLETED"
	} else {
		paymentStatus = "PENDING"
	}
	payment := models.Payment{
		Method: orderJ.Get("payment_method").String(),
		Total:  orderJ.Get("total_amount").Float(),
		Status: paymentStatus,
		Note:   "Thanh toán qua " + orderJ.Get("payment_method").String(),
	}
	dataMapping.Payments = append(dataMapping.Payments, payment)

	// Process customer data
	dataMapping.CustomerData = models.CustomerData{
		OriginalPrice:    total,
		SellPrice:        dataMapping.TotalForBiz,
		ShipmentFee:      orderJ.Get("actual_shipping_fee").Float(),
		OrderDiscount:    totalDiscount,
		ShipmentDiscount: orderJ.Get("estimated_shipping_fee").Float() - orderJ.Get("actual_shipping_fee").Float(),
		AdditionalIncome: 0,
		TotalPaid:        orderJ.Get("total_amount").Float(),
	}

	// Process finance data
	dataMapping.FinanceData = models.FinanceData{
		OriginalPrice:       total,
		SellPrice:           dataMapping.TotalForBiz,
		Commission:          dataMapping.Commission,
		OtherFee:            0,
		TotalShipment:       dataMapping.ShipmentFee,
		ShippingFee:         dataMapping.ShipmentFee,
		NetReceived:         dataMapping.TotalForBiz - dataMapping.Commission,
		AdditionalIncome:    0,
		RealReceived:        dataMapping.TotalForBiz - dataMapping.Commission,
		OtherPromotionPrice: totalDiscount,
		TotalPromotionPrice: totalDiscount,
	}

	// Handle cancel info
	if cancelBy := orderJ.Get("cancel_by").String(); cancelBy != "" {
		if val, ok := CANCEL_REASON_MESSAGE[cancelBy]; ok {
			dataMapping.CancelBy = cancelBy
			dataMapping.CancelReason = val
		}
		if reason := orderJ.Get("cancel_reason").String(); reason != "" {
			if dataMapping.CancelReason != "" {
				dataMapping.CancelReason += ", Chi tiết: " + reason
			} else {
				dataMapping.CancelReason = reason
			}
		}
	}

	return dataMapping
}
