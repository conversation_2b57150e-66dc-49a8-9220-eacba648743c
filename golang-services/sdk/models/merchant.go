package models

import "time"

type Token struct {
	AccessToken  string            `json:"access_token"`
	RefreshToken string            `json:"refresh_token"`
	SiteType     string            `json:"site_type"` // MART or FOOD
	SiteID       string            `json:"site_id"`
	SiteName     string            `json:"site_name"`
	Username     string            `json:"username"`
	Password     string            `json:"password"`
	Params       map[string]string `json:"params,omitempty"` // Additional parameters like auth codes, expire times, etc.
}

type StoreDetail struct {
	ID      string `json:"id"`
	Name    string `json:"name"`
	Phone   string `json:"phone"`
	Address string `json:"address"`
	Raw     any    `json:"raw,omitempty"` // Raw store data for additional fields
}

// MerchantOrder represents an order from a merchant platform (Shopee, Grab, BE, etc.)
type MerchantOrder struct {
	// Basic identifiers
	Source       string `json:"source"`         // The source platform of the order (e.g., "shopee", "grab", "be")
	LongOrderID  string `json:"long_order_id"`  // The full order ID from the source platform
	ShortOrderID string `json:"short_order_id"` // A shortened version of the order ID for display
	ID           string `json:"id,omitempty"`   // Alternative ID field used in some implementations

	// Order status information
	Status string `json:"status"` // Current order status (e.g., "PENDING", "DOING", "FINISH", "CANCEL")
	MD5    string `json:"md5"`    // MD5 hash for comparing order changes

	// Timestamps
	CreatedAt time.Time `json:"created_at,omitempty"` // When the order was created
	UpdatedAt time.Time `json:"updated_at,omitempty"` // When the order was last updated

	// Raw data from the source platform
	DataInList   any `json:"data_in_list"`          // Raw order data from list endpoint
	DataInDetail any `json:"data_in_detail"`        // Raw order data from detail endpoint
	SourceData   any `json:"source_data,omitempty"` // Alternative field for raw data
}

// StoreItem represents a store item in the store list
type StoreItem struct {
	AccessToken string `json:"access_token"`
	StoreType   string `json:"store_type"` // MART or FOOD
	StoreID     string `json:"store_id"`
	StoreName   string `json:"store_name"`
}
