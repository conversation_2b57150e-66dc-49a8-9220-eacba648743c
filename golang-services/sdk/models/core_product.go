package models

import (
	"time"
)

// CoreProductIngredient represents an ingredient in a core product
type CoreProductIngredient struct {
	Code        string  `json:"code"`
	Amount      float64 `json:"amount"`
	DisplayName string  `json:"display_name"`
	Unit        string  `json:"unit"`
}

// CoreProduct represents a product in the system
type CoreProduct struct {
	// Base model fields
	ID        string    `json:"id" gorm:"primaryKey;type:object_id;default:gen_object_id()"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`

	// Brand relationship
	BrandID string `json:"brand_id" gorm:"index"`

	// Basic information
	Name        string            `json:"name" gorm:"not null"`
	Code        string            `json:"code" gorm:"not null;index"`
	BarCode     string            `json:"bar_code"`
	Category    string            `json:"category"`
	Source      string            `json:"source"`
	Type        string            `json:"type" gorm:"type:varchar(20);check:type IN ('nguyen_lieu', 'ban_thanh_pham', 'thanh_pham', 'hang_hoa')"`
	Description string            `json:"description"`
	SalePrice   float64           `json:"sale_price"`
	Price       float64           `json:"price"`
	Images      JSONArray[string] `json:"images" gorm:"type:jsonb"`
	Unit        string            `json:"unit"`
	Weight      float64           `json:"weight"` // kg
	Length      float64           `json:"length"` // cm
	Height      float64           `json:"height"` // cm

	// Ingredients
	Ingredients JSONArray[CoreProductIngredient] `json:"ingredients" gorm:"type:jsonb"`

	// Status and availability
	AvailableForSale  bool   `json:"available_for_sale"`
	Status            string `json:"status" gorm:"type:varchar(10);check:status IN ('active', 'draft', 'inactive');default:'active'"`
	QuantityUnlimited bool   `json:"quantity_unlimited" gorm:"default:false"`
}

// TableName specifies the table name for the CoreProduct model
func (CoreProduct) TableName() string {
	return "core_products"
}

// CoreProductCategory represents a category for core products
type CoreProductCategory struct {
	// Base model fields
	ID        string    `json:"id" gorm:"primaryKey;type:object_id;default:gen_object_id()"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`

	// Brand relationship
	BrandID string `json:"brand_id" gorm:"index"`

	// Basic information
	Name string `json:"name" gorm:"not null"`
}

// TableName specifies the table name for the CoreProductCategory model
func (CoreProductCategory) TableName() string {
	return "core_product_categories"
}

// CoreProductSource represents a source for core products
type CoreProductSource struct {
	// Base model fields
	ID        string    `json:"id" gorm:"primaryKey;type:object_id;default:gen_object_id()"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`

	// Brand relationship
	BrandID string `json:"brand_id" gorm:"index"`

	// Basic information
	Name string `json:"name" gorm:"not null"`
}

// TableName specifies the table name for the CoreProductSource model
func (CoreProductSource) TableName() string {
	return "core_product_sources"
}

// CoreProductUnit represents a unit for core products
type CoreProductUnit struct {
	// Base model fields
	ID        string    `json:"id" gorm:"primaryKey;type:object_id;default:gen_object_id()"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`

	// Brand relationship
	BrandID string `json:"brand_id" gorm:"index"`

	// Basic information
	Name string `json:"name" gorm:"not null"`
}

// TableName specifies the table name for the CoreProductUnit model
func (CoreProductUnit) TableName() string {
	return "core_product_units"
}
