// sdk/models/site.go
package models

import (
	"encoding/json"
	"time"
)

type DeliveryToken struct {
	Source       string            `json:"source"`
	TokenCode    string            `json:"token_code"`
	ServiceTypes JSONArray[string] `json:"service_types"`
}

type MoMoToken struct {
	PartnerCode string `json:"partner_code"`
	AccessKey   string `json:"access_key"`
	SecretKey   string `json:"secret_key"`
}

type VNPayToken struct {
	MerchantID      string `json:"merchant_id"`
	TerminalID      string `json:"terminal_id"`
	QRSecretKey     string `json:"qr_secret_key"`
	TransSecretKey  string `json:"trans_secret_key"`
	RefundSecretKey string `json:"refund_secret_key"`
}

// ShopeeEcomToken represents the authentication details for Shopee E-commerce API
type ShopeeEcomToken struct {
	Source       string    `json:"source"`  // Identifies the token source as "shopee"
	ShopID       int64     `json:"shop_id"` // The Shopee shop ID
	TokenCode    string    `json:"token_code"`
	AccessToken  string    `json:"access_token"`
	RefreshToken string    `json:"refresh_token"`
	ExpiresAt    time.Time `json:"expires_at"` // When the access token expires
	ShopName     string    `json:"shop_name"`  // Store name for display purposes
	SiteData     string    `json:"site_data"`  // Additional data if needed
}

type SiteToken struct {
	Source    string `json:"source"`
	TokenCode string `json:"token_code"`
	// TokenCodeOfficial string `json:"token_code_official"`
	// Username          string          `json:"username"`
	// Password          string          `json:"password"`
	SiteID   string          `json:"site_id"`
	SiteName string          `json:"site_name"`
	SiteData json.RawMessage `json:"site_data"`
	// AccessToken  string          `json:"access_token"`
	// RefreshToken string          `json:"refresh_token"`
}

type WorkingHour struct {
	Start  string `json:"start"`
	End    string `json:"end"`
	Closed bool   `json:"closed"`
}

type Site struct {
	// Base model fields
	ID string `json:"id" gorm:"primaryKey;type:object_id;default:gen_object_id()"`

	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`

	// Relationships
	BrandID string            `json:"brand_id"`
	HubID   string            `json:"hub_id"`
	HubIDs  JSONArray[string] `json:"hub_ids" gorm:"type:jsonb"`

	// Basic information
	Type        string                `json:"type"` // local, mart, he
	Name        string                `json:"name"`
	Code        string                `json:"code" gorm:"uniqueIndex"`
	Address     string                `json:"address"`
	AddressObj  JSONField[AddressObj] `json:"address_obj" gorm:"type:jsonb"`
	Description string                `json:"description"`

	// Configuration and settings
	Tokens           JSONArray[SiteToken]        `json:"tokens,omitempty" gorm:"type:jsonb"`
	AhamoveToken     *JSONField[DeliveryToken]   `json:"ahamove_token,omitempty"  gorm:"type:jsonb"`
	GrabExpressToken *JSONField[DeliveryToken]   `json:"grab_express_token,omitempty"  gorm:"type:jsonb"`
	GrabToken        *JSONField[SiteToken]       `json:"grab_token,omitempty"  gorm:"type:jsonb"`
	ShopeeToken      *JSONField[SiteToken]       `json:"shopee_token,omitempty"  gorm:"type:jsonb"`
	ShopeeEcomToken  *JSONField[ShopeeEcomToken] `json:"shopee_ecom_token,omitempty"  gorm:"type:jsonb"`
	BeToken          *JSONField[SiteToken]       `json:"be_token,omitempty"  gorm:"type:jsonb"`
	MoMoToken        *JSONField[MoMoToken]       `json:"momo_token,omitempty" gorm:"type:jsonb;column:momo_token"`
	VNPayToken       *JSONField[VNPayToken]      `json:"vnpay_token,omitempty"  gorm:"type:jsonb;column:vnpay_token"`

	Active                   bool   `json:"active" gorm:"default:true"`
	AutoConfirm              bool   `json:"auto_confirm"`
	AutoPrint                bool   `json:"auto_print"`
	PrinterIP                string `json:"printer_ip"`
	SlackChannel             string `json:"slack_channel"`
	ZaloGroup                string `json:"zalo_group"`
	ApplyCommission          bool   `json:"apply_commission"`
	ApplyGift                bool   `json:"apply_gift"`
	PartnerHubTier           string `json:"partner_hub_tier"`
	UseCoreProduct           bool   `json:"use_core_product"`
	EnableLocalOrderShipping bool   `json:"enable_local_order_shipping"`
	IsHeadSite               bool   `json:"is_head_site"`
	IsHESite                 bool   `json:"is_he_site"`
	PreparationTimePerOrder  int    `json:"preparation_time_per_order"`

	// Operational status
	PauseApps    JSONField[map[string]bool]        `json:"pause_apps" gorm:"type:jsonb"`
	WorkingHours JSONField[map[string]WorkingHour] `json:"working_hours" gorm:"type:jsonb"`

	// Custom for SAAS
	MainSource string `json:"main_source"`

	// Relations
	Brand *Brand `json:"brand,omitempty" gorm:"foreignKey:BrandID"`
	Hub   *Hub   `json:"hub,omitempty" gorm:"foreignKey:HubID"`
}

// GetToken returns a specific token by source
func (s *Site) GetToken(source string) *SiteToken {
	if s.Tokens == nil {
		return nil
	}

	for _, token := range s.Tokens {
		if token.Source == source {
			return &token
		}
	}
	return nil
}

// IsApplyVoucher checks if vouchers can be applied to this site
func (s *Site) IsApplyVoucher() bool {
	if s.Brand == nil {
		return false
	}
	// Add your voucher application logic here
	return true
}
