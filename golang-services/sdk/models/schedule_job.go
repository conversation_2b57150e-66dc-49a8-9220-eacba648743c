package models

import (
	"encoding/json"
	"time"
)

// JobType represents the type of scheduled job
type JobType string

const (
	JobTypeAPIRequest       JobType = "api_request"
	JobTypeGetOrder         JobType = "get_order"
	JobTypeGetOrderFeedback JobType = "get_order_feedback"
)

// JobStatus represents the status of a job execution
type JobStatus string

const (
	JobStatusSuccess   JobStatus = "success"
	JobStatusFailed    JobStatus = "failed"
	JobStatusPending   JobStatus = "pending"
	JobStatusCompleted JobStatus = "completed"
)

// WatchDogJob represents a watchdog job with scheduling capabilities
type WatchDogJob struct {
	ID           int64           `json:"id" gorm:"primaryKey;autoIncrement" db:"id" header:"id"`
	JobName      string          `json:"job_name" gorm:"type:varchar(100);not null" db:"job_name"`
	JobData      json.RawMessage `json:"job_data" gorm:"type:jsonb" db:"job_data"`
	ScheduleAt   time.Time       `json:"schedule_at" gorm:"not null" db:"schedule_at"`
	StartedAt    *time.Time      `json:"started_at" db:"started_at"`
	CompletedAt  *time.Time      `json:"completed_at" db:"completed_at"`
	CronInterval int             `json:"cron_interval" gorm:"default:0" db:"cron_interval"` // in seconds
	Status       string          `json:"status" gorm:"type:varchar(20);default:'pending'" db:"status"`
	Message      *string         `json:"message" gorm:"type:text" db:"message"`
	CreatedAt    time.Time       `json:"created_at" gorm:"autoCreateTime" db:"created_at" header:"created_at"`
	UpdatedAt    *time.Time      `json:"updated_at" gorm:"autoUpdateTime" db:"updated_at"`
}

// TableName specifies the table name for the WatchDogJob model
func (WatchDogJob) TableName() string {
	return "watchdog_jobs"
}

// WatchDogJobHistory represents the execution history of watchdog jobs
type WatchDogJobHistory struct {
	ID          int64           `json:"id" gorm:"primaryKey;autoIncrement"`
	JobID       int64           `json:"job_id" gorm:"not null;index"`
	JobName     string          `json:"job_name" gorm:"type:varchar(100);not null"`
	JobData     json.RawMessage `json:"job_data" gorm:"type:jsonb"`
	StartedAt   time.Time       `json:"started_at" gorm:"not null"`
	CompletedAt *time.Time      `json:"completed_at"`
	Status      string          `json:"status" gorm:"type:varchar(20);not null"`
	Message     *string         `json:"message" gorm:"type:text"`
	CreatedAt   time.Time       `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt   *time.Time      `json:"updated_at" gorm:"autoUpdateTime"`
}

// TableName specifies the table name for the WatchDogJobHistory model
func (WatchDogJobHistory) TableName() string {
	return "watchdog_job_histories"
}
