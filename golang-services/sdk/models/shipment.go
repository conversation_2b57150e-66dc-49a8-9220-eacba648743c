package models

import "time"

type ShipmentSlot struct {
	Date         string `json:"date"`
	FromTime     string `json:"from_time"`
	FromDateTime string `json:"from_date_time"`
	ToTime       string `json:"to_time"`
	ToDateTime   string `json:"to_date_time"`
	IsActive     bool   `json:"is_active"`
}

// ShipToken represents authentication tokens for shipping vendors
type ShipToken struct {
	Username     string         `json:"username"`
	AccessToken  string         `json:"access_token"`
	RefreshToken string         `json:"refresh_token"`
	ExpiredAt    time.Time      `json:"expired_at"`
	Settings     map[string]any `json:"settings,omitempty"`
	SiteData     map[string]any `json:"site_data,omitempty"`
}

// Location represents a pickup or delivery location
type Location struct {
	Address string `json:"address"`
	Name    string `json:"name"`
	Phone   string `json:"phone"`
}

// Dimensions represents package dimensions
type Dimensions struct {
	Length float64 `json:"length"`
	Width  float64 `json:"width"`
	Height float64 `json:"height"`
	Weight float64 `json:"weight"`
}

// Dish represents an item to be delivered
type Dish struct {
	Name          string  `json:"name"`
	Quantity      int     `json:"quantity"`
	DiscountPrice float64 `json:"discount_price"`
}

// ExtraService represents additional services for a shipment
type ExtraService struct {
	Name  string  `json:"name"`
	Code  string  `json:"code"`
	Price float64 `json:"price"`
}

// PromoCheckRequest represents a request to check a promo code
type PromoCheckRequest struct {
	PromoCode string   `json:"promo_code"`
	From      Location `json:"from"`
	To        Location `json:"to"`
	Services  []string `json:"services"`
}

// PromoCheckResponse represents the response from checking a promo code
type PromoCheckResponse struct {
	Valid bool `json:"valid"`
	Data  any  `json:"data"`
}

// CreateShipmentRequest represents a request to create a shipment
type CreateShipmentRequest struct {
	Dishes            []Dish      `json:"dishes"`
	PromoCode         string      `json:"promo_code,omitempty"`
	From              Location    `json:"from"`
	To                Location    `json:"to"`
	Service           ShipService `json:"service"`
	Note              string      `json:"note,omitempty"`
	COD               float64     `json:"cod"`
	TrackingNumber    string      `json:"tracking_number,omitempty"`
	Dimensions        Dimensions  `json:"dimensions,omitempty"`
	ScheduleOrderTime int64       `json:"schedule_order_time,omitempty"`
}

// ShipService represents a shipping service type
type ShipService struct {
	Code          string         `json:"code"`
	ExtraServices []ExtraService `json:"extra_services,omitempty"`
}

// CreateShipmentResponse represents the response from creating a shipment
type CreateShipmentResponse struct {
	ShipmentID  string  `json:"shipment_id"`
	TrackingURL string  `json:"tracking_url"`
	Price       float64 `json:"price"`
	RawRequest  any     `json:"raw_request"`
	RawResponse any     `json:"raw_response"`
}

// CancelShipmentResponse represents the response from canceling a shipment
type CancelShipmentResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
}

// ShipmentDetail represents the details of a shipment
type ShipmentDetail struct {
	Status      string `json:"status"`
	DriverName  string `json:"driver_name,omitempty"`
	DriverPhone string `json:"driver_phone,omitempty"`
	RawData     any    `json:"raw_data"`
}

// GetShipmentsRequest represents a request to get available shipment options
type GetShipmentsRequest struct {
	From           Location   `json:"from"`
	To             Location   `json:"to"`
	Dimensions     Dimensions `json:"dimensions,omitempty"` // Changed from map to struct
	PromoCode      string     `json:"promo_code,omitempty"`
	TrackingNumber string     `json:"tracking_number,omitempty"`
	Dishes         []Dish     `json:"dishes,omitempty"`
	COD            float64    `json:"cod,omitempty"`
}

// ShipmentOption represents an available shipment option
type ShipmentOption struct {
	Vendor        string         `json:"vendor"`
	Code          string         `json:"code"`
	GroupCode     string         `json:"group_code"`
	Price         float64        `json:"price"`
	Name          string         `json:"name"`
	Description   string         `json:"description"`
	ExtraServices []ExtraService `json:"extra_services,omitempty"`
	Raw           any            `json:"raw"`
}

// WebhookResponse represents the processed response from a webhook
type WebhookResponse struct {
	ShipmentID     string      `json:"shipment_id"`
	OrderID        string      `json:"order_id"`
	ShipmentStatus string      `json:"shipment_status"`
	OrderStatus    string      `json:"order_status"`
	DriverName     string      `json:"driver_name"`
	DriverPhone    string      `json:"driver_phone"`
	TrackingURL    string      `json:"tracking_url"`
	Cancel         *CancelInfo `json:"cancel,omitempty"`
}

// CancelInfo represents information about a canceled shipment
type CancelInfo struct {
	CancelBy     string `json:"cancel_by"`
	CancelType   string `json:"cancel_type"`
	CancelReason string `json:"cancel_reason"`
}
