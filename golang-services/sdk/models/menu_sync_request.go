package models

import (
	"time"
)

type MenuSyncRequest struct {
	// Base model fields
	ID string `json:"id" gorm:"primaryKey;type:object_id;default:gen_object_id()"`

	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`

	// Basic information
	SiteID      string     `json:"site_id" gorm:"index"`
	MenuData    any        `json:"menu_data" gorm:"type:jsonb"`
	Callback    any        `json:"callback" gorm:"type:jsonb"`
	Status      string     `json:"status" gorm:"type:varchar(20);check:status IN ('pending', 'processing', 'success', 'failed');default:'pending'"`
	StartedAt   *time.Time `json:"started_at"`
	CompletedAt *time.Time `json:"completed_at"`
}

// TableName specifies the table name for the Hub model
func (MenuSyncRequest) TableName() string {
	return "menu_sync_requests"
}
