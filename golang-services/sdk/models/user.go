package models

import (
	"time"
)

// UserStatus represents the status of a user
type UserStatus string

const (
	// User status constants
	UserStatusPending  UserStatus = "pending"
	UserStatusActive   UserStatus = "active"
	UserStatusInactive UserStatus = "inactive"
)

// HeAccountInfo represents HE-specific user account information
type HeAccountInfo struct {
	Status        UserStatus `json:"status" gorm:"type:varchar(10);check:status IN ('pending', 'active')"`
	ReferrerID    string     `json:"referrer_id"`
	ApprovedAt    time.Time  `json:"approved_at"`
	SavedAccounts []struct {
		BankName      string `json:"bank_name"`
		AccountNumber string `json:"account_number"`
		AccountName   string `json:"account_name"`
	} `json:"saved_accounts" gorm:"type:jsonb"`
}

// User represents a user in the system
type User struct {
	// Base model fields
	ID        string    `json:"id" gorm:"primaryKey;type:object_id;default:gen_object_id()"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`

	// Basic information
	Username string `json:"username" gorm:"uniqueIndex;not null"`
	Email    string `json:"email" gorm:"uniqueIndex;not null"`
	Phone    string `json:"phone" gorm:"uniqueIndex"`
	Name     string `json:"name"`
	Address  string `json:"address"`
	Password string `json:"password"`
	Avatar   string `json:"avatar"`

	// Ownership relationship
	OwnerID *string `json:"owner_id" gorm:"index"`
	Owner   *User   `json:"owner,omitempty" gorm:"foreignKey:OwnerID"`
	Role    string  `json:"role" gorm:"type:varchar(20)"`

	// Access control
	Status    UserStatus `json:"status" gorm:"type:varchar(10);check:status IN ('pending', 'active', 'inactive');default:'pending'"`
	IsGuest   bool       `json:"is_guest" gorm:"default:false"`
	ExpiredAt *time.Time `json:"expired_at"`

	// Resource access
	Hubs   JSONArray[string] `json:"hubs" gorm:"type:jsonb;default:'[]'"`
	Brands JSONArray[string] `json:"brands" gorm:"type:jsonb;default:'[]'"`
	Sites  JSONArray[string] `json:"sites" gorm:"type:jsonb;default:'[]'"`

	// Login tracking
	LastLoginDevice  string            `json:"last_login_device"`
	LastLoginDevices JSONArray[string] `json:"last_login_devices" gorm:"type:jsonb;default:'[]'"`
	LoginFailCount   int               `json:"login_fail_count"`
}

// TableName specifies the table name for the User model
func (User) TableName() string {
	return "users"
}

// UserOTP represents one-time password records
type UserOTP struct {
	// Base model fields
	ID        string    `json:"id" gorm:"primaryKey;type:object_id;default:gen_object_id()"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`

	// OTP information
	ReceiveMethod string    `json:"receive_method" gorm:"type:varchar(10);check:receive_method IN ('email', 'zalo', 'sms');default:'email'"`
	VerifyType    string    `json:"verify_type" gorm:"type:varchar(20);check:verify_type IN ('verify_phone', 'register', 'forgot_password')"`
	UserUID       string    `json:"user_uid"` // email or phone
	OTP           string    `json:"otp"`
	Message       string    `json:"message"`
	ExpiredAt     time.Time `json:"expired_at"`
	VerifiedHash  string    `json:"verified_hash"`
}

// TableName specifies the table name for the UserOTP model
func (UserOTP) TableName() string {
	return "user_otps"
}

// UserAddress represents a user's saved addresses
type UserAddress struct {
	// Base model fields
	ID        string    `json:"id" gorm:"primaryKey;type:object_id;default:gen_object_id()"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`

	// Address information
	UserID     string                `json:"user_id" gorm:"index"`
	Name       string                `json:"name"`
	Phone      string                `json:"phone"`
	Address    string                `json:"address"`
	AddressObj JSONField[AddressObj] `json:"address_obj" gorm:"type:jsonb"`
	Note       string                `json:"note"`
	IsDefault  bool                  `json:"is_default" gorm:"default:false"`
}

// TableName specifies the table name for the UserAddress model
func (UserAddress) TableName() string {
	return "user_addresses"
}

type FCMToken struct {
	ID        string            `json:"id" gorm:"primaryKey;type:object_id;default:gen_object_id()"`
	UserID    string            `json:"user_id" gorm:"index"`
	FCMToken  string            `json:"fcm_token" gorm:"type:varchar(255);not null"`
	UserAgent string            `json:"user_agent" gorm:"type:text"`
	Topics    JSONArray[string] `json:"topics" gorm:"type:jsonb"`
	CreatedAt time.Time         `json:"created_at"`
	UpdatedAt time.Time         `json:"updated_at"`

	// Relations
	User *User `json:"user,omitempty" gorm:"foreignKey:UserID"`
}

// TableName specifies the table name for GORM
func (FCMToken) TableName() string {
	return "fcm_tokens"
}

type UserAuth struct {
	*User
	ManagedSites  []Site       `json:"managed_sites"`
	ManagedHubs   []Hub        `json:"managed_hubs"`
	ManagedBrands []Brand      `json:"managed_brands"`
	Permissions   []Permission `json:"permissions"` // User's permissions from their role
}

type AuthData struct {
	User        *UserAuth `json:"user"`
	AccessToken string    `json:"access_token"`
}

// UserMetaData represents user metadata stored as key-value pairs
type UserMetaData struct {
	// Base model fields
	ID        string    `json:"id" gorm:"primaryKey;type:object_id;default:gen_object_id()"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`

	// Foreign key to users table
	UserID string `json:"user_id" gorm:"index;not null"`

	// Meta data fields
	MetaKey   string                    `json:"meta_key" gorm:"not null;index"`
	MetaValue JSONField[map[string]any] `json:"meta_value" gorm:"type:jsonb"`
}

// TableName specifies the table name for the UserMetaData model
func (UserMetaData) TableName() string {
	return "user_meta_data"
}
