package utils

import (
	"context"
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"os"
	"strings"

	"cloud.google.com/go/storage"
	"google.golang.org/api/option"
)

func UploadFile(bucketName, filename string, buffer []byte) (string, error) {
	ctx := context.Background()

	privateKeyJSON := os.Getenv("GOOGLE_PRIVATE_KEY")
	if privateKeyJSON == "" {
		return "", fmt.<PERSON><PERSON><PERSON>("GOOGLE_PRIVATE_KEY is not set")
	}

	var credentials map[string]any
	if err := json.Unmarshal([]byte(privateKeyJSON), &credentials); err != nil {
		return "", fmt.Errorf("failed to parse GOOGLE_PRIVATE_KEY JSON: %v", err)
	}

	if privateKey, ok := credentials["private_key"].(string); ok {
		credentials["private_key"] = strings.ReplaceAll(privateKey, "\\n", "\n")
	}

	fixedJSON, err := json.Marshal(credentials)
	if err != nil {
		return "", fmt.<PERSON>rro<PERSON>("failed to marshal fixed credentials: %v", err)
	}
	client, err := storage.NewClient(ctx, option.WithCredentialsJSON(fixedJSON))
	if err != nil {
		return "", fmt.Errorf("failed to create client: %v", err)
	}
	defer client.Close()

	hash := md5.Sum(buffer)
	hashString := hex.EncodeToString(hash[:])
	fileName := fmt.Sprintf("uploads/%s_%s", hashString, filename)

	bucket := client.Bucket(bucketName)
	obj := bucket.Object(fileName)

	writer := obj.NewWriter(ctx)

	writer.ObjectAttrs.ContentType = "application/octet-stream"
	writer.ObjectAttrs.CacheControl = "public, max-age=86400"
	writer.ObjectAttrs.ContentDisposition = "inline"

	if _, err := writer.Write(buffer); err != nil {
		return "", fmt.Errorf("failed to write to bucket: %v", err)
	}

	if err := writer.Close(); err != nil {
		return "", fmt.Errorf("failed to close writer: %v", err)
	}

	return fmt.Sprintf("https://storage.googleapis.com/%s/%s", bucketName, fileName), nil
}
