package utils

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"path/filepath"
	"time"
)

// ExcelDownloadResponse represents the API response structure for Excel exports
type ExcelDownloadResponse struct {
	Success bool                      `json:"success"`
	Data    ExcelDownloadResponseData `json:"data"`
}

// ExcelDownloadResponseData represents the data field in the Excel download response
type ExcelDownloadResponseData struct {
	FileURL  string `json:"file_url"`
	Filename string `json:"filename"`
}

// DownloadCoreProductsExcel downloads core products as an Excel file and saves it to the specified path.
// It handles the two-step process of first fetching the file URL, then downloading the actual file.
//
// Parameters:
//   - baseURL: The base URL of the API (e.g., "https://api.example.com")
//   - brandID: The brand ID for which to export core products
//   - outputDir: Directory where to save the downloaded file (optional, defaults to current directory)
//   - authToken: Authentication token for API access
//
// Returns:
//   - The full path to the downloaded file
//   - Any error encountered during the process
func DownloadCoreProductsExcel(baseURL, brandID, outputDir, authToken string) (string, error) {
	// If output directory is not specified, use current directory
	if outputDir == "" {
		outputDir = "."
	}

	// Create a context with timeout for the request
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// Construct the API URL for the export endpoint
	url := fmt.Sprintf("%s/brands/%s/core-products/export", baseURL, brandID)

	// Create a new HTTP request
	req, err := http.NewRequestWithContext(ctx, http.MethodGet, url, nil)
	if err != nil {
		return "", fmt.Errorf("error creating request: %w", err)
	}

	// Set headers for JSON response and authentication
	req.Header.Set("Accept", "application/json")
	if authToken != "" {
		req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", authToken))
	}

	// Execute the request
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return "", fmt.Errorf("error executing request: %w", err)
	}
	defer resp.Body.Close()

	// Check status code
	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return "", fmt.Errorf("API returned status %d: %s", resp.StatusCode, string(body))
	}

	// Parse the JSON response
	var response ExcelDownloadResponse

	// Decode the JSON response
	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return "", fmt.Errorf("error parsing JSON response: %w", err)
	}

	// Check if the response indicates success
	if !response.Success || response.Data.FileURL == "" {
		return "", fmt.Errorf("API returned unsuccessful response or empty file URL")
	}

	// Determine the output path
	savePath := filepath.Join(outputDir, response.Data.Filename)

	// Download the file from the provided URL
	return downloadFileFromURL(response.Data.FileURL, savePath)
}

// downloadFileFromURL downloads a file from the given URL and saves it to the specified path.
// This is a helper function used by DownloadCoreProductsExcel.
//
// Parameters:
//   - url: The URL from which to download the file
//   - savePath: Full path where the file should be saved
//
// Returns:
//   - The full path to the downloaded file
//   - Any error encountered during the download process
func downloadFileFromURL(url, savePath string) (string, error) {
	// Create a context with timeout for the download
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Minute)
	defer cancel()

	// Create a new HTTP request
	req, err := http.NewRequestWithContext(ctx, http.MethodGet, url, nil)
	if err != nil {
		return "", fmt.Errorf("error creating download request: %w", err)
	}

	// Execute the request
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return "", fmt.Errorf("error downloading file: %w", err)
	}
	defer resp.Body.Close()

	// Check status code
	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("download URL returned status %d", resp.StatusCode)
	}

	// Create the directory for the file if it doesn't exist
	dir := filepath.Dir(savePath)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return "", fmt.Errorf("error creating directory: %w", err)
	}

	// Create the file
	file, err := os.Create(savePath)
	if err != nil {
		return "", fmt.Errorf("error creating file: %w", err)
	}
	defer file.Close()

	// Copy the response body to the file
	_, err = io.Copy(file, resp.Body)
	if err != nil {
		return "", fmt.Errorf("error saving file: %w", err)
	}

	return savePath, nil
}
