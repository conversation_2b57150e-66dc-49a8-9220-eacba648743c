package email

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/utils"
)

type EmailData struct {
	To           string         `json:"to"`
	Subject      string         `json:"subject"`
	TemplateName string         `json:"template_name"`
	Data         map[string]any `json:"data"`
}

func SendEmail(to, subject, template string, data map[string]any) error {
	emailService := "https://email.nexpos.io/v1/email-service/send"

	emailData := EmailData{
		To:           to,
		TemplateName: template,
		Subject:      subject,
		Data:         data,
	}

	jsonData, err := json.Marshal(emailData)
	if err != nil {
		return err
	}
	fmt.Println(string(jsonData))

	req, err := http.NewRequest("POST", emailService, bytes.NewBuffer(jsonData))
	if err != nil {
		return err
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("x-access-token", utils.GetEnv("INTERNAL_API_KEY", ""))

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return err
	}
	// Log response
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}

	log.Printf("Email service response: %s", string(body))
	defer resp.Body.Close()

	return nil
}

func getEmailSubject(templateName string) string {
	subjects := map[string]string{
		"register":        "Verify Your Account",
		"forgot_password": "Reset Your Password",
	}
	return subjects[templateName]
}
