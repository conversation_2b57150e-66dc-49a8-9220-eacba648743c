package utils

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"
)

var httpClient = &http.Client{
	Timeout: 30 * time.Second,
}

// GenerateCurlCommand creates a curl command string from an HTTP request
func GenerateCurlCommand(method, url string, headers map[string]string, body any) string {
	cmd := fmt.Sprintf("curl -X %s '%s'", method, url)

	// Add headers
	for key, value := range headers {
		cmd += fmt.Sprintf(" -H '%s: %s'", key, value)
	}

	// Add content-type header if body is present
	if body != nil {
		_, hasContentType := headers["Content-Type"]
		if !hasContentType {
			cmd += " -H 'Content-Type: application/json; charset=utf-8'"
		}
	}

	// Add body if present
	if body != nil {
		jsonBody, err := json.Marshal(body)
		if err == nil {
			// Escape single quotes in the JSON
			escapedBody := strings.ReplaceAll(string(jsonBody), "'", "\\''")
			cmd += fmt.Sprintf(" -d '%s'", escapedBody)
		}
	}

	return cmd
}

func DoRequest(method, url string, headers map[string]string, body any) ([]byte, error) {
	// Generate and log curl command for debugging
	curlCmd := GenerateCurlCommand(method, url, headers, body)
	fmt.Printf("[DEBUG] CURL Command: %s\n", curlCmd)

	var bodyReader io.Reader
	if body != nil {
		jsonBody, err := json.Marshal(body)
		if err != nil {
			return nil, err
		}
		bodyReader = bytes.NewBuffer(jsonBody)
	}

	req, err := http.NewRequest(method, url, bodyReader)
	if err != nil {
		return nil, err
	}

	for key, value := range headers {
		req.Header.Set(key, value)
	}

	if body != nil {
		req.Header.Set("Content-Type", "application/json; charset=utf-8")
	}

	resp, err := httpClient.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode < 200 || resp.StatusCode >= 300 {
		return nil, fmt.Errorf("HTTP request failed with status: %d", resp.StatusCode)
	}

	return io.ReadAll(resp.Body)
}
