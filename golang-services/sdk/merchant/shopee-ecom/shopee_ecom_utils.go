// Package shopee_ecom provides utility functions for Shopee E-commerce API integration
package shopee_ecom

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"
)

const (
	// APIHostVN is the Shopee Open Platform API host for Vietnam region
	// (Note: Vietnam uses the same host as Singapore but kept separate for clarity)
	APIHostVN = "https://partner.shopeemobile.com"

	// APISandbox is the Shopee Open Platform API sandbox environment
	APISandbox = "https://openplatform.sandbox.test-stable.shopee.sg"
)

// ShopeeConfig holds the configuration for Shopee API
type ShopeeConfig struct {
	PartnerID       int64         // Partner ID provided by Shopee
	PartnerKey      string        // Partner key for signing requests
	Host            string        // API host to use
	AccessToken     string        // Access token for authenticated requests
	ShopID          int64         // Shop ID for shop-level APIs
	MerchantID      int64         // Merchant ID for merchant-level APIs (cross-border sellers)
	Timeout         time.Duration // HTTP client timeout
	Debug           bool          // Enable debug logging
	IsSandbox       bool          // Use sandbox environment
	RetryCount      int           // Number of retry attempts for failed API calls
	RetryWaitTime   time.Duration // Wait time between retries
	SkipMaintenance bool          // Skip API calls during maintenance hours
	MaintenanceFrom int           // Maintenance start hour (0-23)
	MaintenanceTo   int           // Maintenance end hour (0-23)
	UserAgent       string        // User agent for HTTP requests
	AppType         AppType       // Type of Shopee Open Platform application
	Environment     Environment   // Current environment (development/production)
}

// APIType represents the type of Shopee API being called
type APIType int

const (
	// PublicAPI represents APIs that don't require authentication
	PublicAPI APIType = iota
	// ShopAPI represents shop-level APIs requiring shop_id
	ShopAPI
	// MerchantAPI represents merchant-level APIs requiring merchant_id
	MerchantAPI
)

// NewShopeeConfig creates a new configuration for Shopee API
func NewShopeeConfig(partnerID int64, partnerKey string) *ShopeeConfig {
	return &ShopeeConfig{
		PartnerID:     partnerID,
		PartnerKey:    partnerKey,
		Host:          APIHostVN, // Default to Vietnam region
		Timeout:       30 * time.Second,
		RetryCount:    3,
		RetryWaitTime: 2 * time.Second,
		Environment:   GetEnvironment(),
		AppType:       DefaultApp,
	}
}

// NewShopeeConfigWithAppType creates a new configuration for a specific Shopee app type
func NewShopeeConfigWithAppType(appType AppType) *ShopeeConfig {
	// Get current environment
	env := GetEnvironment()

	// Get app configuration for this type and environment
	appConfig, err := GetAppConfig(appType, env)
	if err != nil {
		// Fallback to default values if app type not found
		return NewShopeeConfig(12345, "default-partner-key")
	}

	// Create config with app-specific values
	config := NewShopeeConfig(appConfig.PartnerID, appConfig.PartnerKey)
	config.AppType = appType
	config.Environment = env

	// Set host based on environment
	if host, ok := EnvironmentConfig[env]; ok {
		config.Host = host
	}

	return config
}

// SetAppType changes the config's app type and updates partner ID and key
func (c *ShopeeConfig) SetAppType(appType AppType) error {
	appConfig, err := GetAppConfig(appType, c.Environment)
	if err != nil {
		return err
	}

	// Update config with new app's values
	c.AppType = appType
	c.PartnerID = appConfig.PartnerID
	c.PartnerKey = appConfig.PartnerKey

	return nil
}

// SetEnvironment changes the config's environment and updates host
func (c *ShopeeConfig) SetEnvironment(env Environment) error {
	// Update environment
	c.Environment = env

	// Update host based on environment
	if host, ok := EnvironmentConfig[env]; ok {
		c.Host = host
	} else {
		return fmt.Errorf("invalid environment: %s", env)
	}

	// If app type is already set, update partner ID and key for the new environment
	if c.AppType != "" {
		appConfig, err := GetAppConfig(c.AppType, env)
		if err != nil {
			return err
		}

		c.PartnerID = appConfig.PartnerID
		c.PartnerKey = appConfig.PartnerKey
	}

	return nil
}

// GenerateSign creates a signature for Shopee API requests based on the API type
func (c *ShopeeConfig) GenerateSign(apiPath string, timestamp int64, apiType APIType) string {
	// Create the base string according to API type
	var baseString string

	switch apiType {
	case ShopAPI:
		// Shop API: partner_id + api_path + timestamp + access_token + shop_id
		baseString = fmt.Sprintf("%d%s%d%s%d", c.PartnerID, apiPath, timestamp, c.AccessToken, c.ShopID)
	case MerchantAPI:
		// Merchant API: partner_id + api_path + timestamp + access_token + merchant_id
		baseString = fmt.Sprintf("%d%s%d%s%d", c.PartnerID, apiPath, timestamp, c.AccessToken, c.MerchantID)
	case PublicAPI:
		// Public API: partner_id + api_path + timestamp
		baseString = fmt.Sprintf("%d%s%d", c.PartnerID, apiPath, timestamp)
	}

	// Calculate HMAC-SHA256 signature
	h := hmac.New(sha256.New, []byte(c.PartnerKey))
	h.Write([]byte(baseString))

	// Return hex-encoded signature
	return hex.EncodeToString(h.Sum(nil))
}

// BuildRequestURL constructs the full request URL with all necessary parameters
func (c *ShopeeConfig) BuildRequestURL(apiPath string, apiType APIType, additionalParams map[string]string) string {
	// Get current timestamp
	timestamp := time.Now().Unix()

	// Generate signature
	sign := c.GenerateSign(apiPath, timestamp, apiType)

	// Base URL with common parameters
	baseURL := fmt.Sprintf("%s%s?partner_id=%d&timestamp=%d&sign=%s",
		c.Host, apiPath, c.PartnerID, timestamp, sign)

	// Add type-specific parameters
	switch apiType {
	case ShopAPI:
		baseURL = fmt.Sprintf("%s&shop_id=%d&access_token=%s", baseURL, c.ShopID, c.AccessToken)
	case MerchantAPI:
		baseURL = fmt.Sprintf("%s&merchant_id=%d&access_token=%s", baseURL, c.MerchantID, c.AccessToken)
	}

	// Add additional request parameters
	if additionalParams != nil && len(additionalParams) > 0 {
		params := []string{}
		for key, value := range additionalParams {
			params = append(params, fmt.Sprintf("%s=%s", key, value))
		}
		baseURL = fmt.Sprintf("%s&%s", baseURL, strings.Join(params, "&"))
	}

	return baseURL
}

// APIResponse represents the standard Shopee API response structure
type APIResponse struct {
	RequestID string          `json:"request_id"`
	Error     string          `json:"error"`
	Message   string          `json:"message,omitempty"`
	Warning   string          `json:"warning,omitempty"`
	Response  json.RawMessage `json:"response,omitempty"`
}

// DoRequest performs an HTTP request to the Shopee API
func (c *ShopeeConfig) DoRequest(method, apiPath string, apiType APIType, queryParams map[string]string, body interface{}) (*APIResponse, error) {
	// Build the request URL
	url := c.BuildRequestURL(apiPath, apiType, queryParams)

	var req *http.Request
	var err error

	// Create request with or without body
	if method == http.MethodPost && body != nil {
		jsonBody, err := json.Marshal(body)
		if err != nil {
			return nil, fmt.Errorf("error marshaling request body: %w", err)
		}

		req, err = http.NewRequest(method, url, strings.NewReader(string(jsonBody)))
		if err != nil {
			return nil, fmt.Errorf("error creating request: %w", err)
		}

		req.Header.Set("Content-Type", "application/json")
	} else {
		req, err = http.NewRequest(method, url, nil)
		if err != nil {
			return nil, fmt.Errorf("error creating request: %w", err)
		}
	}

	// Add user agent if specified
	if c.UserAgent != "" {
		req.Header.Set("User-Agent", c.UserAgent)
	}

	// Execute the request with retry logic
	var resp *http.Response
	var lastErr error

	client := &http.Client{Timeout: c.Timeout}

	// Retry loop
	for attempt := 0; attempt <= c.RetryCount; attempt++ {
		if attempt > 0 && c.Debug {
			fmt.Printf("Retrying request %s %s (attempt %d/%d)\n", method, apiPath, attempt, c.RetryCount)
		}

		resp, err = client.Do(req)
		if err == nil {
			break
		}

		lastErr = err

		// Wait before retrying (except on last attempt)
		if attempt < c.RetryCount {
			time.Sleep(c.RetryWaitTime)
		}
	}

	// If all attempts failed, return the last error
	if err != nil {
		return nil, fmt.Errorf("error executing request after %d attempts: %w", c.RetryCount+1, lastErr)
	}

	defer resp.Body.Close()

	// Read and parse the response
	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("error reading response body: %w", err)
	}

	// Log response in debug mode
	if c.Debug {
		fmt.Printf("Response from %s %s: %s\n", method, apiPath, string(responseBody))
	}

	// Parse the API response
	var apiResponse APIResponse
	if err := json.Unmarshal(responseBody, &apiResponse); err != nil {
		return nil, fmt.Errorf("error unmarshaling response: %w", err)
	}

	// Check for API errors
	if apiResponse.Error != "" {
		return &apiResponse, fmt.Errorf("API error: %s - %s", apiResponse.Error, apiResponse.Message)
	}

	return &apiResponse, nil
}

// InitFromToken initializes the ShopeeConfig from a models.Token
func (c *ShopeeConfig) InitFromToken(token *models.Token) error {
	// Set the access token
	c.AccessToken = token.AccessToken

	// Try to parse shop ID from SiteID if it's numeric
	if shopID, err := strconv.ParseInt(token.SiteID, 10, 64); err == nil {
		c.ShopID = shopID
	} else {
		return fmt.Errorf("invalid shop ID format: %s", token.SiteID)
	}

	return nil
}

// AuthenticateToken gets a new access token or refreshes an existing one.
// This is a Public API endpoint according to Shopee documentation.
func (c *ShopeeConfig) AuthenticateToken(authCode, refreshToken string) (*APIResponse, error) {
	// Validate that at least one authentication parameter is provided
	if authCode == "" && refreshToken == "" {
		return nil, fmt.Errorf("either authCode or refreshToken must be provided")
	}

	// Determine if we're getting a new token or refreshing an existing one
	var apiPath string
	var body map[string]interface{}

	if refreshToken != "" {
		// Refresh token flow
		apiPath = "/api/v2/auth/access_token/refresh"
		body = map[string]interface{}{
			"refresh_token": refreshToken,
			"partner_id":    c.PartnerID,
			"shop_id":       c.ShopID,
		}
	} else {
		// New token flow with auth code
		apiPath = "/api/v2/auth/token/get"
		body = map[string]interface{}{
			"code":       authCode,
			"partner_id": c.PartnerID,
			"shop_id":    c.ShopID,
		}
	}

	// Get current timestamp for signature generation
	timestamp := time.Now().Unix()

	// For Public APIs, the sign base string format is: partner_id + api_path + timestamp
	// The shop_id is NOT included in the signature calculation for authentication endpoints
	baseString := fmt.Sprintf("%d%s%d", c.PartnerID, apiPath, timestamp)

	// Calculate HMAC-SHA256 signature using partner key
	h := hmac.New(sha256.New, []byte(c.PartnerKey))
	h.Write([]byte(baseString))
	sign := hex.EncodeToString(h.Sum(nil))

	// Build the URL with required parameters
	// Note: shop_id is included as a query parameter but NOT in the signature
	requestURL := fmt.Sprintf("%s%s?partner_id=%d&timestamp=%d&sign=%s",
		c.Host, apiPath, c.PartnerID, timestamp, sign)

	// Log request details for debugging
	if c.Debug {
		jsonBody, _ := json.Marshal(body)
		fmt.Printf("Debug: URL: %s\n", requestURL)
		fmt.Printf("Debug: Base string for signing: %s\n", baseString)
		fmt.Printf("Debug: Partner ID: %d, Shop ID: %d\n", c.PartnerID, c.ShopID)
		fmt.Printf("Debug: Request body: %s\n", string(jsonBody))
	}

	// Create the HTTP request with the JSON body
	jsonBody, err := json.Marshal(body)
	if err != nil {
		return nil, fmt.Errorf("error marshaling request body: %w", err)
	}

	req, err := http.NewRequest(http.MethodPost, requestURL, strings.NewReader(string(jsonBody)))
	if err != nil {
		return nil, fmt.Errorf("error creating request: %w", err)
	}

	// Set required headers
	req.Header.Set("Content-Type", "application/json")

	// Create HTTP client with timeout
	client := &http.Client{
		Timeout: c.Timeout,
	}

	// Execute the HTTP request
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("error executing request: %w", err)
	}
	defer resp.Body.Close()

	// Read the response body
	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("error reading response body: %w", err)
	}

	// Always log the response for debugging
	if c.Debug || resp.StatusCode != http.StatusOK {
		fmt.Printf("Response from POST %s (HTTP %d): %s\n",
			apiPath, resp.StatusCode, string(responseBody))
	}

	// Create a response object that will be populated with token data
	apiResponse := &APIResponse{}

	// Shopee auth endpoints don't use the standard response format
	// They return the token data directly in the root of the JSON
	// First try to parse it into a temporary structure
	var directResponse struct {
		AccessToken  string `json:"access_token"`
		RefreshToken string `json:"refresh_token"`
		ExpireIn     int    `json:"expire_in"`
		RequestID    string `json:"request_id"`
		Error        string `json:"error"`
		Message      string `json:"message"`
		ShopID       int64  `json:"shop_id,omitempty"`
		MerchantID   int64  `json:"merchant_id,omitempty"`
	}

	if err := json.Unmarshal(responseBody, &directResponse); err != nil {
		return nil, fmt.Errorf("error parsing token response: %w", err)
	}

	// Check for API-level errors
	if directResponse.Error != "" {
		apiResponse.Error = directResponse.Error
		apiResponse.Message = directResponse.Message
		apiResponse.RequestID = directResponse.RequestID
		return apiResponse, fmt.Errorf("API error: %s - %s", directResponse.Error, directResponse.Message)
	}

	// Convert the direct response to our standard APIResponse format
	// The token data becomes the Response field
	tokenData, err := json.Marshal(directResponse)
	if err != nil {
		return nil, fmt.Errorf("error marshaling token data: %w", err)
	}

	apiResponse.RequestID = directResponse.RequestID
	apiResponse.Error = directResponse.Error
	apiResponse.Message = directResponse.Message
	apiResponse.Response = tokenData

	fmt.Print("Received response from Shopee API: ", string(apiResponse.Response), "\n")

	return apiResponse, nil
}

// GenerateShopAuthURL creates an authentication URL for Shopee's Shop Authorization API
// following Shopee's API authentication requirements.
func (c *ShopeeConfig) GenerateShopAuthURL(redirectURL string) (string, error) {
	// Validate required parameters
	if c.PartnerID <= 0 {
		return "", fmt.Errorf("invalid partner_id: must be greater than 0")
	}

	if c.PartnerKey == "" {
		return "", fmt.Errorf("partner_key cannot be empty")
	}

	if c.Host == "" {
		return "", fmt.Errorf("host URL cannot be empty")
	}

	if redirectURL == "" {
		return "", fmt.Errorf("redirect URL cannot be empty")
	}

	// Define the API path for shop authorization
	apiPath := "/api/v2/shop/auth_partner"

	// Get current Unix timestamp
	timestamp := time.Now().Unix()

	// For Public APIs, the sign base string format is: partner_id + api_path + timestamp
	baseString := fmt.Sprintf("%d%s%d", c.PartnerID, apiPath, timestamp)

	// Create HMAC-SHA256 hasher with the partner key as encryption key
	h := hmac.New(sha256.New, []byte(c.PartnerKey))

	// Write the base string to the hasher
	h.Write([]byte(baseString))

	// Generate the signature as lowercase hexadecimal string
	sign := hex.EncodeToString(h.Sum(nil))

	// Construct the final URL with all required parameters
	url := fmt.Sprintf("%s%s?partner_id=%d&timestamp=%d&sign=%s&redirect=%s",
		c.Host, apiPath, c.PartnerID, timestamp, sign, url.QueryEscape(redirectURL))

	// Log the generated URL if in debug mode
	if c.Debug {
		fmt.Printf("Debug: Generated Shopee Auth URL: %s\n", url)
	}

	return url, nil
}

// GenerateMerchantAuthURL creates an authentication URL for Shopee's Merchant Authorization API
// This is used for cross-border sellers who need merchant-level authorization.
func (c *ShopeeConfig) GenerateMerchantAuthURL(redirectURL string) (string, error) {
	// Validate required parameters
	if c.PartnerID <= 0 {
		return "", fmt.Errorf("invalid partner_id: must be greater than 0")
	}

	if c.PartnerKey == "" {
		return "", fmt.Errorf("partner_key cannot be empty")
	}

	if c.Host == "" {
		return "", fmt.Errorf("host URL cannot be empty")
	}

	if redirectURL == "" {
		return "", fmt.Errorf("redirect URL cannot be empty")
	}

	// Define the API path for merchant authorization
	apiPath := "/api/v2/merchant/auth_partner"

	// Get current Unix timestamp
	timestamp := time.Now().Unix()

	// For Public APIs, the sign base string format is: partner_id + api_path + timestamp
	baseString := fmt.Sprintf("%d%s%d", c.PartnerID, apiPath, timestamp)

	// Create HMAC-SHA256 hasher with the partner key as encryption key
	h := hmac.New(sha256.New, []byte(c.PartnerKey))

	// Write the base string to the hasher
	h.Write([]byte(baseString))

	// Generate the signature as lowercase hexadecimal string
	sign := hex.EncodeToString(h.Sum(nil))

	// Construct the final URL with all required parameters
	url := fmt.Sprintf("%s%s?partner_id=%d&timestamp=%d&sign=%s&redirect=%s",
		c.Host, apiPath, c.PartnerID, timestamp, sign, url.QueryEscape(redirectURL))

	// Log the generated URL if in debug mode
	if c.Debug {
		fmt.Printf("Debug: Generated Shopee Merchant Auth URL: %s\n", url)
	}

	return url, nil
}
