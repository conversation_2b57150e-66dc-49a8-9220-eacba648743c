// Package shopee_ecom provides implementation for Shopee E-commerce platform integration
package shopee_ecom

import (
	"fmt"
	"os"
	"strings"
)

// AppType represents the type of Shopee Open Platform application
type AppType string

// Define supported Shopee Open Platform app types
const (
	ERPSystem         AppType = "ERP_SYSTEM"
	ProductManagement AppType = "PRODUCT_MANAGEMENT"
	OrderManagement   AppType = "ORDER_MANAGEMENT"
	Accounting        AppType = "ACCOUNTING_FINANCE"
	// Marketing         AppType = "MARKETING"
	Logistic   AppType = "LOGISTIC"
	DefaultApp AppType = ERPSystem // Default to ERP System if not specified
)

// Environment type for determining which credentials to use
type Environment string

// Define supported environments
const (
	Development Environment = "development"
	Production  Environment = "production"
)

// AppConfig holds configuration for a specific app type
type AppConfig struct {
	PartnerID  int64
	PartnerKey string
	AppName    string
}

// EnvironmentConfig maps environments to hosts
var EnvironmentConfig = map[Environment]string{
	Development: APISandbox,
	Production:  APIHostVN,
}

// appConfigurations stores Partner ID and Key for each app type and environment
var appConfigurations = map[Environment]map[AppType]AppConfig{
	Development: {
		ERPSystem: {
			PartnerID:  1159728,
			PartnerKey: "47476f6870636d57475a76724b7a49544d6f42727258667a786e6c7750686e55",
			AppName:    "ERP System (Dev)",
		},
		ProductManagement: {
			PartnerID:  1279112,
			PartnerKey: "4762516458754149595a765868766369516c6561566c5841736a6c4276665745",
			AppName:    "Product Management (Dev)",
		},
		OrderManagement: {
			PartnerID:  1279111,
			PartnerKey: "716a774b5a51464a6a6a584d5248474c48676a56566c564d7951585054555a45",
			AppName:    "Order Management (Dev)",
		},
		Accounting: {
			PartnerID:  1279113,
			PartnerKey: "53575852674d4a6e6a51647a44547565537376626f496a76694b515770464474",
			AppName:    "Accounting and Finance (Dev)",
		},
		Logistic: {
			PartnerID:  1279114,
			PartnerKey: "53445254626b665648597a764b50557058427372735673456248797459784773",
			AppName:    "Logistic (Dev)",
		},
	},
	Production: {
		ERPSystem: {
			PartnerID:  98765,
			PartnerKey: "prod-erp-partner-key",
			AppName:    "ERP System",
		},
		ProductManagement: {
			PartnerID:  98764,
			PartnerKey: "prod-product-partner-key",
			AppName:    "Product Management",
		},
		OrderManagement: {
			PartnerID:  98763,
			PartnerKey: "prod-order-partner-key",
			AppName:    "Order Management",
		},
		Accounting: {
			PartnerID:  98762,
			PartnerKey: "prod-accounting-partner-key",
			AppName:    "Accounting and Finance",
		},
		Logistic: {
			PartnerID:  98761,
			PartnerKey: "prod-logistic-partner-key",
			AppName:    "Logistic",
		},
	},
}

// GetAppConfig retrieves the configuration for a specific app type in the current environment
func GetAppConfig(appType AppType, env Environment) (AppConfig, error) {
	// Check if environment exists in configurations
	envConfig, envExists := appConfigurations[env]
	if !envExists {
		return AppConfig{}, fmt.Errorf("environment %s not configured", env)
	}

	// Check if app type exists for the environment
	config, appExists := envConfig[appType]
	if !appExists {
		return AppConfig{}, fmt.Errorf("app type %s not configured for environment %s", appType, env)
	}

	return config, nil
}

// GetEnvironment determines the current environment based on ENV variable
// Explicitly returns Development as the default if not set or not matching Production
func GetEnvironment() Environment {
	// Check if SHOPEE_ENV is set to production values
	env := strings.ToLower(os.Getenv("SHOPEE_ENV"))
	if env == "production" || env == "prod" {
		return Production
	}
	// Default to Development environment
	return Development
}

// GetAppTypeFromString converts a string to an AppType
func GetAppTypeFromString(appTypeStr string) AppType {
	switch strings.ToUpper(appTypeStr) {
	case string(ERPSystem):
		return ERPSystem
	case string(ProductManagement):
		return ProductManagement
	case string(OrderManagement):
		return OrderManagement
	case string(Accounting):
		return Accounting
	case string(Logistic):
		return Logistic
	default:
		return DefaultApp
	}
}
