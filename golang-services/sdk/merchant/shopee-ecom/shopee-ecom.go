// Package shopee_ecom provides implementation for Shopee E-commerce platform integration
package shopee_ecom

import (
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"regexp"
	"strconv"
	"time"

	"crypto/sha256"
	"encoding/hex"

	"github.com/golang-module/carbon"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"
	"github.com/tidwall/gjson"
)

// ShopeeEcomClient implements the IMerchant interface for Shopee E-commerce platform
type ShopeeEcomClient struct {
	httpClient *http.Client
	config     *ShopeeConfig
	appType    AppType
	env        Environment
}

// NewShopeeEcomClient creates a new instance of ShopeeEcomClient with default settings (ERP System in current environment)
func NewShopeeEcomClient() *ShopeeEcomClient {
	return NewShopeeEcomClientWithType(DefaultApp)
}

// NewShopeeEcomClientWithType creates a new instance of ShopeeEcomClient for a specific app type
func NewShopeeEcomClientWithType(appType AppType) *ShopeeEcomClient {
	// Get current environment (development/production)
	env := GetEnvironment()

	// Get app configuration for the specified type and environment
	appConfig, err := GetAppConfig(appType, env)
	if err != nil {
		// Fallback to default configuration if app type not found
		appConfig = AppConfig{
			PartnerID:  12345, // Fallback Partner ID
			PartnerKey: "default-partner-key",
		}
	}

	// Create base config with partner ID and key from app config
	baseConfig := NewShopeeConfig(appConfig.PartnerID, appConfig.PartnerKey)

	// Set host based on environment
	if host, ok := EnvironmentConfig[env]; ok {
		baseConfig.Host = host
	}

	return &ShopeeEcomClient{
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
		config:  baseConfig,
		appType: appType,
		env:     env,
	}
}

// GetCurrentAppConfig returns the current app configuration
func (c *ShopeeEcomClient) GetCurrentAppConfig() (AppConfig, error) {
	return GetAppConfig(c.appType, c.env)
}

// SetAppType changes the client's app type and updates config
func (c *ShopeeEcomClient) SetAppType(appType AppType) error {
	appConfig, err := GetAppConfig(appType, c.env)
	if err != nil {
		return err
	}

	// Update client's app type
	c.appType = appType

	// Update config with new app's partner ID and key
	c.config.PartnerID = appConfig.PartnerID
	c.config.PartnerKey = appConfig.PartnerKey

	return nil
}

// skipCallAPI determines if an API call should be skipped based on time constraints
func skipCallAPI() bool {
	vietnamTime := carbon.Now().SetTimezone("Asia/Ho_Chi_Minh")
	hour := vietnamTime.Hour()
	// API might have maintenance time or usage restrictions
	return hour >= 1 && hour <= 4 // Restrict calls from 1 AM to 4 AM
}

// GetToken authenticates and retrieves a token from Shopee Ecom API
func (c *ShopeeEcomClient) GetToken(token *models.Token) error {
	// Initialize config from the provided token
	if err := c.config.InitFromToken(token); err != nil {
		return err
	}

	// If we already have a refresh token in the token.RefreshToken field, use it
	if token.RefreshToken != "" {
		// Attempt to refresh the token
		response, err := c.config.AuthenticateToken("", token.RefreshToken)
		if err != nil {
			return fmt.Errorf("failed to refresh token: %w", err)
		}

		// Parse the response
		var tokenData struct {
			AccessToken  string `json:"access_token"`
			RefreshToken string `json:"refresh_token"`
			ExpireIn     int    `json:"expire_in"`
		}

		if err := json.Unmarshal(response.Response, &tokenData); err != nil {
			return fmt.Errorf("error parsing token response: %w", err)
		}

		// Update the token with new values
		token.AccessToken = tokenData.AccessToken
		token.RefreshToken = tokenData.RefreshToken

		// Store expiration info in params if provided
		if token.Params == nil {
			token.Params = make(map[string]string)
		}
		token.Params["expire_in"] = strconv.Itoa(tokenData.ExpireIn)
		token.Params["updated_at"] = strconv.FormatInt(time.Now().Unix(), 10)

		return nil
	}

	// If we have an auth code in token.Params, use it to get a new token
	if authCode, ok := token.Params["code"]; ok && authCode != "" {
		// Exchange auth code for token
		response, err := c.config.AuthenticateToken(authCode, "")
		if err != nil {
			return fmt.Errorf("failed to exchange auth code for token: %w", err)
		}

		// Parse the response
		var tokenData struct {
			AccessToken  string `json:"access_token"`
			RefreshToken string `json:"refresh_token"`
			ExpireIn     int    `json:"expire_in"`
		}

		if err := json.Unmarshal(response.Response, &tokenData); err != nil {
			return fmt.Errorf("error parsing token response: %w", err)
		}

		// Update the token
		token.AccessToken = tokenData.AccessToken
		token.RefreshToken = tokenData.RefreshToken

		// Store expiration info in params
		if token.Params == nil {
			token.Params = make(map[string]string)
		}
		token.Params["expire_in"] = strconv.Itoa(tokenData.ExpireIn)
		token.Params["updated_at"] = strconv.FormatInt(time.Now().Unix(), 10)

		return nil
	}

	// If no refresh token or auth code, we need to initiate the authorization flow
	return fmt.Errorf("neither refresh token nor auth code provided - call GenerateAuthURL first to initiate authorization flow")
}

// GenerateAuthURL creates the URL for initiating the authorization process with Shopee
// The redirectURL is where Shopee will redirect after successful authorization
// The state parameter can be used to maintain state between the request and callback
func (c *ShopeeEcomClient) GenerateAuthURL(redirectURL, state string) (string, error) {
	// Convert partner ID to string
	partnerId := strconv.FormatInt(c.config.PartnerID, 10)

	// Log parameters for debugging
	fmt.Printf("Debug: Generating Shopee auth URL\n")
	fmt.Printf("Debug: Partner ID: %s\n", partnerId)
	fmt.Printf("Debug: Redirect URL: %s\n", redirectURL)

	// Build the authorization URL according to Shopee's sandbox format
	authURL := fmt.Sprintf("%s/auth?auth_type=seller&partner_id=%s&redirect_uri=%s&response_type=code",
		"https://open.sandbox.test-stable.shopee.com",
		partnerId,
		url.QueryEscape(redirectURL))

	// Add state parameter if provided
	if state != "" {
		authURL = fmt.Sprintf("%s&state=%s", authURL, url.QueryEscape(state))
	}

	// Log final URL
	fmt.Printf("Debug: Generated auth URL: %s\n", authURL)

	return authURL, nil
}

// AuthorizeWithCode exchanges an authorization code for access and refresh tokens
// and updates the provided token with the new credentials
func (c *ShopeeEcomClient) AuthorizeWithCode(token *models.Token, authCode string, shopID int64) error {
	// Validate input parameters
	if token == nil {
		return fmt.Errorf("token cannot be nil")
	}

	if authCode == "" {
		return fmt.Errorf("authorization code cannot be empty")
	}

	if shopID <= 0 {
		return fmt.Errorf("invalid shop ID: %d", shopID)
	}

	// Save shop ID to config for this request
	c.config.ShopID = shopID

	// Enable debug mode temporarily to help diagnose issues
	// previousDebug := c.config.Debug
	c.config.Debug = true

	// Log the authorization attempt
	fmt.Printf("Authorizing with Shopee using code: %s, shop ID: %d, partner ID: %d\n",
		authCode[:5]+"...", shopID, c.config.PartnerID)

	// Exchange the auth code for access and refresh tokens
	// This uses the AuthenticateToken function which handles the token exchange
	response, err := c.config.AuthenticateToken(authCode, "")

	// Restore previous debug setting
	// c.config.Debug = previousDebug

	// Handle errors from the token exchange
	if err != nil {
		fmt.Printf("Error exchanging auth code for token: %v\n", err)
		return fmt.Errorf("failed to exchange auth code for token: %w", err)
	}

	// Check if we have a valid response
	fmt.Print("Received response from Shopee API: ", string(response.Response), "\n")
	if response == nil || response.Response == nil {
		return fmt.Errorf("empty response from Shopee API")
	}

	// Parse the response into a structured format
	var tokenData struct {
		AccessToken  string `json:"access_token"`
		RefreshToken string `json:"refresh_token"`
		ExpireIn     int    `json:"expire_in"`
		ShopID       int64  `json:"shop_id"`
		MerchantID   int64  `json:"merchant_id,omitempty"`
	}

	// Unmarshal the JSON response into our struct
	if err := json.Unmarshal(response.Response, &tokenData); err != nil {
		return fmt.Errorf("error parsing token data: %w", err)
	}

	// Validate the token data
	if tokenData.AccessToken == "" {
		return fmt.Errorf("received empty access token from Shopee API")
	}

	// Update the token object with the new credentials
	token.AccessToken = tokenData.AccessToken
	token.RefreshToken = tokenData.RefreshToken
	token.SiteID = strconv.FormatInt(tokenData.ShopID, 10)
	// token.ExpiredIn = time.Now().Add(time.Duration(tokenData.ExpireIn) * time.Second)

	// Store additional parameters in the token's Params map
	if token.Params == nil {
		token.Params = make(map[string]string)
	}

	// Save expire_in as a parameter
	token.Params["expire_in"] = strconv.Itoa(tokenData.ExpireIn)

	// If merchant ID is provided, save it as well
	if tokenData.MerchantID > 0 {
		token.Params["merchant_id"] = strconv.FormatInt(tokenData.MerchantID, 10)
	}

	return nil
}

// GetStoreListByAuth retrieves a list of stores after authentication
func (c *ShopeeEcomClient) GetStoreListByAuth(token *models.Token) ([]models.StoreItem, error) {
	// Initialize config from the provided token
	if err := c.config.InitFromToken(token); err != nil {
		return nil, err
	}

	// Skip call during maintenance hours
	if skipCallAPI() {
		return []models.StoreItem{}, nil
	}

	// API path for getting shop info
	apiPath := "/api/v2/shop/get_shop_info"

	// Make the API request
	response, err := c.config.DoRequest(http.MethodGet, apiPath, ShopAPI, nil, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to get shop info: %w", err)
	}

	// Parse shop info response
	var shopInfo struct {
		ShopName       string `json:"shop_name"`
		Region         string `json:"region"`
		Status         string `json:"status"`
		Description    string `json:"description"`
		IsOfficialShop bool   `json:"is_official_shop"`
	}

	if err := json.Unmarshal(response.Response, &shopInfo); err != nil {
		return nil, fmt.Errorf("error parsing shop info: %w", err)
	}

	// Create store item from shop info
	storeItem := models.StoreItem{
		AccessToken: token.AccessToken,
		StoreType:   "MART",
		StoreID:     token.SiteID,
		StoreName:   shopInfo.ShopName,
	}

	return []models.StoreItem{storeItem}, nil
}

// GetStore retrieves detailed information about a specific store
func (c *ShopeeEcomClient) GetStore(token *models.Token) (*models.StoreDetail, error) {
	// Initialize config from the provided token
	if err := c.config.InitFromToken(token); err != nil {
		return nil, err
	}

	// Skip call during maintenance hours
	if skipCallAPI() {
		return &models.StoreDetail{}, nil
	}

	// API path for getting shop info
	apiPath := "/api/v2/shop/get_shop_info"

	// Make the API request
	response, err := c.config.DoRequest(http.MethodGet, apiPath, ShopAPI, nil, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to get shop info: %w", err)
	}

	// Parse shop info response
	var shopInfo struct {
		ShopName       string `json:"shop_name"`
		Region         string `json:"region"`
		Status         string `json:"status"`
		Description    string `json:"description"`
		IsOfficialShop bool   `json:"is_official_shop"`
		Address        string `json:"address,omitempty"` // May not be available in all responses
		Phone          string `json:"phone,omitempty"`   // May not be available in all responses
	}

	if err := json.Unmarshal(response.Response, &shopInfo); err != nil {
		return nil, fmt.Errorf("error parsing shop info: %w", err)
	}

	// Get additional contact information if needed
	// For Shopee Ecom, we might need to make another API call to get complete address and phone
	// This is a placeholder for that additional API call if needed

	// Build address string - in real implementation, you might get this from the response
	address := shopInfo.Address
	if address == "" && shopInfo.Region != "" {
		// If no address in response but we have region, use that as minimal address info
		address = "Region: " + shopInfo.Region
	}

	// Extract store details according to the standard StoreDetail model
	return &models.StoreDetail{
		ID:      token.SiteID,
		Name:    shopInfo.ShopName,
		Phone:   shopInfo.Phone,
		Address: address,
		Raw:     shopInfo, // Store the complete response data for additional fields
	}, nil
}

// GetStoreList retrieves a list of all stores for a merchant
func (c *ShopeeEcomClient) GetStoreList(token *models.Token) ([]any, error) {
	// Initialize config from the provided token
	if err := c.config.InitFromToken(token); err != nil {
		return nil, err
	}

	// Skip call during maintenance hours
	if skipCallAPI() {
		return []any{}, nil
	}

	// API path for getting shops by partner
	apiPath := "/api/v2/public/get_shops_by_partner"

	// Make the API request
	response, err := c.config.DoRequest(http.MethodGet, apiPath, PublicAPI, nil, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to get shops by partner: %w", err)
	}

	// Parse the response
	var shopsData struct {
		More       bool   `json:"more"`
		NextCursor string `json:"next_cursor"`
		Shops      []struct {
			ShopID       int64  `json:"shop_id"`
			ShopName     string `json:"shop_name"`
			Region       string `json:"region"`
			AuthTime     int64  `json:"auth_time"`
			ExpireTime   int64  `json:"expire_time"`
			IsCrossBoard bool   `json:"is_cross_board"`
		} `json:"shops"`
	}

	if err := json.Unmarshal(response.Response, &shopsData); err != nil {
		return nil, fmt.Errorf("error parsing shops data: %w", err)
	}

	// Convert to a generic slice
	shops := make([]any, 0, len(shopsData.Shops))
	for _, shop := range shopsData.Shops {
		shops = append(shops, map[string]interface{}{
			"shop_id":   strconv.FormatInt(shop.ShopID, 10),
			"shop_name": shop.ShopName,
			"region":    shop.Region,
		})
	}

	return shops, nil
}

// GetOrderListV2 retrieves the list of orders for real-time monitoring within the current day
func (c *ShopeeEcomClient) GetOrderListV2(auth *models.Token) (map[string][]models.MerchantOrder, error) {
	// Initialize config from the provided token
	if err := c.config.InitFromToken(auth); err != nil {
		return nil, err
	}

	// Skip call during maintenance hours
	if skipCallAPI() {
		return map[string][]models.MerchantOrder{}, nil
	}

	// API path for getting order list
	apiPath := "/api/v2/order/get_order_list"

	// Set up query parameters for current day only
	location, _ := time.LoadLocation("Asia/Ho_Chi_Minh")
	now := time.Now().In(location)
	startTime := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, location)
	endTime := now

	// Convert time to Unix timestamps
	timeFrom := startTime.Unix()
	timeTo := endTime.Unix()

	// Set up query parameters
	queryParams := map[string]string{
		"time_range_field": "create_time", // Options: create_time, update_time
		"time_from":        strconv.FormatInt(timeFrom, 10),
		"time_to":          strconv.FormatInt(timeTo, 10),
		"page_size":        "50", // Maximum allowed by Shopee API
	}

	// Make the API request
	response, err := c.config.DoRequest(http.MethodGet, apiPath, ShopAPI, queryParams, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to get order list: %w", err)
	}

	// Parse the response
	var orderListData struct {
		More      bool `json:"more"`
		OrderList []struct {
			OrderSN     string `json:"order_sn"`
			OrderStatus string `json:"order_status"`
			UpdateTime  int64  `json:"update_time"`
			CreateTime  int64  `json:"create_time"`
		} `json:"order_list"`
		NextCursor string `json:"next_cursor"`
	}

	if err := json.Unmarshal(response.Response, &orderListData); err != nil {
		return nil, fmt.Errorf("error parsing order list: %w", err)
	}

	// Create result map with statuses as keys
	result := make(map[string][]models.MerchantOrder)

	// Map Shopee order statuses to our system statuses
	statusMap := map[string]string{
		"UNPAID":             "PENDING", // Matches with 'Unpaid' in Seller Centre
		"READY_TO_SHIP":      "DOING",   // Matches with 'To ship (To ship)'
		"RETRY_SHIP":         "DOING",   // Matches with 'To ship (To ship)'
		"IN_CANCEL":          "CANCEL",  // Matches with 'Cancellation (Cancellation Requested)'
		"CANCELLED":          "CANCEL",  // Matches with 'Cancellation(Cancelled)'
		"PROCESSED":          "DOING",   // Matches with 'To ship (Processed)'
		"SHIPPED":            "PICK",    // Matches with 'Shipping (Shipped)'
		"TO_RETURN":          "CANCEL",  // Matches with 'Return/Refund (Return Pending)'
		"TO_CONFIRM_RECEIVE": "PICK",    // Matches with 'Shipping(Shipped)'
		"COMPLETED":          "FINISH",  // Matches with 'Completed...'
	}

	// Process each order and categorize by status
	for _, order := range orderListData.OrderList {
		// Map Shopee status to our status
		status, exists := statusMap[order.OrderStatus]
		if !exists {
			status = "PENDING" // Default to PENDING if status is unknown
		}

		// Create merchant order object
		merchantOrder := models.MerchantOrder{
			LongOrderID:  order.OrderSN,
			ShortOrderID: order.OrderSN,
			Source:       "shopee_ecom",
			ID:           order.OrderSN,
			Status:       status,
			CreatedAt:    time.Unix(order.CreateTime, 0),
			UpdatedAt:    time.Unix(order.UpdateTime, 0),
			SourceData:   order,
		}

		// Add to result map
		if _, ok := result[status]; !ok {
			result[status] = []models.MerchantOrder{}
		}
		result[status] = append(result[status], merchantOrder)
	}

	return result, nil
}

// GetOrderListByDuration retrieves historical orders within a specific time range,
// focusing on completed and cancelled orders with full pagination support
func (c *ShopeeEcomClient) GetOrderListByDuration(auth *models.Token, startTime, endTime time.Time) (map[string][]models.MerchantOrder, error) {
	// Initialize config from the provided token
	if err := c.config.InitFromToken(auth); err != nil {
		return nil, err
	}

	// Skip call during maintenance hours
	if skipCallAPI() {
		return map[string][]models.MerchantOrder{}, nil
	}

	// Initialize result map with only FINISH and CANCEL categories
	result := map[string][]models.MerchantOrder{
		"FINISH": {},
		"CANCEL": {},
	}

	// API path for getting order list
	apiPath := "/api/v2/order/get_order_list"

	// Convert time to Unix timestamps
	timeFrom := startTime.Unix()
	timeTo := endTime.Unix()

	// Set up initial query parameters
	queryParams := map[string]string{
		"time_range_field": "create_time", // Options: create_time, update_time
		"time_from":        strconv.FormatInt(timeFrom, 10),
		"time_to":          strconv.FormatInt(timeTo, 10),
		"page_size":        "50", // Maximum allowed by Shopee API
	}

	var allOrders []struct {
		OrderSN     string `json:"order_sn"`
		OrderStatus string `json:"order_status"`
		UpdateTime  int64  `json:"update_time"`
		CreateTime  int64  `json:"create_time"`
	}

	// Implement pagination loop to get all historical orders
	for {
		// Make the API request
		response, err := c.config.DoRequest(http.MethodGet, apiPath, ShopAPI, queryParams, nil)
		if err != nil {
			return nil, fmt.Errorf("failed to get order list: %w", err)
		}

		// Parse the response
		var orderListData struct {
			More      bool `json:"more"`
			OrderList []struct {
				OrderSN     string `json:"order_sn"`
				OrderStatus string `json:"order_status"`
				UpdateTime  int64  `json:"update_time"`
				CreateTime  int64  `json:"create_time"`
			} `json:"order_list"`
			NextCursor string `json:"next_cursor"`
		}

		if err := json.Unmarshal(response.Response, &orderListData); err != nil {
			return nil, fmt.Errorf("error parsing order list: %w", err)
		}

		// Add orders to the collection
		allOrders = append(allOrders, orderListData.OrderList...)

		// Check if there are more pages
		if !orderListData.More {
			break
		}

		// Update cursor for next page
		queryParams["cursor"] = orderListData.NextCursor
	}

	// Process collected orders, focusing only on FINISH and CANCEL statuses
	for _, order := range allOrders {
		var status string

		// Map only completed and cancelled orders
		switch order.OrderStatus {
		case "COMPLETED":
			status = "FINISH"
		case "CANCELLED", "IN_CANCEL", "TO_RETURN":
			status = "CANCEL"
		default:
			continue // Skip other statuses
		}

		// Create merchant order object
		merchantOrder := models.MerchantOrder{
			ID:           order.OrderSN,
			LongOrderID:  order.OrderSN,
			ShortOrderID: order.OrderSN,
			Source:       "shopee_ecom",
			Status:       status,
			CreatedAt:    time.Unix(order.CreateTime, 0),
			UpdatedAt:    time.Unix(order.UpdateTime, 0),
			SourceData:   order,
		}

		// Add to appropriate category in result map
		result[status] = append(result[status], merchantOrder)
	}

	return result, nil
}

// OrderDetailResponse represents the response from Shopee's get_order_detail API
type OrderDetailResponse struct {
	RequestID string        `json:"request_id"`
	Error     string        `json:"error"`
	OrderList []interface{} `json:"order_list"`
}

// OrderResponse represents the response field in OrderDetailResponse
type OrderResponse struct {
	OrderList []interface{} `json:"order_list"` // Changed from []Order to []interface{} to accept any order structure
}

// GetOrderDetail retrieves detailed information about specific orders
func (c *ShopeeEcomClient) GetOrderDetail(auth *models.Token, orderSN string) (interface{}, error) {
	// Initialize config from the provided token
	if err := c.config.InitFromToken(auth); err != nil {
		return nil, fmt.Errorf("failed to initialize config: %w", err)
	}

	// Skip call during maintenance hours
	if skipCallAPI() {
		return nil, nil
	}

	// API path for getting order details
	apiPath := "/api/v2/order/get_order_detail"

	// Get current timestamp
	timestamp := time.Now().Unix()

	// Convert PartnerID and ShopID to strings
	partnerID := strconv.FormatInt(c.config.PartnerID, 10)
	shopID := strconv.FormatInt(c.config.ShopID, 10)

	// Additional query parameters
	queryParams := map[string]string{
		"access_token":                 auth.AccessToken,
		"partner_id":                   partnerID,
		"shop_id":                      shopID,
		"timestamp":                    fmt.Sprintf("%d", timestamp),
		"order_sn_list":                orderSN,
		"response_optional_fields":     "buyer_user_id,buyer_username,estimated_shipping_fee,recipient_address,actual_shipping_fee%20,goods_to_declare,note,note_update_time,item_list,pay_time,dropshipper,split_up,buyer_cancel_reason,cancel_by,cancel_reason,actual_shipping_fee_confirmed,buyer_cpf_id,fulfillment_flag,pickup_done_time,package_list,shipping_carrier,payment_method,total_amount,buyer_username,invoice_data,order_chargeable_weight_gram,return_request_due_date,edt",
		"request_order_status_pending": "true",
	}

	// Calculate signature based on Shopee API v2 requirements
	baseString := fmt.Sprintf("%s%s%d%s%s%s",
		c.config.PartnerKey,
		apiPath,
		timestamp,
		partnerID,
		shopID,
		orderSN,
	)
	hash := sha256.New()
	hash.Write([]byte(baseString))
	queryParams["sign"] = hex.EncodeToString(hash.Sum(nil))

	// Make the API request
	response, err := c.config.DoRequest(http.MethodGet, apiPath, ShopAPI, queryParams, nil)
	if err != nil {
		fmt.Printf("Error making request to Shopee API: %v\n", err)
		return nil, fmt.Errorf("failed to get order details: %w", err)
	}

	// Check if response is valid
	if response == nil || response.Response == nil {
		return nil, fmt.Errorf("empty response from Shopee API")
	}

	// Log response if in debug mode
	fmt.Printf("Debug: Received response from Shopee API: %s\n", string(response.Response))

	// Parse the response
	var orderDetailResponse OrderDetailResponse
	if err := json.Unmarshal(response.Response, &orderDetailResponse); err != nil {
		return nil, fmt.Errorf("error parsing order detail response: %w", err)
	}

	// Check if we have order details
	if len(orderDetailResponse.OrderList) == 0 {
		return nil, fmt.Errorf("no order details found for order SN: %s", orderSN)
	}

	// Return the first order detail
	return &orderDetailResponse.OrderList[0], nil
}

// ConfirmOrder confirms an order in the Shopee Ecom platform
func (c *ShopeeEcomClient) ConfirmOrder(auth *models.Token, orderID string) error {
	// Initialize config from the provided token
	if err := c.config.InitFromToken(auth); err != nil {
		return err
	}

	// Skip call during maintenance hours
	if skipCallAPI() {
		return nil
	}

	// API path for accepting order
	apiPath := "/api/v2/order/accept"

	// Request body
	requestBody := map[string]interface{}{
		"order_sn": orderID,
	}

	// Make the API request
	response, err := c.config.DoRequest(http.MethodPost, apiPath, ShopAPI, nil, requestBody)
	if err != nil {
		return fmt.Errorf("failed to confirm order: %w", err)
	}

	// Check if the response indicates success
	if response.Error != "" {
		return fmt.Errorf("API error when confirming order: %s - %s", response.Error, response.Message)
	}

	return nil
}

// CancelOrder cancels an order in the Shopee Ecom platform
func (c *ShopeeEcomClient) CancelOrder(auth *models.Token, orderID string, cancelType string) error {
	// Initialize config from the provided token
	if err := c.config.InitFromToken(auth); err != nil {
		return err
	}

	// Skip call during maintenance hours
	if skipCallAPI() {
		return nil
	}

	// API path for canceling order
	apiPath := "/api/v2/order/cancel"

	// Map our cancel types to Shopee's cancel reasons
	cancelReasonMap := map[string]string{
		"OUT_OF_STOCK":           "OUT_OF_STOCK",
		"CUSTOMER_REQUEST":       "CUSTOMER_REQUEST",
		"UNDELIVERABLE_LOCATION": "UNDELIVERABLE_AREA",
		"COD_NOT_SUPPORTED":      "COD_NOT_SUPPORTED",
		"OTHER":                  "OTHER",
	}

	// Default to OTHER if cancel type is not recognized
	reason, exists := cancelReasonMap[cancelType]
	if !exists {
		reason = "OTHER"
	}

	// Request body
	requestBody := map[string]interface{}{
		"order_sn":       orderID,
		"cancel_reason":  reason,
		"item_list":      []interface{}{}, // Empty array means cancel the whole order
		"cancel_message": "Order canceled via API",
	}

	// Make the API request
	response, err := c.config.DoRequest(http.MethodPost, apiPath, ShopAPI, nil, requestBody)
	if err != nil {
		return fmt.Errorf("failed to cancel order: %w", err)
	}

	// Check if the response indicates success
	if response.Error != "" {
		return fmt.Errorf("API error when canceling order: %s - %s", response.Error, response.Message)
	}

	return nil
}

// UpdateStoreStatus updates a store's operating status (open/closed)
func (c *ShopeeEcomClient) UpdateStoreStatus(auth *models.Token, status string, duration int) error {
	// Initialize config from the provided token
	if err := c.config.InitFromToken(auth); err != nil {
		return err
	}

	// Skip call during maintenance hours
	if skipCallAPI() {
		return nil
	}

	// Determine which API to call based on status
	var apiPath string
	var requestBody map[string]interface{}

	// Map our status values to Shopee's expectations
	if status == "open" {
		// API path for resuming store operations
		apiPath = "/api/v2/shop/resume"
		requestBody = map[string]interface{}{} // No specific parameters needed for resume
	} else if status == "close" {
		// API path for suspending store operations
		apiPath = "/api/v2/shop/suspend"

		// If duration is provided, add it to the request
		// Duration is in minutes, convert to seconds for API
		if duration > 0 {
			// Shopee allows suspending a shop up to 30 days (43200 minutes)
			if duration > 43200 {
				duration = 43200
			}

			requestBody = map[string]interface{}{
				"suspend_until": time.Now().Add(time.Duration(duration) * time.Minute).Unix(),
			}
		} else {
			// If no duration provided, suspend indefinitely
			requestBody = map[string]interface{}{}
		}
	} else {
		return fmt.Errorf("invalid status: %s, expected 'open' or 'close'", status)
	}

	// Make the API request
	response, err := c.config.DoRequest(http.MethodPost, apiPath, ShopAPI, nil, requestBody)
	if err != nil {
		return fmt.Errorf("failed to update store status: %w", err)
	}

	// Check if the response indicates success
	if response.Error != "" {
		return fmt.Errorf("API error when updating store status: %s - %s", response.Error, response.Message)
	}

	return nil
}

// GetOpenStatus checks if a store is currently open
func (c *ShopeeEcomClient) GetOpenStatus(auth *models.Token) (bool, error) {
	// Initialize config from the provided token
	if err := c.config.InitFromToken(auth); err != nil {
		return false, err
	}

	// Skip call during maintenance hours
	if skipCallAPI() {
		return false, nil
	}

	// API path for getting shop info
	apiPath := "/api/v2/shop/get_shop_info"

	// Make the API request
	response, err := c.config.DoRequest(http.MethodGet, apiPath, ShopAPI, nil, nil)
	if err != nil {
		return false, fmt.Errorf("failed to get shop info: %w", err)
	}

	// Parse shop info response
	var shopInfo struct {
		Status string `json:"status"`
	}

	if err := json.Unmarshal(response.Response, &shopInfo); err != nil {
		return false, fmt.Errorf("error parsing shop info: %w", err)
	}

	// Check if the shop is open based on status
	// Possible statuses: NORMAL, BANNED, FROZEN, DORMANT
	return shopInfo.Status == "NORMAL", nil
}

// ShopeeWorkingHour represents the structure of working hours in Shopee
type ShopeeWorkingHour struct {
	DaysOfWeek []int  `json:"days_of_week"` // 1-7 for Monday-Sunday
	OpenTime   string `json:"open_time"`    // Format: HH:MM (24-hour)
	CloseTime  string `json:"close_time"`   // Format: HH:MM (24-hour)
}

// GetOpeningHour retrieves a store's operating hours
func (c *ShopeeEcomClient) GetOpeningHour(auth *models.Token) (any, error) {
	// Initialize config from the provided token
	if err := c.config.InitFromToken(auth); err != nil {
		return nil, err
	}

	// Skip call during maintenance hours
	if skipCallAPI() {
		return nil, nil
	}

	// API path for getting shop working hours
	apiPath := "/api/v2/shop/get_shop_info"

	// Make the API request
	response, err := c.config.DoRequest(http.MethodGet, apiPath, ShopAPI, nil, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to get shop info: %w", err)
	}

	// Shopee doesn't provide a specific API for working hours, but they might be in shop info
	// Parse the response to extract working hours if available
	jsonResponse := gjson.ParseBytes(response.Response)

	// Check if working_hours field exists
	if !jsonResponse.Get("working_hours").Exists() {
		// If not available, return a default structure
		return []ShopeeWorkingHour{
			{
				DaysOfWeek: []int{1, 2, 3, 4, 5, 6, 7}, // All days
				OpenTime:   "00:00",
				CloseTime:  "23:59",
			},
		}, nil
	}

	// Parse working hours
	var workingHours []ShopeeWorkingHour
	if err := json.Unmarshal([]byte(jsonResponse.Get("working_hours").Raw), &workingHours); err != nil {
		return nil, fmt.Errorf("error parsing working hours: %w", err)
	}

	return workingHours, nil
}

// UpdateOpeningHour updates a store's operating hours
func (c *ShopeeEcomClient) UpdateOpeningHour(auth *models.Token, workingHours any) error {
	// Initialize config from the provided token
	if err := c.config.InitFromToken(auth); err != nil {
		return err
	}

	// Skip call during maintenance hours
	if skipCallAPI() {
		return nil
	}

	// Convert the input to our expected format
	var shopeeWorkingHours []ShopeeWorkingHour

	// Handle different input types
	switch hours := workingHours.(type) {
	case []ShopeeWorkingHour:
		shopeeWorkingHours = hours
	case []interface{}:
		// Try to convert from a generic interface array
		for _, hour := range hours {
			if hourMap, ok := hour.(map[string]interface{}); ok {
				workingHour := ShopeeWorkingHour{}

				// Extract days_of_week
				if daysRaw, ok := hourMap["days_of_week"].([]interface{}); ok {
					for _, day := range daysRaw {
						if dayInt, ok := day.(float64); ok {
							workingHour.DaysOfWeek = append(workingHour.DaysOfWeek, int(dayInt))
						}
					}
				}

				// Extract open_time
				if openTime, ok := hourMap["open_time"].(string); ok {
					workingHour.OpenTime = openTime
				}

				// Extract close_time
				if closeTime, ok := hourMap["close_time"].(string); ok {
					workingHour.CloseTime = closeTime
				}

				shopeeWorkingHours = append(shopeeWorkingHours, workingHour)
			}
		}
	default:
		return fmt.Errorf("unsupported working hours format")
	}

	// Validate working hours
	for _, hour := range shopeeWorkingHours {
		// Validate days of week
		for _, day := range hour.DaysOfWeek {
			if day < 1 || day > 7 {
				return fmt.Errorf("invalid day of week: %d, expected 1-7", day)
			}
		}

		// Validate time format (HH:MM)
		timeRegex := `^([01]\d|2[0-3]):([0-5]\d)$`
		if !regexp.MustCompile(timeRegex).MatchString(hour.OpenTime) {
			return fmt.Errorf("invalid open time format: %s, expected HH:MM", hour.OpenTime)
		}
		if !regexp.MustCompile(timeRegex).MatchString(hour.CloseTime) {
			return fmt.Errorf("invalid close time format: %s, expected HH:MM", hour.CloseTime)
		}
	}

	// API path for updating shop profile which includes working hours
	apiPath := "/api/v2/shop/update_profile"

	// Request body with working hours
	requestBody := map[string]interface{}{
		"working_hours": shopeeWorkingHours,
	}

	// Make the API request
	response, err := c.config.DoRequest(http.MethodPost, apiPath, ShopAPI, nil, requestBody)

	if err != nil {
		return fmt.Errorf("failed to update working hours: %w", err)
	}
	// Check if the response indicates success
	if response.Error != "" {
		return fmt.Errorf("API error when updating working hours: %s - %s", response.Error, response.Message)
	}
	return nil
}

// GetOrderFeedbacks retrieves order feedbacks from Shopee E-commerce
// Note: Shopee E-commerce platform may not have a direct feedback API
// This is a placeholder implementation that returns empty results
func (c *ShopeeEcomClient) GetOrderFeedbacks(auth *models.Token, limit int) ([]any, error) {
	// Initialize config from the provided token
	if err := c.config.InitFromToken(auth); err != nil {
		return nil, err
	}

	// Skip call during maintenance hours
	if skipCallAPI() {
		return []any{}, nil
	}

	// Shopee E-commerce doesn't have a direct feedback API in their public documentation
	// This would need to be implemented if such an API becomes available
	// For now, return empty results
	return []any{}, nil
}

// generateSignature creates a SHA256 hash of the input string
func (c *ShopeeEcomClient) generateSignature(input string) string {
	hash := sha256.New()
	hash.Write([]byte(input))
	return hex.EncodeToString(hash.Sum(nil))
}
