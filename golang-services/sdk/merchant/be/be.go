package be

import (
	"encoding/json"
	"fmt"
	"net/http"

	"strconv"
	"strings"
	"time"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/utils"

	"maps"

	"github.com/golang-module/carbon"
	"github.com/spf13/cast"
	"github.com/tidwall/gjson"
)

// BEMerchantClient implements the Client interface
type BEMerchantClient struct {
	httpClient *http.Client
}

// NewBEMerchantClient creates a new BEMerchantClient
func NewBEMerchantClient() *BEMerchantClient {
	return &BEMerchantClient{
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

// skipCallAPI checks if API should be skipped based on Vietnam time
func skipCallAPI() bool {
	vietnamTime := carbon.Now().SetTimezone("Asia/Bangkok")
	hour := vietnamTime.Hour()
	return hour >= 1 && hour <= 5
}

// baseHeaders generates the base headers for API requests
func baseHeaders() map[string]string {
	return map[string]string{
		"user-agent":   "Dart/3.2 (dart:io)",
		"content-type": "application/json",
		"device_type":  "0",
	}
}

// baseBody generates the base body for API requests
func baseBody(token *models.Token) (map[string]any, error) {
	if token.SiteID == "" {
		return nil, fmt.Errorf("site_id is required")
	}

	siteJ := gjson.Parse(token.SiteID)

	return map[string]any{
		"access_token":   token.AccessToken,
		"app_version":    "151",
		"device_token":   "",
		"operator_token": "",
		"locale":         "vi",
		"device_type":    0,
		"merchant_id":    siteJ.Get("merchant_id").Int(),
		"restaurant_id":  siteJ.Get("restaurant_id").Int(),
		"user_id":        siteJ.Get("user_id").Int(),
		// "vendor_id":      siteJ.Get("vendor_id").String(),
	}, nil
}

// GetToken implements the authentication flow
func (c BEMerchantClient) GetToken(token *models.Token) error {
	if token.Password == "" {
		return fmt.Errorf("password is required")
	}

	headers := baseHeaders()
	data, err := utils.DoRequest("POST", "https://gw.be.com.vn/api/v1/be-merchant-gateway/v2/merchant/login", headers, map[string]any{
		"operator_token": "",
		"device_type":    "0",
		"access_token":   "PENDING",
		"email":          token.Username,
		"password":       token.Password,
		"device_token":   "",
		"app_version":    "151",
		"locale":         "vi",
	})
	if err != nil {
		return err
	}

	accessToken := gjson.ParseBytes(data).Get("token").String()
	userID := gjson.ParseBytes(data).Get("user.user_id").String()

	if accessToken == "" || userID == "" {
		return fmt.Errorf("failed to get access token")
	}

	token.AccessToken = accessToken

	// Get user profiles
	data, err = utils.DoRequest("POST", "https://gw.be.com.vn/api/v1/be-merchant-gateway/v2/merchant/get_user_profiles", headers, map[string]any{
		"access_token": accessToken,
		"user_id":      userID,
	})
	if err != nil {
		return err
	}

	// Check if user is MERCHANT_OWNER
	roleNames := gjson.ParseBytes(data).Get("data.0.role_names").Array()
	isMerchantOwner := false
	for _, role := range roleNames {
		if role.String() == "MERCHANT_OWNER" {
			isMerchantOwner = true
			break
		}
	}

	if isMerchantOwner {
		merchantID := gjson.ParseBytes(data).Get("data.0.merchant_id").String()
		siteData := map[string]any{
			"merchant_id":   merchantID,
			"restaurant_id": nil,
			"user_id":       userID,
			"vendor_id":     nil,
			"role":          "MERCHANT_OWNER",
		}
		siteDataJSON, err := json.Marshal(siteData)
		if err != nil {
			return err
		}
		token.SiteID = string(siteDataJSON)
		return nil
	}

	// Get first store from profiles
	storeID := gjson.ParseBytes(data).Get("data.0.store_profiles.0.store_id").String()
	if storeID == "" {
		return fmt.Errorf("no store found")
	}

	// Get store details
	data, err = utils.DoRequest("POST", "https://gw.be.com.vn/api/v1/be-merchant-gateway/v2/merchant/store/get", headers, map[string]any{
		"access_token": accessToken,
		"store_id":     storeID,
		"user_id":      userID,
	})
	if err != nil {
		return err
	}

	// Set the site data
	merchantID := gjson.ParseBytes(data).Get("store.merchant_id").String()
	vendorID := gjson.ParseBytes(data).Get("store.vendor_id").String()
	siteName := gjson.ParseBytes(data).Get("store.store_name").String()

	siteData := map[string]any{
		"merchant_id":   merchantID,
		"restaurant_id": storeID,
		"user_id":       userID,
		"vendor_id":     vendorID,
	}
	siteDataJSON, err := json.Marshal(siteData)
	if err != nil {
		return err
	}

	token.SiteID = string(siteDataJSON)
	token.SiteName = siteName

	return nil
}

// GetStoreListByAuth authenticates and retrieves a list of stores
func (c BEMerchantClient) GetStoreListByAuth(token *models.Token) ([]models.StoreItem, error) {
	if token.Password == "" {
		return nil, fmt.Errorf("password is required")
	}

	headers := baseHeaders()
	// Login to get access token
	data, err := utils.DoRequest("POST", "https://gw.be.com.vn/api/v1/be-merchant-gateway/v2/merchant/login", headers, map[string]any{
		"operator_token": "",
		"device_type":    "0",
		"access_token":   "PENDING",
		"email":          token.Username,
		"password":       token.Password,
		"device_token":   "",
		"app_version":    "151",
		"locale":         "vi",
	})
	if err != nil {
		return nil, err
	}

	accessToken := gjson.ParseBytes(data).Get("token").String()
	userID := gjson.ParseBytes(data).Get("user.user_id").String()

	if accessToken == "" || userID == "" {
		return nil, fmt.Errorf("failed to get access token")
	}

	// Get user profiles to get store list
	data, err = utils.DoRequest("POST", "https://gw.be.com.vn/api/v1/be-merchant-gateway/v2/merchant/get_user_profiles", headers, map[string]any{
		"access_token": accessToken,
		"user_id":      userID,
	})
	if err != nil {
		return nil, err
	}

	result := []models.StoreItem{}

	// Process all merchants in the response
	merchants := gjson.ParseBytes(data).Get("data").Array()
	for _, merchant := range merchants {
		// Get all store profiles for this merchant
		storeProfiles := merchant.Get("store_profiles").Array()
		for _, storeProfile := range storeProfiles {
			storeID := storeProfile.Get("store_id").String()
			storeName := storeProfile.Get("store_name").String()

			siteData := map[string]any{
				"merchant_id":   merchant.Get("merchant_id").Int(),
				"restaurant_id": storeID,
				"user_id":       userID,
				"vendor_id":     0,
			}
			siteDataJSON, err := json.Marshal(siteData)
			if err != nil {
				continue // Skip this store if there's an error
			}

			result = append(result, models.StoreItem{
				AccessToken: accessToken,
				StoreType:   "FOOD",
				StoreID:     string(siteDataJSON),
				StoreName:   storeName,
			})
		}
	}

	return result, nil
}

// GetStore retrieves store information
func (c BEMerchantClient) GetStore(token *models.Token) (*models.StoreDetail, error) {
	if token.AccessToken == "" || token.SiteID == "" {
		return nil, nil
	}

	var siteData map[string]any
	if err := json.Unmarshal([]byte(token.SiteID), &siteData); err != nil {
		return nil, err
	}

	requestBody, _ := baseBody(token)
	requestBody["store_id"] = cast.ToInt(siteData["restaurant_id"])

	data, err := utils.DoRequest("POST", "https://gw.be.com.vn/api/v1/be-merchant-gateway/v2/merchant/store/get", baseHeaders(), requestBody)
	if err != nil {
		return nil, err
	}

	// Parse the store data
	storeData := gjson.ParseBytes(data).Get("store")
	if !storeData.Exists() {
		return nil, fmt.Errorf("store data not found")
	}

	// Extract store details
	return &models.StoreDetail{
		ID:      storeData.Get("store_id").String(),
		Name:    storeData.Get("name").String(),
		Phone:   strings.ReplaceAll(storeData.Get("phone_no").String(), "+84", "0"),
		Address: storeData.Get("address").String(),
		Raw:     storeData.Value(),
	}, nil
}

// GetStoreList retrieves the list of stores for a merchant
func (c BEMerchantClient) GetStoreList(token *models.Token) ([]any, error) {
	if token.AccessToken == "" {
		return nil, nil
	}

	// Get user profiles to get store list
	requestBody := map[string]any{
		"access_token": token.AccessToken,
	}

	// If we have site data, extract user_id
	if token.SiteID != "" {
		var siteData map[string]any
		if err := json.Unmarshal([]byte(token.SiteID), &siteData); err == nil && siteData["user_id"] != nil {
			requestBody["user_id"] = siteData["user_id"]
		}
	}

	data, err := utils.DoRequest("POST", "https://gw.be.com.vn/api/v1/be-merchant-gateway/v2/merchant/get_user_profiles", baseHeaders(), requestBody)
	if err != nil {
		return nil, err
	}

	// Extract store profiles
	storeProfiles := gjson.ParseBytes(data).Get("data.0.store_profiles").Array()
	if len(storeProfiles) == 0 {
		return []any{}, nil
	}

	// Convert to array of any
	result := make([]any, len(storeProfiles))
	for i, store := range storeProfiles {
		result[i] = store.Value()
	}

	return result, nil
}

// GetOrderListV2 retrieves the list of orders
func (c BEMerchantClient) GetOrderListV2(token *models.Token) (map[string][]models.MerchantOrder, error) {
	result := map[string][]models.MerchantOrder{
		"PENDING": {},
		"DOING":   {},
		"FINISH":  {},
		"CANCEL":  {},
	}

	if token.AccessToken == "" || token.SiteID == "" || skipCallAPI() {
		return result, nil
	}

	baseBody, err := baseBody(token)
	if err != nil {
		return result, err
	}

	// Define filter for different order statuses
	filterData := map[string]map[string]any{
		"PENDING": {
			"fetch_pending_orders": 1,
		},
		"DOING": {
			"fetch_pending_orders": 0,
		},
		"FINISH_OR_CANCEL": {
			"fetch_previous_orders": 1,
			"start_index":           0,
			"page_size":             100,
			"start_date":            carbon.Now().Format("Y-m-d"),
			"end_date":              carbon.Now().Format("Y-m-d"),
		},
	}

	cancelStatus := []int{3, 33, 9, 10, 17, 21, 25}

	for status, filter := range filterData {
		requestBody := make(map[string]any)
		maps.Copy(requestBody, baseBody)
		maps.Copy(requestBody, filter)

		data, err := utils.DoRequest("POST", "https://gw.be.com.vn/api/v1/be-marketplace/restaurant_orders", baseHeaders(), requestBody)
		if err != nil {
			return result, err
		}

		orders := gjson.ParseBytes(data).Get("order_info").Array()

		if status == "FINISH_OR_CANCEL" {
			for _, order := range orders {
				createdAt := order.Get("created_at").String()
				// Check if order was created today
				if !carbon.Parse(createdAt).IsToday() {
					continue
				}

				orderStatus := order.Get("order_status").Int()
				orderObj := models.MerchantOrder{
					LongOrderID:  order.Get("order_id").String(),
					ShortOrderID: order.Get("order_id").String(),
					DataInList:   order.Value(),
				}

				isCancel := false
				for _, statusCode := range cancelStatus {
					if orderStatus == int64(statusCode) {
						isCancel = true
						break
					}
				}

				if isCancel {
					result["CANCEL"] = append(result["CANCEL"], orderObj)
				} else {
					result["FINISH"] = append(result["FINISH"], orderObj)
				}
			}
		} else {
			for _, order := range orders {
				result[status] = append(result[status], models.MerchantOrder{
					LongOrderID:  order.Get("order_id").String(),
					ShortOrderID: order.Get("order_id").String(),
					DataInList:   order.Value(),
				})
			}
		}
	}

	return result, nil
}

// GetOrderListByDuration retrieves orders within a specific time range
func (c BEMerchantClient) GetOrderListByDuration(token *models.Token, startTime, endTime time.Time) (map[string][]models.MerchantOrder, error) {
	result := map[string][]models.MerchantOrder{
		"FINISH": {},
		"CANCEL": {},
	}

	if token.AccessToken == "" || token.SiteID == "" || skipCallAPI() {
		return result, nil
	}

	baseBody, err := baseBody(token)
	if err != nil {
		return result, err
	}

	requestBody := make(map[string]any)
	maps.Copy(requestBody, baseBody)

	// Add filter parameters
	requestBody["fetch_previous_orders"] = 1
	requestBody["start_index"] = 0
	requestBody["page_size"] = 200
	requestBody["start_date"] = startTime.Format("2006-01-02")
	requestBody["end_date"] = endTime.Format("2006-01-02")

	data, err := utils.DoRequest("POST", "https://gw.be.com.vn/api/v1/be-marketplace/restaurant_orders", baseHeaders(), requestBody)
	if err != nil {
		return result, err
	}

	cancelStatus := []int{3, 33, 9, 10, 17, 21, 25}
	orders := gjson.ParseBytes(data).Get("order_info").Array()

	for _, order := range orders {
		orderStatus := order.Get("order_status").Int()
		orderObj := models.MerchantOrder{
			LongOrderID:  order.Get("order_id").String(),
			ShortOrderID: order.Get("order_id").String(),
			DataInList:   order.Value(),
		}

		isCancel := false
		for _, statusCode := range cancelStatus {
			if orderStatus == int64(statusCode) {
				isCancel = true
				break
			}
		}

		if isCancel {
			result["CANCEL"] = append(result["CANCEL"], orderObj)
		} else {
			result["FINISH"] = append(result["FINISH"], orderObj)
		}
	}

	return result, nil
}

// ConfirmOrder confirms an order
func (c BEMerchantClient) ConfirmOrder(token *models.Token, orderID string) error {
	if token.AccessToken == "" || token.SiteID == "" || skipCallAPI() {
		return nil
	}

	baseBody, err := baseBody(token)
	if err != nil {
		return err
	}

	requestBody := make(map[string]any)
	maps.Copy(requestBody, baseBody)

	requestBody["order_id"] = cast.ToInt64(orderID)

	_, err = utils.DoRequest("POST", "https://gw.be.com.vn/api/v1/be-marketplace/process_order", baseHeaders(), requestBody)
	if err != nil {
		return err
	}

	return nil
}

// CancelOrder cancels an order
func (c BEMerchantClient) CancelOrder(token *models.Token, orderID string, cancelType string) error {
	if token.AccessToken == "" || token.SiteID == "" || skipCallAPI() {
		return nil
	}

	baseBody, err := baseBody(token)
	if err != nil {
		return err
	}

	requestBody := make(map[string]any)
	maps.Copy(requestBody, baseBody)

	// Parse order ID to number (BE API expects numeric order ID)
	orderIDNum, err := strconv.ParseInt(orderID, 10, 64)
	if err != nil {
		return fmt.Errorf("invalid order ID: %s", orderID)
	}
	requestBody["order_id"] = orderIDNum

	// Set cancel reason based on type
	if cancelType == "out_stock" {
		requestBody["reason"] = "Hết món yêu cầu"
	} else {
		requestBody["reason"] = "Quán quá tải"
	}

	data, err := utils.DoRequest("POST", "https://gw.be.com.vn/api/v1/be-marketplace/cancel_order", baseHeaders(), requestBody)
	if err != nil {
		return err
	}

	success := gjson.ParseBytes(data).Get("message").String() == "success"
	if !success {
		return fmt.Errorf("failed to cancel order")
	}

	return nil
}

// GetOrderDetail retrieves details of a specific order
func (c BEMerchantClient) GetOrderDetail(token *models.Token, orderID string) (any, error) {
	if token.AccessToken == "" || token.SiteID == "" || skipCallAPI() {
		return nil, nil
	}

	baseBody, err := baseBody(token)
	if err != nil {
		return nil, err
	}

	requestBody := make(map[string]any)
	maps.Copy(requestBody, baseBody)

	// Parse order ID to number (BE API expects numeric order ID)
	orderIDNum, err := strconv.ParseInt(orderID, 10, 64)
	if err != nil {
		return nil, fmt.Errorf("invalid order ID: %s", orderID)
	}
	requestBody["order_id"] = orderIDNum

	data, err := utils.DoRequest("POST", "https://gw.be.com.vn/api/v1/be-marketplace/restaurant_orders", baseHeaders(), requestBody)
	if err != nil {
		return nil, err
	}

	orderData := gjson.ParseBytes(data).Get("order_info.0")
	if !orderData.Exists() {
		return nil, fmt.Errorf("order data not found")
	}

	return orderData.Value(), nil
}

// UpdateStoreStatus updates the store's open/close status
func (c BEMerchantClient) UpdateStoreStatus(token *models.Token, status string, duration int) error {
	if token.AccessToken == "" || token.SiteID == "" || skipCallAPI() {
		return nil
	}

	baseBody, err := baseBody(token)
	if err != nil {
		return err
	}

	requestBody := make(map[string]any)
	maps.Copy(requestBody, baseBody)

	// Set order mode (1 = open, 2 = close)
	if status == "close" {
		requestBody["order_mode"] = 2
	} else {
		requestBody["order_mode"] = 1
	}

	_, err = utils.DoRequest("POST", "https://gw.be.com.vn/api/v1/be-marketplace/vendor/update_restaurant_profile", baseHeaders(), requestBody)
	if err != nil {
		return err
	}

	return nil
}

// GetOpenStatus checks if the store is currently open
func (c BEMerchantClient) GetOpenStatus(token *models.Token) (bool, error) {
	if token.AccessToken == "" || token.SiteID == "" || skipCallAPI() {
		return false, nil
	}

	baseBody, err := baseBody(token)
	if err != nil {
		return false, err
	}

	data, err := utils.DoRequest("POST", "https://gw.be.com.vn/api/v1/be-merchant-gateway/v2/merchant/get_user_profiles", baseHeaders(), baseBody)
	if err != nil {
		return false, err
	}

	// Check if store is open (order_mode = 1)
	orderMode := gjson.ParseBytes(data).Get("data.0.store_profiles.0.order_mode").Int()
	return orderMode == 1, nil
}

// GetOpeningHour retrieves the store's opening hours
func (c BEMerchantClient) GetOpeningHour(token *models.Token) (any, error) {
	if token.AccessToken == "" || token.SiteID == "" || skipCallAPI() {
		return nil, nil
	}

	baseBody, err := baseBody(token)
	if err != nil {
		return nil, err
	}

	requestBody := make(map[string]any)
	maps.Copy(requestBody, baseBody)

	data, err := utils.DoRequest("POST", "https://gw.be.com.vn/api/v1/be-marketplace/vendor/restaurant_timing/get", baseHeaders(), requestBody)
	if err != nil {
		return nil, err
	}

	timings := gjson.ParseBytes(data).Get("timings")
	if !timings.Exists() {
		return nil, fmt.Errorf("timings not found")
	}

	return timings.Value(), nil
}

// UpdateOpeningHour updates the store's opening hours
func (c BEMerchantClient) UpdateOpeningHour(token *models.Token, workingHours any) error {
	if token.AccessToken == "" || token.SiteID == "" || skipCallAPI() {
		return nil
	}

	// First get the current opening hours to preserve time_id values
	oldTimings, err := c.GetOpeningHour(token)
	if err != nil {
		return err
	}

	// Convert to proper types for processing
	oldTimingsSlice, ok := oldTimings.([]any)
	if !ok {
		return fmt.Errorf("invalid old timings format")
	}

	workingHoursSlice, ok := workingHours.([]any)
	if !ok {
		return fmt.Errorf("invalid working hours format")
	}

	// Update time_id in new working hours based on day_id match with old timings
	for _, newHour := range workingHoursSlice {
		newHourMap, ok := newHour.(map[string]any)
		if !ok {
			continue
		}

		dayID, ok := newHourMap["day_id"]
		if !ok {
			continue
		}

		// Find matching old timing
		for _, oldTiming := range oldTimingsSlice {
			oldTimingMap, ok := oldTiming.(map[string]any)
			if !ok {
				continue
			}

			oldDayID, ok := oldTimingMap["day_id"]
			if !ok || oldDayID != dayID {
				continue
			}

			// Check if both have timings arrays
			newTimings, newHasTimings := newHourMap["timings"].([]any)
			oldTimings, oldHasTimings := oldTimingMap["timings"].([]any)

			if newHasTimings && len(newTimings) > 0 && oldHasTimings && len(oldTimings) > 0 {
				// Copy time_id from old to new
				newTimingMap, ok := newTimings[0].(map[string]any)
				if !ok {
					continue
				}

				oldTimingMap, ok := oldTimings[0].(map[string]any)
				if !ok {
					continue
				}

				if timeID, exists := oldTimingMap["time_id"]; exists {
					newTimingMap["time_id"] = timeID
				}
			}
		}
	}

	// Prepare the request body
	baseBody, err := baseBody(token)
	if err != nil {
		return err
	}

	requestBody := make(map[string]any)
	maps.Copy(requestBody, baseBody)

	// Convert working hours to JSON string
	workingHoursJSON, err := json.Marshal(workingHours)
	if err != nil {
		return err
	}
	requestBody["timings"] = string(workingHoursJSON)

	// Make the request
	data, err := utils.DoRequest("POST", "https://gw.be.com.vn/api/v1/be-marketplace/vendor/restaurant_timing/update", baseHeaders(), requestBody)
	if err != nil {
		return err
	}

	// Check response
	success := gjson.ParseBytes(data).Get("message").String() == "success"
	if !success {
		return fmt.Errorf("failed to update opening hours")
	}

	return nil
}

// GetOrderFeedbacks retrieves order feedbacks from BE
func (c BEMerchantClient) GetOrderFeedbacks(token *models.Token, limit int) ([]any, error) {
	if token.AccessToken == "" || token.SiteID == "" || skipCallAPI() {
		return []any{}, nil
	}

	baseBody, err := baseBody(token)
	if err != nil {
		return nil, err
	}

	requestBody := make(map[string]any)
	maps.Copy(requestBody, baseBody)
	requestBody["page"] = 1
	requestBody["limit"] = 100

	data, err := utils.DoRequest("POST", "https://gw.be.com.vn/api/v1/be-merchant-gateway/v2/merchant/restaurant/ratings", baseHeaders(), requestBody)
	if err != nil {
		return nil, err
	}

	// Parse ratings
	ratings := gjson.ParseBytes(data).Get("ratings")
	if !ratings.Exists() || !ratings.IsArray() {
		return []any{}, nil
	}

	var result []any
	for _, rating := range ratings.Array() {
		result = append(result, rating.Value())
		if len(result) >= limit {
			break
		}
	}

	return result, nil
}
