package rabbitmq

import (
	"context"
	"log"
	"os"
	"runtime/debug"
	"time"

	"github.com/ThreeDotsLabs/watermill"
	"github.com/ThreeDotsLabs/watermill-amqp/v2/pkg/amqp"
	"github.com/ThreeDotsLabs/watermill/message"
	"github.com/samber/lo"
)

var NODE_ENV = lo.If(os.Getenv("NODE_ENV") == "prod", "prod").Else("dev")

// RabbitClient represents a RabbitMQ client with pub-sub capabilities
type RabbitClient struct {
	URI        string
	Publisher  message.Publisher
	Subscriber *amqp.Subscriber
	Logger     watermill.LoggerAdapter
}

// NewRabbitClient creates a new RabbitMQ client with the provided URI
func NewRabbitClient(uri string) (*RabbitClient, error) {
	logger := watermill.NewStdLogger(false, false)

	publisher, err := amqp.NewPublisher(
		amqp.NewDurablePubSubConfig(uri, nil),
		logger,
	)
	if err != nil {
		return nil, err
	}

	return &RabbitClient{
		URI:       uri,
		Publisher: publisher,
		Logger:    logger,
	}, nil
}

// Subscribe creates a subscriber and subscribes to a topic in one operation
// Returns a channel of messages from the specified topic
func (c *RabbitClient) Subscribe(topic string, consumerGroup string) (<-chan *message.Message, error) {
	ctx := context.Background()
	subscriber, err := amqp.NewSubscriber(
		amqp.NewDurablePubSubConfig(
			c.URI,
			amqp.GenerateQueueNameTopicNameWithSuffix(consumerGroup),
		),
		c.Logger,
	)
	if err != nil {
		return nil, err
	}

	c.Subscriber = subscriber
	return subscriber.Subscribe(ctx, NODE_ENV+"_"+topic)
}

// Publish sends a message to the specified topic
func (c *RabbitClient) Publish(topic string, payload []byte) error {
	msg := message.NewMessage(watermill.NewUUID(), payload)
	return c.Publisher.Publish(NODE_ENV+"_"+topic, msg)
}

// ProcessMessages processes messages from a channel with a handler function
func ProcessMessages(messages <-chan *message.Message, handler func(*message.Message)) {
	for msg := range messages {
		handler(msg)
		msg.Ack()
	}
}

// ProcessMessagesWithRecovery processes messages with panic recovery and error handling
func ProcessMessagesWithRecovery(messages <-chan *message.Message, topic string, handler func(*message.Message)) {
	for msg := range messages {
		func() {
			defer func() {
				if r := recover(); r != nil {
					log.Printf("PANIC processing message for topic %s: %v\nMessage: %s\nStack trace:\n%s",
						topic, r, string(msg.Payload), debug.Stack())
					// Nack the message so it can be retried
					msg.Nack()
					return
				}
			}()

			// Process the message
			handler(msg)
			// Ack the message only if processing was successful
			msg.Ack()
		}()
	}
}

// SubscribeWithRecovery subscribes to a topic with automatic recovery and reconnection
func (c *RabbitClient) SubscribeWithRecovery(topic string, consumerGroup string, handler func(*message.Message)) {
	go func() {
		defer func() {
			if r := recover(); r != nil {
				log.Printf("PANIC in subscription for topic %s: %v\nStack trace:\n%s", topic, r, debug.Stack())
				// Restart subscription after a delay
				time.Sleep(10 * time.Second)
				log.Printf("Restarting subscription for topic: %s", topic)
				c.SubscribeWithRecovery(topic, consumerGroup, handler)
			}
		}()

		for {
			messages, err := c.Subscribe(topic, consumerGroup)
			if err != nil {
				log.Printf("Error subscribing to %s: %v. Retrying in 30 seconds...", topic, err)
				time.Sleep(30 * time.Second)
				continue
			}

			log.Printf("Successfully subscribed to topic: %s", topic)

			// Process messages with recovery
			ProcessMessagesWithRecovery(messages, topic, handler)

			// If we reach here, the subscription was closed
			log.Printf("Subscription to %s was closed. Reconnecting in 5 seconds...", topic)
			time.Sleep(5 * time.Second)
		}
	}()
}

// Close closes the client connections
func (c *RabbitClient) Close() error {
	if c.Publisher != nil {
		if err := c.Publisher.Close(); err != nil {
			return err
		}
	}

	if c.Subscriber != nil {
		if err := c.Subscriber.Close(); err != nil {
			return err
		}
	}

	return nil
}
