package route

import (
	"encoding/json"
	"fmt"
	"net/http"
	"os"
	"regexp"
	"time"

	"github.com/nexdorvn/nexpos-backend/golang-services/nexdorpay-service/sdk"
	"github.com/nexdorvn/nexpos-backend/golang-services/nexdorpay-service/sdk/google"
	"github.com/nexdorvn/nexpos-backend/golang-services/nexdorpay-service/sdk/myredis"
	"github.com/nexdorvn/nexpos-backend/golang-services/nexdorpay-service/sdk/utils"
	"github.com/nexdorvn/nexpos-backend/golang-services/nexdorpay-service/sdk/vietqr"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/middlewares"

	"github.com/go-resty/resty/v2"
	"github.com/spf13/cast"

	"github.com/gin-gonic/gin"
)

var mbBankService = sdk.NewMBBankService()

func GetTransactionList(c *gin.Context) {
	bankTxns, err := mbBankService.GetTransactionList()
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
	}

	redisClient := middlewares.GetRedis(c)

	queueBankTransactionKey := "bank_transactions"
	// oldTransactionList := myredis.Get(redisClient, queueBankTransactionKey)
	// if oldTransactionList == utils.StructToJSON(bankTxns).Raw {
	// 	c.JSON(http.StatusOK, gin.H{
	// 		"success": true,
	// 		"data":    bankTxns,
	// 	})
	// 	return
	// }

	for _, bankTxn := range bankTxns {
		updatedTransaction := map[string]any{
			"transaction_id":         bankTxn.PartnerTransactionID,
			"partner_transaction_id": bankTxn.TransactionID,
			"callback_data":          bankTxn,
		}

		// Default server callback URL
		serverCallback := fmt.Sprintf("%s/api/nexdorpay/callbacks", os.Getenv("API_BASE"))

		if bankTxn.PartnerTransactionID != "" {
			redisData := myredis.Get(redisClient, "transactions:"+bankTxn.PartnerTransactionID)
			if redisData != "" {
				redisUpdateData := cast.ToStringMap(redisData)
				redisUpdateData["status"] = "COMPLETED"
				redisUpdateData["callback_data"] = bankTxn
				myredis.Set(redisClient, "transactions:"+bankTxn.PartnerTransactionID, utils.StructToJSON(redisUpdateData).Raw)

				// Get server callback from transaction data if it exists
				if serverCallbackVal, exists := redisUpdateData["server_callback"]; exists && serverCallbackVal != "" {
					serverCallback = fmt.Sprintf("%v", serverCallbackVal)
				}
			}

			updatedTransaction["status"] = "COMPLETED"
		} else {
			updatedTransaction["status"] = "UNSETTLED"
		}

		resp, err := resty.New().R().
			SetAuthToken("NexDor Token").
			SetBody(updatedTransaction).
			Post(serverCallback)

		fmt.Println("Callback response", resp.String(), err)

	}

	if len(bankTxns) > 0 {
		myredis.Set(redisClient, queueBankTransactionKey, utils.StructToJSON(bankTxns).Raw)
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    bankTxns,
	})
}

type Transaction struct {
	Amount         int             `json:"amount"`
	OrderID        string          `json:"order_id"`
	TransactionID  string          `json:"transaction_id"`
	ClientCallback string          `json:"client_callback"`
	ServerCallback string          `json:"server_callback"`
	QRCode         string          `json:"qrcode"`
	Status         string          `json:"status"`
	Signature      string          `json:"signature"`
	ExpiredAt      *time.Time      `json:"expired_at"`
	CreatedAt      time.Time       `json:"created_at"`
	CallbackData   json.RawMessage `json:"callback_data"`
}

func CreateTransaction(c *gin.Context) {
	var req Transaction
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}

	message := fmt.Sprintf("NexDor %s Don Hang %s", req.TransactionID, regexp.MustCompile(`[^A-Za-z0-9]+`).ReplaceAllString(req.OrderID, ""))
	// if request.Signature != helper.HashWithSecret(message, "NexDorPay") {
	// 	c.JSON(http.StatusOK, gin.H{
	// 		"success": false,
	// 		"message": "Invalid signature",
	// 	})
	// 	return
	// }
	qrCodeBuff, err := vietqr.GenerateViQR(message, req.Amount)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	qrcodeFile, err := google.UploadFile("nexpos-files", fmt.Sprintf("transactions/%s.png", req.TransactionID), qrCodeBuff)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}

	redisClient := middlewares.GetRedis(c)

	expired := time.Now().Add(10 * time.Minute)
	req.ExpiredAt = &expired
	req.CreatedAt = time.Now()
	req.Status = "PENDING"
	req.QRCode = qrcodeFile

	if err := myredis.Set(redisClient, "transactions:"+req.TransactionID, utils.StructToJSON(req).Raw); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}

	if err := myredis.Set(redisClient, "transactions:latest", utils.StructToJSON(req).Raw); err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"qrcode":  qrcodeFile,
			"pay_url": fmt.Sprintf("https://pay.nexdor.tech?tx=%s", req.TransactionID),
		},
	})
}

func GetTransaction(c *gin.Context) {
	transactionID := c.Param("transaction_id")

	redisClient := middlewares.GetRedis(c)
	transactionData, err := redisClient.Get(c.Request.Context(), "transactions:"+transactionID).Result()
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": err.Error(),
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    cast.ToStringMap(transactionData),
	})
}
