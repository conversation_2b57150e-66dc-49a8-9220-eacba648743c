#!/bin/bash

# Test script for Working Shift History APIs
# Make sure to replace the variables below with actual values

# Configuration
BASE_URL="http://localhost:3000/v1/brand-service"
HUB_ID="your_hub_id_here"
SHIFT_ID="your_shift_id_here"
ACCESS_TOKEN="your_access_token_here"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Testing Working Shift History APIs${NC}"
echo "=================================="

# Function to make API calls
make_request() {
    local method=$1
    local url=$2
    local description=$3
    
    echo -e "\n${YELLOW}Testing: $description${NC}"
    echo "URL: $method $url"
    echo "Response:"
    
    response=$(curl -s -X $method "$url" \
        -H "Authorization: Bearer $ACCESS_TOKEN" \
        -H "Content-Type: application/json" \
        -w "\nHTTP_STATUS:%{http_code}")
    
    # Extract HTTP status
    http_status=$(echo "$response" | grep "HTTP_STATUS:" | cut -d: -f2)
    response_body=$(echo "$response" | sed '/HTTP_STATUS:/d')
    
    # Pretty print JSON if possible
    if command -v jq &> /dev/null; then
        echo "$response_body" | jq .
    else
        echo "$response_body"
    fi
    
    # Check status
    if [[ $http_status -eq 200 ]]; then
        echo -e "${GREEN}✓ Success (HTTP $http_status)${NC}"
    else
        echo -e "${RED}✗ Failed (HTTP $http_status)${NC}"
    fi
}

# Test 1: Get Working Shift History (basic)
make_request "GET" \
    "$BASE_URL/hubs/$HUB_ID/working-shifts/history" \
    "Get Working Shift History (basic)"

# Test 2: Get Working Shift History with pagination
make_request "GET" \
    "$BASE_URL/hubs/$HUB_ID/working-shifts/history?page=1&limit=5" \
    "Get Working Shift History with pagination"

# Test 3: Get Working Shift History with status filter
make_request "GET" \
    "$BASE_URL/hubs/$HUB_ID/working-shifts/history?status=closed" \
    "Get Working Shift History (closed shifts only)"

# Test 4: Get Working Shift History with date range
START_DATE=$(date -d "7 days ago" +%Y-%m-%d)
END_DATE=$(date +%Y-%m-%d)
make_request "GET" \
    "$BASE_URL/hubs/$HUB_ID/working-shifts/history?start_date=$START_DATE&end_date=$END_DATE" \
    "Get Working Shift History (last 7 days)"

# Test 5: Get Working Shift History with search
make_request "GET" \
    "$BASE_URL/hubs/$HUB_ID/working-shifts/history?search=admin" \
    "Get Working Shift History (search by user name)"

# Test 6: Get Working Shift History Detail
make_request "GET" \
    "$BASE_URL/hubs/$HUB_ID/working-shifts/$SHIFT_ID/history" \
    "Get Working Shift History Detail"

# Test 7: Get Working Shift Statistics (daily)
make_request "GET" \
    "$BASE_URL/hubs/$HUB_ID/working-shifts/statistics?start_date=$START_DATE&end_date=$END_DATE&group_by=day" \
    "Get Working Shift Statistics (daily grouping)"

# Test 8: Get Working Shift Statistics (weekly)
WEEK_START=$(date -d "4 weeks ago" +%Y-%m-%d)
make_request "GET" \
    "$BASE_URL/hubs/$HUB_ID/working-shifts/statistics?start_date=$WEEK_START&end_date=$END_DATE&group_by=week" \
    "Get Working Shift Statistics (weekly grouping)"

# Test 9: Get Working Shift Statistics (monthly)
MONTH_START=$(date -d "3 months ago" +%Y-%m-%d)
make_request "GET" \
    "$BASE_URL/hubs/$HUB_ID/working-shifts/statistics?start_date=$MONTH_START&end_date=$END_DATE&group_by=month" \
    "Get Working Shift Statistics (monthly grouping)"

echo -e "\n${YELLOW}Testing completed!${NC}"
echo -e "${YELLOW}Note: Make sure to update the configuration variables at the top of this script with actual values.${NC}"
