
/**
 * TaskPool - A utility class for processing tasks with controlled concurrency
 */
class TaskPool {
    /**
     * Create a new TaskPool
     * @param {Array} tasks - Array of tasks to process
     * @param {number} concurrency - Maximum number of tasks to process concurrently
     */
    constructor(tasks, concurrency) {
        this.tasks = tasks;
        this.concurrency = concurrency;
        this.running = 0;
        this.completed = 0;
        this.errors = [];
        this.results = [];
    }

    /**
     * Run all tasks with the specified concurrency
     * @returns {Promise<Array>} - Results of all tasks
     */
    async run() {
        return new Promise((resolve, reject) => {
            const runTask = async (task, index) => {
                this.running++;
                try {
                    const result = await task();
                    this.results[index] = result;
                } catch (error) {
                    this.errors.push({ index, error });
                    console.error(`Task ${index} failed:`, error);
                } finally {
                    this.running--;
                    this.completed++;

                    // Log progress every 10 tasks
                    if (this.completed % 10 === 0 || this.completed === this.tasks.length) {
                        console.log(`Progress: ${this.completed}/${this.tasks.length} tasks completed`);
                    }

                    next();
                }
            };

            const next = () => {
                if (this.taskIndex >= this.tasks.length && this.running === 0) {
                    // All tasks have been processed
                    resolve(this.results);
                    return;
                }

                // Start more tasks if we haven't reached concurrency limit
                while (this.running < this.concurrency && this.taskIndex < this.tasks.length) {
                    runTask(this.tasks[this.taskIndex], this.taskIndex);
                    this.taskIndex++;
                }
            };

            this.taskIndex = 0;
            next();
        });
    }
}

module.exports = {
    TaskPool
}