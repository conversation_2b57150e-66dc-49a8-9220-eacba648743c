# NexPOS - Production Deployment Guide

[![CI/CD Status](https://img.shields.io/badge/CI%2FCD-Active-green)](https://circleci.com/gh/nexdor-tech/nexpos-backend)
[![Production](https://img.shields.io/badge/Production-Stable-blue)](https://nexpos.nexdor.com)
[![Staging](https://img.shields.io/badge/Staging-Available-yellow)](https://nexpos-stag.nexdor.com)

## 🚀 Overview

NexPOS is a sophisticated Point-of-Sale and restaurant management platform with a hybrid microservices architecture. This guide provides comprehensive instructions for deploying to production environments.

### Architecture
- **Microservices**: Go (Gin framework) + Node.js (Express.js)
- **Databases**: PostgreSQL (primary), MongoDB (legacy), Redis (caching)
- **Infrastructure**: Docker + Kubernetes on Google Cloud Platform
- **CI/CD**: CircleCI + GitHub Actions for automated deployments

## 📋 Prerequisites

### Required Tools
```bash
# Install required tools
kubectl         # Kubernetes CLI
gcloud          # Google Cloud SDK
docker          # Container runtime
make            # Build automation
git             # Version control
```

### Access Requirements
- **Google Cloud Project**: `friendly-idea-384714`
- **Kubernetes Clusters**:
  - Production: `cluster-nexdor` (asia-southeast1-a)
  - Staging: `cluster-nexdor-stag` (asia-southeast1-a)
- **Container Registry**: `asia-southeast1-docker.pkg.dev/friendly-idea-384714/nexpos`
- **CircleCI**: Configured with GCP credentials
- **GitHub**: Repository access with deployment permissions

## 🌍 Environment Overview

| Environment | Branch | Cluster | URL | Auto-Deploy |
|-------------|--------|---------|-----|-------------|
| **Production** | `master` | `cluster-nexdor` | nexpos.nexdor.com | ✅ GitHub Actions |
| **Staging** | `stag` | `cluster-nexdor` | nexpos-stag.nexdor.com | ✅ GitHub Actions |
| **UAT** | `uat` | `cluster-nexdor-stag` | nexpos-uat.nexdor.com | ✅ GitHub Actions |
| **Development** | `dev` | `cluster-nexdor` | nexpos-dev.nexdor.com | ✅ GitHub Actions |

## 🔄 Automated Deployment (Recommended)

### 1. Branch-Based Deployment

**Main Application Deployment:**
```bash
# Deploy to Development
git push origin dev

# Deploy to Staging
git push origin stag

# Deploy to UAT
git push origin uat

# Deploy to Production
git push origin master
```

**Service-Specific Deployment:**
```bash
# Deploy specific service to development
git checkout -b deployment/brand-service-dev
git push origin deployment/brand-service-dev

# Deploy specific service to production
git checkout -b deployment/brand-service-prod
git push origin deployment/brand-service-prod
```

### 2. Supported Services

Available services for individual deployment:
- `brand-service` - Brand and site management
- `order-service` - Order processing and management
- `user-service` - User authentication and management
- `merchant-service` - Third-party merchant integrations
- `notification-service` - Push notifications and alerts
- `email-service` - Email communications
- `cron-service` - Scheduled tasks and jobs
- `nexdorpay-service` - Payment processing
- `zalo-mini-app-service` - Zalo integration

## ⚙️ Manual Deployment

### 1. Setup Google Cloud Authentication

```bash
# Authenticate with Google Cloud
gcloud auth login

# Set project
gcloud config set project friendly-idea-384714

# Configure Docker for GCR
gcloud auth configure-docker asia-southeast1-docker.pkg.dev

# Get cluster credentials
make prod-profile  # For production
make uat-profile   # For UAT/staging
```

### 2. Deploy Main Application

```bash
# Deploy to specific environments
make deploy-prod    # Production deployment
make deploy-stag    # Staging deployment
make deploy-uat     # UAT deployment
make deploy-dev     # Development deployment
```

### 3. Deploy Individual Services

```bash
# Build and deploy specific services
make deploy-brand-image      # Deploy brand service to production
make deploy-order-image      # Deploy order service to production
make deploy-user-image       # Deploy user service to production

# Deploy to development environment
make deploy-brand-image-dev  # Deploy brand service to development
make deploy-order-image-dev  # Deploy order service to development
```

### 4. Build Only (No Deployment)

```bash
# Build and push images without deployment
make build-brand-image
make build-order-image
make build-user-image
```

## 🗄️ Database Operations

### Production to Development Sync
```bash
# Sync production database to development
make dump_prod_to_dev
```

### Odoo Module Deployment
```bash
# Deploy Odoo modules
make deploy-odoo-prod    # Production Odoo
make deploy-odoo-dev     # Development Odoo
```

## 🔧 Advanced Operations

### Kubernetes Context Management
```bash
# Switch between clusters
make prod-profile    # Switch to production cluster
make uat-profile     # Switch to UAT cluster

# Verify current context
kubectl config current-context

# View cluster info
kubectl cluster-info
```

### Manual Kubernetes Operations
```bash
# View deployments
kubectl get deployments

# View pods
kubectl get pods

# View services
kubectl get services

# Check logs
kubectl logs -f deployment/nexpos-service

# Restart deployment
kubectl rollout restart deployment/nexpos-service
```

## 🚨 Troubleshooting

### Common Issues

**1. Authentication Errors**
```bash
# Re-authenticate with Google Cloud
gcloud auth login
gcloud auth configure-docker asia-southeast1-docker.pkg.dev
```

**2. Cluster Access Issues**
```bash
# Refresh cluster credentials
gcloud container clusters get-credentials cluster-nexdor --zone asia-southeast1-a --project friendly-idea-384714
```

**3. Image Push Failures**
```bash
# Check Docker daemon
docker info

# Verify registry access
docker pull asia-southeast1-docker.pkg.dev/friendly-idea-384714/nexpos/nexpos:latest
```

**4. Deployment Failures**
```bash
# Check deployment status
kubectl describe deployment nexpos-service

# View recent events
kubectl get events --sort-by=.metadata.creationTimestamp

# Check pod logs
kubectl logs -l app=nexpos-service --tail=100
```

### Service Health Checks
```bash
# Check service endpoints
kubectl get endpoints

# Test service connectivity
kubectl port-forward service/nexpos-service 8080:80

# Check ingress status
kubectl get ingress
```

## 🔒 Security Considerations

### Secrets Management
- **Google Cloud Credentials**: Stored in CircleCI/GitHub Secrets
- **Database Credentials**: Managed via Kubernetes secrets
- **API Keys**: Environment-specific configuration

### Access Control
- **RBAC**: Kubernetes role-based access control enabled
- **Network Policies**: Pod-to-pod communication restrictions
- **Image Security**: Regular vulnerability scanning

## 📊 Monitoring & Logging

### Application Monitoring
```bash
# View application logs
kubectl logs -f deployment/nexpos-service

# Monitor resource usage
kubectl top pods
kubectl top nodes

# Check service status
kubectl get pods -o wide
```

### Performance Monitoring
- **Metrics**: Prometheus + Grafana dashboards
- **Logging**: Centralized logging via Google Cloud Logging
- **Alerts**: Automated alerting for critical issues

## 🔄 CI/CD Pipeline Details

### GitHub Actions Workflows
- **`.github/workflows/deploy-prod.yml`** - Production deployment (master branch)
- **`.github/workflows/deploy-stag.yml`** - Staging deployment (stag branch)
- **`.github/workflows/deploy-uat.yml`** - UAT deployment (uat branch)
- **`.github/workflows/deploy-dev.yml`** - Development deployment (dev branch)
- **`.github/workflows/deploy-any-service-*.yml`** - Service-specific deployments

### CircleCI Integration
- **Branch Pattern**: `deployment/*-service-dev` and `deployment/*-service-prod`
- **Automatic Builds**: Triggered on branch push
- **Service Extraction**: Automatic service name detection from branch name

## 📁 Project Structure

```
nexpos-backend/
├── .circleci/              # CircleCI configuration
├── .docker/                # Docker files for all services
├── .github/workflows/      # GitHub Actions workflows
├── .k8s/                   # Kubernetes deployment manifests
├── golang-services/        # Go microservices
│   ├── brand-service/
│   ├── order-service/
│   ├── user-service/
│   └── ...
├── nodejs-services/        # Node.js services
│   ├── gateway-service/
│   ├── nexpos-service/
│   └── ...
├── scripts/                # Deployment and utility scripts
├── Makefile               # Build and deployment commands
└── README.md              # This deployment guide
```

## 🚀 Quick Start for New Team Members

### 1. Initial Setup
```bash
# Clone repository
git clone https://github.com/nexdor-tech/nexpos-backend.git
cd nexpos-backend

# Install dependencies
npm install  # For Node.js services
cd golang-services && go mod download  # For Go services
```

### 2. Local Development
```bash
# Start local development environment
docker-compose up -d  # If available

# Or run individual services
cd golang-services/brand-service && go run main.go
cd nodejs-services/nexpos-service && npm start
```

### 3. Deploy Your First Change
```bash
# Create feature branch
git checkout -b feature/your-feature

# Make changes and commit
git add .
git commit -m "Your changes"

# Deploy to development
git checkout dev
git merge feature/your-feature
git push origin dev  # Triggers automatic deployment
```

## 📞 Support & Contacts

### Emergency Contacts
- **DevOps Lead**: <EMAIL>
- **Technical Lead**: Available via Slack #devops-alerts
- **On-Call Engineer**: Check current rotation in PagerDuty

### Resources
- **Documentation**: Internal wiki and Confluence
- **Monitoring**: Grafana dashboards
- **Logs**: Google Cloud Console
- **Issues**: GitHub Issues and Jira

## 📝 Deployment Checklist

### Pre-Deployment
- [ ] Code review completed and approved
- [ ] Unit tests passing
- [ ] Integration tests passing
- [ ] Database migrations reviewed (if applicable)
- [ ] Environment variables updated (if needed)
- [ ] Security scan completed

### Post-Deployment
- [ ] Application health check passed
- [ ] Database connectivity verified
- [ ] External API integrations working
- [ ] Monitoring alerts configured
- [ ] Performance metrics within acceptable range
- [ ] Rollback plan confirmed

## 🔄 Rollback Procedures

### Automatic Rollback
```bash
# Rollback to previous deployment
kubectl rollout undo deployment/nexpos-service

# Rollback to specific revision
kubectl rollout undo deployment/nexpos-service --to-revision=2

# Check rollout status
kubectl rollout status deployment/nexpos-service
```

### Manual Rollback
```bash
# Deploy previous image tag
cd .k8s
sed -i 's/:current-tag/:previous-tag/g' deployment.nexpos.yaml
kubectl apply -f deployment.nexpos.yaml
```

---

## 📈 Continuous Improvement

This deployment guide is continuously updated based on team feedback and operational experience. For suggestions or improvements, please:

1. Create an issue in the repository
2. Submit a pull request with proposed changes
3. Discuss in the #devops Slack channel

**Last Updated**: December 2024
**Version**: 2.0
**Maintained by**: DevOps Team